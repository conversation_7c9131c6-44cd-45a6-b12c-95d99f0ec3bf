(function ($) {
    'use strict';
    var yayCurrencyAdmin = function () {
        var self = this;
        self.wooCommerceDashboardBlock = '#woocommerce_dashboard_status';
        self.sync_orders_button = '.yay-currency-analytics-fetch-button';
        self.loadingButtonClass = 'updating-message button';

        if (yayCurrency_Admin.woo_subscriptions) {
            self.signupRevenueArea = '.signup-revenue';
            self.renewalRevenueArea = '.renewal-revenue';
        }

        self.init = function () {

            // Compatible with WooCommerce Subscriptions
            if (yayCurrency_Admin.woo_subscriptions && $(self.wooCommerceDashboardBlock).length) {
                self.CompatibleWithWooCommerceSubscriptions();
            }
            // Compatible with Funnel Kit Automation
            if (yayCurrency_Admin.funnel_kit_automation) {
                self.CompatibleWithFunnelKitAutomation();
            }

            // Show Notice Sync Orders to Base Currency
            self.showNotice();

            // Sync Orders to Base currency
            $(document).on('click', self.sync_orders_button, function (event) {
                event.preventDefault();
                self.fetchAnalyticsButton();
            });

        };

        self.setOpacityArea = function (elementArea, type = 'before') {
            if (type === 'before') {
                $(elementArea).css('opacity', 0.2);
            } else {
                $(elementArea).css('opacity', 1);
            }
        }

        // Compatible with WooCommerce Subscriptions
        self.CompatibleWithWooCommerceSubscriptions = function (year) {

            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'yay_subscription_admin_reports_data',
                    _nonce: yayCurrency_Admin.nonce,
                },
                beforeSend: function (res) {
                    self.setOpacityArea(self.wooCommerceDashboardBlock);
                },
                success: function success(res) {
                    self.setOpacityArea(self.wooCommerceDashboardBlock, 'after');
                    $(self.signupRevenueArea).find('strong').html(res.data.signup_revenue);
                    $(self.renewalRevenueArea).find('strong').html(res.data.renewal_revenue);
                }
            });
        }

        // Compatible with Funnel Kit Automation
        self.CompatibleWithFunnelKitAutomation = function () {
            if ('yes' === yayCurrency_Admin.fkit_automation_customer_contact_purchase) {
                self.recalculateRevenueFnkAutomation();
            }
            $(document).on('click', yayCurrency_Admin.fkit_automation_bwf_purchase_tab, function (event) {
                const tab = $(this).data('tab');
                if ('purchase' == tab) {
                    self.recalculateRevenueFnkAutomation();
                }
            });
            if (yayCurrency_Admin.fkit_automation_menu_page) {
                $(document).on('click', yayCurrency_Admin.fkit_automation_menu_page, function (event) {
                    if ('admin.php?page=autonami&path=/contacts' === $(this).find('.current a').attr('href')) {
                        self.doConvertToDefaultSymbolCode();
                    }
                });
                if (yayCurrency_Admin.fkit_automation_customer_contact_page) {
                    self.doConvertToDefaultSymbolCode();
                }
            }
        }

        self.recalculateRevenueFnkAutomation = function () {
            var count = 0,
                flag = false,
                intervalTime = setInterval(function () {
                    const analytics_area = jQuery(yayCurrency_Admin.fkit_automation_bwf_analytics_area);
                    if (analytics_area.length) {
                        var orderIDs = $('.bwf-table-item:nth-child(2) a.bwf-a-no-underline');
                        orderIDs.each(function (index, item) {
                            var orderID = +$(this).text().replace('#', '');
                            $.ajax({
                                url: ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'yay_bwf_admin_recalculate_revenue',
                                    orderID: orderID,
                                    _nonce: yayCurrency_Admin.nonce,
                                },
                                beforeSend: function (res) {
                                    self.setOpacityArea(analytics_area);
                                },
                                success: function success(res) {
                                    self.setOpacityArea(analytics_area, 'after');
                                    if (res.data.fkit_revenue) {
                                        $(item).closest('tr').find('.bwf-table-item:last-child').html(res.data.fkit_revenue);
                                    }

                                }
                            });

                        });
                        flag = true;
                    }
                    if (flag || 50 === count) {
                        clearInterval(intervalTime);
                    }
                    count++;
                }, 500);
        }

        self.doConvertToDefaultSymbolCode = function () {
            var count = 0,
                flag = false,
                intervalTime = setInterval(function () {
                    const analytics_area = $('.bwf-analytics-card.bwfcrm-contacts-list .bwf-table-table .bwf-table-item .woocommerce-Price-currencySymbol');
                    if (analytics_area.length) {
                        analytics_area.html(yayCurrency_Admin.fkit_default_symbol)
                        flag = true;
                    }
                    if (flag || 20 === count) {
                        clearInterval(intervalTime);
                    }
                    count++;
                }, 500);

        }

        self.showNotice = function () {
            const sync_orders = yayCurrency_Admin.sync_orders ?? false;
            if ((sync_orders && sync_orders.reverted && 'yes' === sync_orders.reverted) || !$('.woocommerce-layout__primary').length) {
                return;
            }
            const html = `<div data-wp-component="Card" style="background:#ffffff;" class="components-surface components-card woocommerce-store-alerts is-alert-update yay-currency-admin-notice-container">
            <div class="yay-currency-admin-notice-wrapper">
                <div data-wp-component="CardHeader" class="components-flex components-card__header components-card-header yay-currency-admin-notice-heading">
                    <h2 style="color: rgb(30, 30, 30);margin: 0px; font-size: calc(24px);font-weight: normal;line-height: 32px;" data-wp-component="Text" class="components-truncate components-text">${sync_orders.notice_title}</h2>
                </div>
                <div data-wp-component="CardBody" class="components-card__body components-card-body yay-currency-admin-notice-content">
                    <div class="woocommerce-store-alerts__message">${sync_orders.notice_desc}</div>
                </div>
                <div data-wp-component="CardFooter" class="components-flex components-card__footer components-card-footer yay-currency-admin-notice-footer">
                    <div class="woocommerce-store-alerts__actions"><a href="javascript:void(0)" class="components-button is-secondary yay-currency-analytics-fetch-button">${sync_orders.notice_button}</a></div>
                </div>
            </div>
            
        </div>
        `;
            $('.woocommerce-layout__primary').prepend(html)
        }

        self.fetchAnalyticsButton = function (paged = 1) {
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'yayCurrency_sync_orders_revert_to_base',
                    _yay_sync: 'yes',
                    _sync_currencies: yayCurrency_Admin.sync_currencies,
                    _paged: paged,
                    _nonce: yayCurrency_Admin.nonce,
                },
                beforeSend: function (res) {
                    $(self.sync_orders_button).addClass(self.loadingButtonClass);
                    $('#adminmenumain,.woocommerce-layout__header,.woocommerce-layout__main').css({
                        'pointer-events': 'none',
                        'opacity': '0.4',
                    })
                },
                success: function success(res) {
                    if (res.success) {
                        if (res.data.paged) {
                            self.fetchAnalyticsButton(res.data.paged);
                        } else {
                            $(self.sync_orders_button).removeClass(self.loadingButtonClass);
                            location.reload();
                        }
                    } else {
                        $(self.sync_orders_button).removeClass(self.loadingButtonClass);
                        location.reload();
                    }

                }
            });

        }
    };

    jQuery(document).ready(function ($) {
        var yay_currency_admin = new yayCurrencyAdmin();
        yay_currency_admin.init();
    });
})(jQuery);