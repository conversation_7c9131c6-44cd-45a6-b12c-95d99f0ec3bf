'use strict';
(function ($) {
    // Defined Variable
    const yay_currency_third_party_args = {
        opacityClass: 'yay-currency-cache-opacity',
        productAddonPricesWrapper: 'form.cart .wc-pao-addons-container', // WooCommerce Product Addons
    }

    // ================Add Action Hooks================

    yayCurrencyHooks.addAction('yayCurrencyBeforeSendCaching', function (args) {
        const res = args.response;
        // WooCommerce Product Addons
        if (yay_currency_caching_data.wc_product_addons_active) {
            $(yay_currency_third_party_args.productAddonPricesWrapper).addClass(yay_currency_third_party_args.opacityClass);
        }
    });

    yayCurrencyHooks.addAction('yayCurrencyResponseCaching', function (args) {
        const res = args.response,
            currentCurrency = res.data.current_currency.currency,
            price_details = res.data.price_details ? res.data.price_details : false;

        // Flux Theme
        if ('fluxwp' === yayCurrency.current_theme && $('#currencyButtonContainer').length) {
            document.getElementById('selectedCurrency').innerText = currentCurrency;
            var flagElement = document.getElementById('currencyFlag');
            var flagUrl = getFlagUrlByCurrency(currentCurrency);
            flagElement.style.backgroundImage = 'url(' + flagUrl + ')';
        }

        // Woo All Products For Subscriptions
        if (yay_currency_caching_data.wcs_att) {
            if (yay_currency_caching_data.product_id && price_details && $('.wcsatt-options-wrapper').length && $('.product form.cart').length) {
                for (let id in price_details) {
                    if (id === yay_currency_caching_data.product_id) {
                        $('.wcsatt-options-wrapper').html(price_details[id]);
                    }
                }
                $('.product form.cart').each(function () {
                    if ($(this).data('satt_script')) {
                        $(this).removeData('satt_script');
                    }
                });
                $(document.body).trigger('wcsatt-initialize');
            }
        }

        // WooCommerce Product Addons
        if (yay_currency_caching_data.wc_product_addons_active) {
            if ($(yay_currency_third_party_args.productAddonPricesWrapper).length && res.data.yay_currency_addon_display_html) {
                $(yay_currency_third_party_args.productAddonPricesWrapper).replaceWith(res.data.yay_currency_addon_display_html)
                if (res.data.current_symbol) {
                    woocommerce_addons_params.currency_format_symbol = res.data.current_symbol;
                }

                $('body')
                    .find('.cart:not(.cart_group)')
                    .each(function () {
                        new WC_PAO.Form($(this));
                    });

            }
        }

    });

    yayCurrencyHooks.addAction('yayCurrencyResponseCaching', function (args) {
        // WooCommerce Product Addons
        if (yay_currency_caching_data.wc_product_addons_active) {
            $(yay_currency_third_party_args.productAddonPricesWrapper).removeClass(yay_currency_third_party_args.opacityClass);
        }
    });

    yayCurrencyHooks.addAction('yayCurrencyCompatibleThirdParty', function (args) {
        const currencyID = args.currencyID;
        // compatible with Measurement Price Calculator plugin
        if (window.wc_price_calculator_params) {
            const applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID),
                rateFee = YayCurrency_Callback.Helper.getRateFeeByCurrency(applyCurrency);

            window.wc_price_calculator_params.woocommerce_currency_pos =
                applyCurrency.currencyPosition;
            window.wc_price_calculator_params.woocommerce_price_decimal_sep =
                applyCurrency.decimalSeparator;
            window.wc_price_calculator_params.woocommerce_price_num_decimals =
                applyCurrency.numberDecimal;
            window.wc_price_calculator_params.woocommerce_price_thousand_sep =
                applyCurrency.thousandSeparator;

            window.wc_price_calculator_params.pricing_rules &&
                window.wc_price_calculator_params.pricing_rules.forEach((rule) => {
                    rule.price = (parseFloat(rule.price) * rateFee).toString();
                    rule.regular_price = (
                        parseFloat(rule.regular_price) * rateFee
                    ).toString();
                    rule.sale_price = (
                        parseFloat(rule.sale_price) * rateFee
                    ).toString();
                });
        }

        // Compatible with WooCommerce PayPal Payments plugin
        if (window.yayCurrency.ppc_paypal) {

            //Refresh mini cart - not on checkout page (checkout_diff_currency)
            if (yayCurrency.checkout_diff_currency && '1' === yayCurrency.checkout_diff_currency) {
                jQuery(document).ready(function ($) {
                    if (!yayCurrency.checkout_page || '1' !== yayCurrency.checkout_page) {
                        $(document.body).trigger('wc_fragment_refresh');
                    }
                });
            }

            const setOrDeleteYayPaypalCookie = (cookieName, condition) => {
                if (condition) {
                    YayCurrency_Callback.Helper.setCookie(cookieName, 'yes', +yayCurrency.cookie_lifetime_days);
                } else if (YayCurrency_Callback.Helper.getCookie(cookieName)) {
                    YayCurrency_Callback.Helper.deleteCookie(cookieName);
                }
            };

            const updateYayPaypalCookies = () => {
                setOrDeleteYayPaypalCookie('ppc_paypal_cart_or_product_page', '1' === yayCurrency.cart_page || '1' === yayCurrency.product_page);
                setOrDeleteYayPaypalCookie('ppc_paypal_checkout_page', yayCurrency.checkout_page && '1' === yayCurrency.checkout_page);
            };

            // Initial cookie setup
            updateYayPaypalCookies();

            // Update cookies on page visibility change
            $(document).on('visibilitychange', function () {
                if ('visible' === document.visibilityState) {
                    updateYayPaypalCookies();
                }
            });
        }
        //
    });

    // ================Add Filter Hooks================

    yayCurrencyHooks.addFilter('yayCurrencyGetDataCaching', function (data) {
        // WooCommerce Product Addons
        if (yay_currency_caching_data.wc_product_addons_active) {
            if ($(yay_currency_third_party_args.productAddonPricesWrapper).length && yay_currency_caching_data.product_id) {
                data.product_id = yay_currency_caching_data.product_id;
            }
        }
        return data;
    });


    yayCurrencyHooks.addFilter('yayCurrencyDetectAllowCaching', function (allow) {
        if (typeof yay_dokan_data !== 'undefined') {
            if ('yes' === yay_dokan_data.dashboard_page) {
                return false;
            }
        }
        return allow;
    });

})(jQuery);