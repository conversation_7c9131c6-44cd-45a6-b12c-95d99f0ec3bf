<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class CountryHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function currency_code_by_country_code() {
		$countries_code = array(
			'AED' => 'ae',
			'AFN' => 'af',
			'ALL' => 'al',
			'AMD' => 'am',
			'ANG' => 'an',
			'AOA' => 'ao',
			'ARS' => 'ar',
			'AUD' => 'au',
			'AWG' => 'aw',
			'AZN' => 'az',
			'BAM' => 'ba',
			'BBD' => 'bb',
			'BDT' => 'bd',
			'BGN' => 'bg',
			'BHD' => 'bh',
			'BIF' => 'bi',
			'BMD' => 'bm',
			'BND' => 'bn',
			'BOB' => 'bo',
			'BRL' => 'br',
			'BSD' => 'bs',
			'BTN' => 'bt',
			'BTC' => 'btc',
			'BWP' => 'bw',
			'BYN' => 'by',
			'BYR' => 'byr',
			'BZD' => 'bz',
			'CAD' => 'ca',
			'CDF' => 'cd',
			'CHF' => 'ch',
			'CLP' => 'cl',
			'CNY' => 'cn',
			'COP' => 'co',
			'CRC' => 'cr',
			'CUP' => 'cu',
			'CUC' => 'cuc',
			'CVE' => 'cv',
			'CZK' => 'cz',
			'DJF' => 'dj',
			'DKK' => 'dk',
			'DOP' => 'do',
			'DZD' => 'dz',
			'EGP' => 'eg',
			'ERN' => 'er',
			'ETB' => 'et',
			'ETH' => 'eth',
			'EUR' => 'eu',
			'FJD' => 'fj',
			'FKP' => 'fk',
			'GBP' => 'gb',
			'GEL' => 'ge',
			'GGP' => 'gg',
			'GHS' => 'gh',
			'GIP' => 'gi',
			'GMD' => 'gm',
			'GNF' => 'gn',
			'GTQ' => 'gt',
			'GYD' => 'gy',
			'HKD' => 'hk',
			'HNL' => 'hn',
			'HRK' => 'hr',
			'HTG' => 'ht',
			'HUF' => 'hu',
			'IDR' => 'id',
			'ILS' => 'il',
			'IMP' => 'im',
			'INR' => 'in',
			'IQD' => 'iq',
			'IRR' => 'ir',
			'IRT' => 'irt',
			'ISK' => 'is',
			'JEP' => 'je',
			'JMD' => 'jm',
			'JOD' => 'jo',
			'JPY' => 'jp',
			'KES' => 'ke',
			'KGS' => 'kg',
			'KHR' => 'kh',
			'KMF' => 'km',
			'KPW' => 'kp',
			'KRW' => 'kr',
			'KWD' => 'kw',
			'KYD' => 'ky',
			'KZT' => 'kz',
			'LAK' => 'la',
			'LBP' => 'lb',
			'LKR' => 'lk',
			'LRD' => 'lr',
			'LSL' => 'ls',
			'LYD' => 'ly',
			'MAD' => 'ma',
			'MDL' => 'md',
			'PRB' => 'mda',
			'MGA' => 'mg',
			'MKD' => 'mk',
			'MMK' => 'mm',
			'MNT' => 'mn',
			'MOP' => 'mo',
			'MRU' => 'mr',
			'MUR' => 'mu',
			'MVR' => 'mv',
			'MWK' => 'mw',
			'MXN' => 'mx',
			'MYR' => 'my',
			'MZN' => 'mz',
			'NAD' => 'na',
			'NGN' => 'ng',
			'NIO' => 'ni',
			'NOK' => 'no',
			'XOF' => 'none',
			'XPF' => 'none1',
			'XCD' => 'none2',
			'XAF' => 'none3',
			'NPR' => 'np',
			'NZD' => 'nz',
			'OMR' => 'om',
			'PAB' => 'pa',
			'PEN' => 'pe',
			'PGK' => 'pg',
			'PHP' => 'ph',
			'PKR' => 'pk',
			'PLN' => 'pl',
			'PYG' => 'py',
			'QAR' => 'qa',
			'RON' => 'ro',
			'RSD' => 'rs',
			'RUB' => 'ru',
			'RWF' => 'rw',
			'SAR' => 'sa',
			'SBD' => 'sb',
			'SCR' => 'sc',
			'SDG' => 'sd',
			'SEK' => 'se',
			'SGD' => 'sg',
			'SHP' => 'sh',
			'SLL' => 'sl',
			'SOS' => 'so',
			'SRD' => 'sr',
			'SSP' => 'ss',
			'STN' => 'st',
			'SYP' => 'sy',
			'SZL' => 'sz',
			'THB' => 'th',
			'TJS' => 'tj',
			'TMT' => 'tm',
			'TND' => 'tn',
			'TOP' => 'to',
			'TRY' => 'tr',
			'TTD' => 'tt',
			'TWD' => 'tw',
			'TZS' => 'tz',
			'UAH' => 'ua',
			'UGX' => 'ug',
			'USD' => 'us',
			'UYU' => 'uy',
			'UZS' => 'uz',
			'VES' => 've',
			'VEF' => 'vef',
			'VND' => 'vn',
			'VUV' => 'vu',
			'WST' => 'ws',
			'YER' => 'ye',
			'ZAR' => 'za',
			'ZMW' => 'zm',
		);
		return $countries_code;
	}

	public static function get_locale_by_country_code( $country_code ) {
		// List of country codes with their locales
		$locales = array(
			'AF' => 'fa_AF',  // Afghanistan
			'AL' => 'sq_AL',  // Albania
			'DZ' => 'ar_DZ',  // Algeria
			'AS' => 'en_AS',  // American Samoa
			'AD' => 'ca_AD',  // Andorra
			'AO' => 'pt_AO',  // Angola
			'AI' => 'en_AI',  // Anguilla
			'AG' => 'en_AG',  // Antigua and Barbuda
			'AR' => 'es_AR',  // Argentina
			'AM' => 'hy_AM',  // Armenia
			'AU' => 'en_AU',  // Australia
			'AT' => 'de_AT',  // Austria
			'AZ' => 'az_AZ',  // Azerbaijan
			'BS' => 'en_BS',  // Bahamas
			'BH' => 'ar_BH',  // Bahrain
			'BD' => 'bn_BD',  // Bangladesh
			'BB' => 'en_BB',  // Barbados
			'BY' => 'be_BY',  // Belarus
			'BE' => 'nl_BE',  // Belgium
			'BZ' => 'en_BZ',  // Belize
			'BJ' => 'fr_BJ',  // Benin
			'BM' => 'en_BM',  // Bermuda
			'BT' => 'dz_BT',  // Bhutan
			'BO' => 'es_BO',  // Bolivia
			'BA' => 'bs_BA',  // Bosnia and Herzegovina
			'BW' => 'en_BW',  // Botswana
			'BR' => 'pt_BR',  // Brazil
			'BN' => 'ms_BN',  // Brunei
			'BG' => 'bg_BG',  // Bulgaria
			'BF' => 'fr_BF',  // Burkina Faso
			'BI' => 'fr_BI',  // Burundi
			'KH' => 'km_KH',  // Cambodia
			'CM' => 'fr_CM',  // Cameroon
			'CA' => 'en_CA',  // Canada
			'CV' => 'pt_CV',  // Cape Verde
			'KY' => 'en_KY',  // Cayman Islands
			'CF' => 'fr_CF',  // Central African Republic
			'TD' => 'fr_TD',  // Chad
			'CL' => 'es_CL',  // Chile
			'CN' => 'zh_CN',  // China
			'CO' => 'es_CO',  // Colombia
			'KM' => 'ar_KM',  // Comoros
			'CG' => 'fr_CG',  // Congo
			'CD' => 'fr_CD',  // Congo (Democratic Republic)
			'CR' => 'es_CR',  // Costa Rica
			'CI' => 'fr_CI',  // Côte d'Ivoire
			'HR' => 'hr_HR',  // Croatia
			'CU' => 'es_CU',  // Cuba
			'CY' => 'el_CY',  // Cyprus
			'CZ' => 'cs_CZ',  // Czech Republic
			'DK' => 'da_DK',  // Denmark
			'DJ' => 'fr_DJ',  // Djibouti
			'DM' => 'en_DM',  // Dominica
			'DO' => 'es_DO',  // Dominican Republic
			'EC' => 'es_EC',  // Ecuador
			'EG' => 'ar_EG',  // Egypt
			'SV' => 'es_SV',  // El Salvador
			'GQ' => 'es_GQ',  // Equatorial Guinea
			'ER' => 'ti_ER',  // Eritrea
			'EE' => 'et_EE',  // Estonia
			'ET' => 'am_ET',  // Ethiopia
			'FJ' => 'en_FJ',  // Fiji
			'FI' => 'fi_FI',  // Finland
			'FR' => 'fr_FR',  // France
			'GA' => 'fr_GA',  // Gabon
			'GM' => 'en_GM',  // Gambia
			'GE' => 'ka_GE',  // Georgia
			'DE' => 'de_DE',  // Germany
			'GH' => 'en_GH',  // Ghana
			'GR' => 'el_GR',  // Greece
			'GD' => 'en_GD',  // Grenada
			'GT' => 'es_GT',  // Guatemala
			'GN' => 'fr_GN',  // Guinea
			'GW' => 'pt_GW',  // Guinea-Bissau
			'GY' => 'en_GY',  // Guyana
			'HT' => 'fr_HT',  // Haiti
			'HN' => 'es_HN',  // Honduras
			'HU' => 'hu_HU',  // Hungary
			'IS' => 'is_IS',  // Iceland
			'IN' => 'hi_IN',  // India
			'ID' => 'id_ID',  // Indonesia
			'IR' => 'fa_IR',  // Iran
			'IQ' => 'ar_IQ',  // Iraq
			'IE' => 'en_IE',  // Ireland
			'IL' => 'he_IL',  // Israel
			'IT' => 'it_IT',  // Italy
			'JM' => 'en_JM',  // Jamaica
			'JP' => 'ja_JP',  // Japan
			'JO' => 'ar_JO',  // Jordan
			'KZ' => 'kk_KZ',  // Kazakhstan
			'KE' => 'en_KE',  // Kenya
			'KI' => 'en_KI',  // Kiribati
			'KP' => 'ko_KP',  // North Korea
			'KR' => 'ko_KR',  // South Korea
			'KW' => 'ar_KW',  // Kuwait
			'KG' => 'ky_KG',  // Kyrgyzstan
			'LA' => 'lo_LA',  // Laos
			'LV' => 'lv_LV',  // Latvia
			'LB' => 'ar_LB',  // Lebanon
			'LS' => 'en_LS',  // Lesotho
			'LR' => 'en_LR',  // Liberia
			'LY' => 'ar_LY',  // Libya
			'LI' => 'de_LI',  // Liechtenstein
			'LT' => 'lt_LT',  // Lithuania
			'LU' => 'fr_LU',  // Luxembourg
			'MG' => 'fr_MG',  // Madagascar
			'MW' => 'en_MW',  // Malawi
			'MY' => 'ms_MY',  // Malaysia
			'MV' => 'dv_MV',  // Maldives
			'ML' => 'fr_ML',  // Mali
			'MT' => 'mt_MT',  // Malta
			'MH' => 'en_MH',  // Marshall Islands
			'MR' => 'ar_MR',  // Mauritania
			'MU' => 'en_MU',  // Mauritius
			'MX' => 'es_MX',  // Mexico
			'FM' => 'en_FM',  // Micronesia
			'MD' => 'ro_MD',  // Moldova
			'MC' => 'fr_MC',  // Monaco
			'MN' => 'mn_MN',  // Mongolia
			'ME' => 'sr_ME',  // Montenegro
			'MA' => 'ar_MA',  // Morocco
			'MZ' => 'pt_MZ',  // Mozambique
			'MM' => 'my_MM',  // Myanmar
			'NA' => 'en_NA',  // Namibia
			'NR' => 'en_NR',  // Nauru
			'NP' => 'ne_NP',  // Nepal
			'NL' => 'nl_NL',  // Netherlands
			'NZ' => 'en_NZ',  // New Zealand
			'NI' => 'es_NI',  // Nicaragua
			'NE' => 'fr_NE',  // Niger
			'NG' => 'en_NG',  // Nigeria
			'NO' => 'nb_NO',  // Norway
			'OM' => 'ar_OM',  // Oman
			'PK' => 'ur_PK',  // Pakistan
			'PW' => 'en_PW',  // Palau
			'PA' => 'es_PA',  // Panama
			'PG' => 'en_PG',  // Papua New Guinea
			'PY' => 'es_PY',  // Paraguay
			'PE' => 'es_PE',  // Peru
			'PH' => 'en_PH',  // Philippines
			'PL' => 'pl_PL',  // Poland
			'PT' => 'pt_PT',  // Portugal
			'PR' => 'es_PR',  // Puerto Rico
			'QA' => 'ar_QA',  // Qatar
			'RO' => 'ro_RO',  // Romania
			'RU' => 'ru_RU',  // Russia
			'RW' => 'rw_RW',  // Rwanda
			'KN' => 'en_KN',  // Saint Kitts and Nevis
			'LC' => 'en_LC',  // Saint Lucia
			'VC' => 'en_VC',  // Saint Vincent and the Grenadines
			'WS' => 'sm_WS',  // Samoa
			'ST' => 'pt_ST',  // São Tomé and Príncipe
			'SA' => 'ar_SA',  // Saudi Arabia
			'SN' => 'fr_SN',  // Senegal
			'RS' => 'sr_RS',  // Serbia
			'SC' => 'en_SC',  // Seychelles
			'SL' => 'en_SL',  // Sierra Leone
			'SG' => 'en_SG',  // Singapore
			'SK' => 'sk_SK',  // Slovakia
			'SI' => 'sl_SI',  // Slovenia
			'SB' => 'en_SB',  // Solomon Islands
			'SO' => 'so_SO',  // Somalia
			'ZA' => 'af_ZA',  // South Africa
			'ES' => 'es_ES',  // Spain
			'LK' => 'si_LK',  // Sri Lanka
			'SD' => 'ar_SD',  // Sudan
			'SR' => 'nl_SR',  // Suriname
			'SZ' => 'en_SZ',  // Eswatini
			'SE' => 'sv_SE',  // Sweden
			'CH' => 'de_CH',  // Switzerland
			'SY' => 'ar_SY',  // Syria
			'TW' => 'zh_TW',  // Taiwan
			'TJ' => 'tg_TJ',  // Tajikistan
			'TZ' => 'sw_TZ',  // Tanzania
			'TH' => 'th_TH',  // Thailand
			'TL' => 'tet_TL', // Timor-Leste
			'TG' => 'fr_TG',  // Togo
			'TO' => 'en_TO',  // Tonga
			'TT' => 'en_TT',  // Trinidad and Tobago
			'TN' => 'ar_TN',  // Tunisia
			'TR' => 'tr_TR',  // Turkey
			'TM' => 'tk_TM',  // Turkmenistan
			'TV' => 'en_TV',  // Tuvalu
			'UG' => 'lg_UG',  // Uganda
			'UA' => 'uk_UA',  // Ukraine
			'AE' => 'ar_AE',  // United Arab Emirates
			'GB' => 'en_GB',  // United Kingdom
			'US' => 'en_US',  // United States
			'UY' => 'es_UY',  // Uruguay
			'UZ' => 'uz_UZ',  // Uzbekistan
			'VU' => 'bi_VU',  // Vanuatu
			'VE' => 'es_VE',  // Venezuela
			'VN' => 'vi_VN',  // Vietnam
			'YE' => 'ar_YE',  // Yemen
			'ZM' => 'en_ZM',  // Zambia
			'ZW' => 'en_ZW',  // Zimbabwe
		);

		// Check if the country code exists in the array and return the corresponding locale
		if ( array_key_exists( $country_code, $locales ) ) {
			return $locales[ $country_code ];
		} else {
			// Return default locale if country code not found
			return 'en_US';
		}
	}

	public static function format_amount_by_country_code( $amount, $country_code, $currency_code ) {
		// Get the locale for the country
		$locale = self::get_locale_by_country_code( $country_code );

		try {
			// Create a number formatter for currency
			$formatter = new \NumberFormatter( $locale, \NumberFormatter::CURRENCY );
			return $formatter->formatCurrency( $amount, $currency_code );
		} catch ( \Exception $e ) {
			// Log or handle error if necessary
			return $amount; // Return unformatted amount if there's an error
		}
	}

	public static function get_flag_by_country_code( $country_code = 'us' ) {

		$flag_url = YAY_CURRENCY_PLUGIN_DIR . 'assets/dist/flags/' . $country_code . '.svg';

		if ( file_exists( $flag_url ) ) {
			$flag_url = YAY_CURRENCY_PLUGIN_URL . 'assets/dist/flags/' . $country_code . '.svg';
		} else {
			$flag_url = YAY_CURRENCY_PLUGIN_URL . 'assets/dist/flags/us.svg';
		}

		return apply_filters( 'yay_currency_get_flag_by_country_code', $flag_url, $country_code );

	}

	public static function geolocate_via_api( $ip_address ) {

		// If the IP address is localhost, return an empty value automatically.
		if ( in_array( $ip_address, array( '127.0.0.1', '::1' ), true ) ) {
			return '';
		}

		// Check if the result is already cached.
		$transient_key = 'yay_currency_geolocation_' . md5( $ip_address );
		$cached_result = get_transient( $transient_key );

		if ( $cached_result ) {
			return $cached_result;
		}

		$geoip_apis = array(
			'ipinfo.io'  => 'https://ipinfo.io/%s/json',
			'ip-api.com' => 'http://ip-api.com/json/%s',
		);

		$geoip_services = apply_filters( 'woocommerce_geolocation_geoip_apis', $geoip_apis );

		if ( empty( $geoip_services ) ) {
			return '';
		}

		$country_code = false;

		$geoip_services_keys = array_keys( $geoip_services );
		shuffle( $geoip_services_keys );

		foreach ( $geoip_services_keys as $service_name ) {
			$service_endpoint = $geoip_services[ $service_name ];
			$response         = wp_safe_remote_get(
				sprintf( $service_endpoint, $ip_address ),
				array(
					'timeout'    => 2,
					'user-agent' => 'WooCommerce/' . wc()->version,
				)
			);

			if ( ! is_wp_error( $response ) && $response['body'] ) {
				switch ( $service_name ) {
					case 'ipinfo.io':
						$data         = json_decode( $response['body'] );
						$country_code = isset( $data->country ) ? $data->country : '';
						break;
					case 'ip-api.com':
						$data         = json_decode( $response['body'] );
						$country_code = isset( $data->countryCode ) ? $data->countryCode : ''; // @codingStandardsIgnoreLine
						break;
					default:
						$country_code = $response['body'];
						break;
				}

				$country_code = sanitize_text_field( strtoupper( $country_code ) );

				if ( $country_code ) {
					break;
				}
			}
		}

		// Cache the result for 24 hours.
		if ( $country_code ) {
			set_transient( $transient_key, $country_code, DAY_IN_SECONDS );
		}

		return $country_code;

	}

	public static function get_ip_address() {

		if ( isset( $_REQUEST['yay-currency-country-ip'] ) ) {
			return sanitize_text_field( wp_unslash( $_REQUEST['yay-currency-country-ip'] ) );
		}

		$ip_address = '';
		if ( isset( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) {
			// Proxy servers can send through this header like this: X-Forwarded-For: client1, proxy1, proxy2
			// Make sure we always only send through the first IP in the list which should always be the client IP.
			$ip_address = (string) rest_is_ip_address( trim( current( preg_split( '/,/', sanitize_text_field( wp_unslash( $_SERVER['HTTP_X_FORWARDED_FOR'] ) ) ) ) ) );
		} elseif ( isset( $_SERVER['HTTP_X_REAL_IP'] ) ) {
			$ip_address = sanitize_text_field( wp_unslash( $_SERVER['HTTP_X_REAL_IP'] ) );
		} elseif ( isset( $_SERVER['REMOTE_ADDR'] ) ) {
			$ip_address = sanitize_text_field( wp_unslash( $_SERVER['REMOTE_ADDR'] ) );
		}

		return apply_filters( 'yay_currency_get_ip_address', $ip_address );
	}

	public static function get_country_code_from_headers() {
		$country_code = '';

		$headers = array(
			'MM_COUNTRY_CODE',
			'GEOIP_COUNTRY_CODE',
			'HTTP_CF_IPCOUNTRY',
			'HTTP_X_COUNTRY_CODE',
		);

		foreach ( $headers as $header ) {
			if ( empty( $_SERVER[ $header ] ) ) {
				continue;
			}

			$country_code = strtoupper( sanitize_text_field( wp_unslash( $_SERVER[ $header ] ) ) );
			break;
		}

		return $country_code;
	}

	// Get Country Code by Force Payment
	public static function detect_country_code() {
		$country_code = '';
		global $woocommerce;
		if ( isset( $woocommerce->customer ) && ! empty( $woocommerce->customer ) ) {
			if ( 'billing_country' === self::force_payment_country() ) {
				$country_code = isset( $_REQUEST['country'] ) ? sanitize_text_field( $_REQUEST['country'] ) : $woocommerce->customer->get_billing_country();
			} else {
				$country_code = isset( $_REQUEST['s_country'] ) ? sanitize_text_field( $_REQUEST['s_country'] ) : $woocommerce->customer->get_shipping_country();
			}
		}

		return $country_code;
	}

	public static function get_currency_code_by_country_code( $country_code ) {

		if ( empty( $country_code ) ) {
			return false;
		}

		require_once ABSPATH . 'wp-admin/includes/class-wp-filesystem-base.php';
		require_once ABSPATH . 'wp-admin/includes/class-wp-filesystem-direct.php';

		$wp_filesystem = new \WP_Filesystem_Direct( null );
		$country_data  = $wp_filesystem->get_contents( YAY_CURRENCY_PLUGIN_DIR . 'lib/country-data.json' );

		$country_code = strtolower( $country_code );

		$decoded_country_data = json_decode( $country_data );
		$currency_code        = isset( $decoded_country_data->$country_code ) ? $decoded_country_data->$country_code : false;

		return $currency_code;

	}

	public static function get_country_info_from_IP() {

		$ip_address = self::get_ip_address();

		if ( apply_filters( 'yay_currency_detect_country_code_from_headers', false ) ) {
			$country = self::get_country_code_from_headers();
		} else {
			$country = self::geolocate_via_api( $ip_address );
		}

		$country = apply_filters( 'yay_currency_get_country_code', $country, $ip_address );

		if ( empty( $country ) ) {
			return array(
				'country_code'  => $country,
				'currency_code' => Helper::default_currency_code(),
			);
		}

		$currency_code = self::get_currency_code_by_country_code( $country );

		return array(
			'country_code'  => $country,
			'currency_code' => $currency_code ? $currency_code : Helper::default_currency_code(),
		);
	}

	public static function count_display_elements_in_switcher( $is_show_flag = true, $is_show_currency_name = true, $is_show_currency_symbol = true, $is_show_currency_code = true ) {
		$display_elements_array = array();
		$is_show_flag ? array_push( $display_elements_array, $is_show_flag ) : null;
		$is_show_currency_name ? array_push( $display_elements_array, $is_show_currency_name ) : null;
		$is_show_currency_symbol ? array_push( $display_elements_array, $is_show_currency_symbol ) : null;
		$is_show_currency_code ? array_push( $display_elements_array, $is_show_currency_code ) : null;
		return count( $display_elements_array );
	}

	public static function get_apply_currency_by_force_payment_country( $is_notice = false ) {
		$apply_currency = false;
		// Checkout page use Shortcode
		if ( self::detect_force_country_by_checkout_page() || $is_notice ) {
			global $woocommerce;
			if ( isset( $woocommerce->customer ) && ! empty( $woocommerce->customer ) ) {
				$country_code = self::detect_country_code();
				if ( ! empty( $country_code ) ) {
					$currency_code = self::get_currency_code_by_country_code( $country_code );
					$currency_code = apply_filters( 'yay_currency_custom_currency_code_by_force_country', $currency_code );
					if ( $currency_code ) {
						$apply_currency_by_country_code = self::get_current_currency_by_force_specific_country( $country_code, $currency_code );
						if ( $apply_currency_by_country_code ) {
							return $apply_currency_by_country_code;
						}
					}
				}
			}
		}

		$apply_currency = apply_filters( 'yay_currency_force_payment_country_with_blocks', $apply_currency, true );

		return $apply_currency;
	}

	public static function get_current_currency_by_force_specific_country( $country_code, $currency_code ) {
		if ( get_option( 'yay_currency_auto_select_currency_by_countries', 0 ) ) {
			$converted_currency                                 = YayCurrencyHelper::converted_currency();
			$current_currency_by_auto_select_currency_countries = self::get_yay_currency_by_country_code( $country_code, $currency_code, $converted_currency );
			if ( $current_currency_by_auto_select_currency_countries ) {
				return $current_currency_by_auto_select_currency_countries;
			}
		}

		$current_currency_by_country_code = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );

		return $current_currency_by_country_code;

	}

	// CART PAGE - SHORTCODES
	public static function get_apply_currency_by_force_shipping_country_cart_page() {
		$apply_currency = false;
		if ( self::detect_force_country_by_cart_page() ) {
			$country_code = self::detect_force_country_by_cart_page( true );
			if ( ! empty( $country_code ) ) {
				$currency_code = self::get_currency_code_by_country_code( $country_code );
				$currency_code = apply_filters( 'yay_currency_custom_currency_code_by_force_country', $currency_code );
				if ( $currency_code ) {
					$apply_currency_by_country_code = self::get_current_currency_by_force_specific_country( $country_code, $currency_code );
					if ( $apply_currency_by_country_code ) {
						return $apply_currency_by_country_code;
					}
				}
			}
		}

		$apply_currency = apply_filters( 'yay_currency_force_payment_country_with_blocks', $apply_currency, false );

		return $apply_currency;
	}

	// Force Payment
	public static function force_payment_country() {
		$force_payment_currency         = self::force_payment_specific_currency();
		$is_checkout_different_currency = get_option( 'yay_currency_checkout_different_currency', 0 );
		if ( ! isset( $force_payment_currency['force_enable'] ) || 0 === intval( $force_payment_currency['force_enable'] ) || 0 === intval( $is_checkout_different_currency ) ) {
			return false;
		}
		return $force_payment_currency['force_payment'];
	}

	public static function get_rest_route_via_rest_api() {

		$query_vars = isset( $GLOBALS['wp']->query_vars ) && ! empty( $GLOBALS['wp']->query_vars ) ? $GLOBALS['wp']->query_vars : false;

		if ( ! $query_vars || ! isset( $query_vars['rest_route'] ) ) {
			return false;
		}

		$rest_route = ! empty( $query_vars['rest_route'] ) ? $query_vars['rest_route'] : false;

		if ( ! $rest_route ) {
			return false;
		}

		return $rest_route;

	}

	// Force Country

	public static function detect_force_country_by_cart_blocks_page() {

		if ( SupportHelper::detect_rest_api_doing() && isset( $_COOKIE['yay_cart_blocks_page'] ) ) {
			return true;
		}

		return false;

	}

	public static function detect_force_country_by_checkout_blocks_page() {

		if ( SupportHelper::detect_rest_api_doing() && isset( $_COOKIE['yay_checkout_blocks_page'] ) ) {
			return true;
		}

		return false;

	}

	// Detect WooCommerce Blocks --- use Rest API
	public static function detect_country_code_with_rest_api( $cart_page = false ) {
		$country_code = '';
		if ( isset( WC()->session ) && ! empty( WC()->session ) && isset( WC()->session->customer ) && ! empty( WC()->session->customer ) ) {
			$customer_data = WC()->session->customer;
			$country       = isset( $customer_data['country'] ) && ! empty( $customer_data['country'] ) ? $customer_data['country'] : false;
			if ( ! $country ) {
				return $country_code;
			}
			$shipping_country = isset( $customer_data['shipping_country'] ) && ! empty( $customer_data['shipping_country'] ) ? $customer_data['shipping_country'] : $country;
			if ( $cart_page ) {
				$country_code = $shipping_country;
			} else {
				$country_code = 'billing_country' === self::force_payment_country() ? $country : $shipping_country;
			}
		}

		return $country_code;
	}

	public static function detect_force_country_by_cart_page( $get_country_code = false ) {
		$is_force_country = false;

		if ( isset( $_REQUEST['calc_shipping_country'] ) && isset( $_REQUEST['woocommerce-shipping-calculator-nonce'] ) && isset( $_REQUEST['calc_shipping_postcode'] ) && isset( $_REQUEST['calc_shipping_country'] ) ) {
			if ( ! $get_country_code ) {
				return true;
			}
			$nonce_value = sanitize_text_field( $_REQUEST['woocommerce-shipping-calculator-nonce'] );
			$postcode    = sanitize_text_field( $_REQUEST['calc_shipping_postcode'] );
			$country     = sanitize_text_field( $_REQUEST['calc_shipping_country'] );
			if ( ! empty( $nonce_value ) && wp_verify_nonce( $nonce_value, 'woocommerce-shipping-calculator' ) && \WC_Validation::is_postcode( $postcode, $country ) ) {
				return $country;
			}
		}
		return $is_force_country;
	}

	public static function detect_force_country_by_checkout_page() {
		$args_checkout       = array( 'update_order_review', 'checkout' );
		$update_order_review = wp_doing_ajax() && ( isset( $_REQUEST['wc-ajax'] ) && in_array( $_REQUEST['wc-ajax'], $args_checkout, true ) );
		return $update_order_review;
	}

	public static function force_payment_specific_currency() {

		$args = array(
			'force_enable'      => 0,
			'force_payment'     => 'billing_country',
			'force_notice'      => 1,
			'force_notice_text' => 'Paying in <strong>%currency-selected%</strong> is not supported in your location. So your payment will be recorded in <strong>%currency-by-country%</strong>.',
			'reload_page'       => 0,
		);

		$force_payment_currency = get_option( 'yay_currency_force_payment_specific_currency', $args );

		if ( ! isset( $force_payment_currency['reload_page'] ) ) {
			$force_payment_currency['reload_page'] = '0';
		}

		return $force_payment_currency;
	}

	public static function detect_current_currency_by_country( $country_code = '', $currency_code = '', $find = 'relative', $converted_currency = array() ) {
		$apply_currency         = array();
		$country_code           = strtoupper( $country_code );
		$converted_currency     = $converted_currency ? $converted_currency : YayCurrencyHelper::converted_currency();
		$default_countries_args = array();
		foreach ( $converted_currency as $convert_currency ) {
			$countries = isset( $convert_currency['countries'] ) ? $convert_currency['countries'] : false;

			if ( ! $countries || ! isset( $countries[0] ) ) {
				continue;
			}

			if ( in_array( $country_code, $countries ) ) {
				$apply_currency = $convert_currency;
				break;
			}

			$match_currency_code = $currency_code === $convert_currency['currency'] ? true : false;
			if ( $match_currency_code ) {
				$apply_currency = $convert_currency;
			} elseif ( 'default' === $countries[0] ) {
				$default_countries_args[ $convert_currency['currency'] ] = $convert_currency;
			}
		}

		if ( 'relative' === $find ) {
			//If not found, then get the first
			if ( ! $apply_currency && $default_countries_args ) {
				$apply_currency = array_shift( $default_countries_args );
			}
		} elseif ( ! $apply_currency && $default_countries_args && isset( $default_countries_args[ $currency_code ] ) ) {
			$apply_currency = $default_countries_args[ $currency_code ];
		}

		return $apply_currency;
	}

	public static function get_yay_currency_by_country_code( $country_code = '', $currency_code = '', $converted_currency = array() ) {
		$apply_currency = array();
		if ( ! empty( $currency_code ) && ! empty( $country_code ) ) {
			$apply_currency = self::detect_current_currency_by_country( $country_code, $currency_code, 'relative', $converted_currency );
		}
		return $apply_currency;
	}

	public static function find_apply_currency_by_country_code( $country_code = '', $currency_code = '', $converted_currency = array() ) {

		$apply_currency = array();
		if ( ! empty( $currency_code ) && ! empty( $country_code ) ) {
			if ( get_option( 'yay_currency_auto_select_currency_by_countries', 0 ) ) {
				$apply_currency = self::detect_current_currency_by_country( $country_code, $currency_code, 'absolute', $converted_currency );
			} else {
				$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );
			}
		}
		return $apply_currency ? $apply_currency : false;

	}

	public static function get_country_currency_notice( $apply_currency = false ) {

		$country_info  = self::get_country_info_from_IP();
		$country_code  = isset( $country_info['country_code'] ) ? $country_info['country_code'] : false;
		$currency_code = isset( $country_info['currency_code'] ) ? $country_info['currency_code'] : false;

		if ( ! $country_code || ! $currency_code ) {
			return '';
		}

		$country_code = strtoupper( $country_code );

		if ( ! $apply_currency ) {
			$currency_by_country_code = self::get_yay_currency_by_country_code( $country_code, $currency_code );
			$apply_currency           = $currency_by_country_code ? $currency_by_country_code : YayCurrencyHelper::detect_current_currency();
		}

		$cookie_switcher_name = YayCurrencyHelper::get_cookie_name( 'switcher' );
		if ( isset( $_COOKIE[ $cookie_switcher_name ] ) ) {
			$cookie_switcher = sanitize_text_field( $_COOKIE[ $cookie_switcher_name ] );
			if ( intval( $cookie_switcher ) !== intval( $apply_currency['ID'] ) ) {
				return '';
			}
		}

		$html = '';
		if ( $currency_code === $apply_currency['currency'] || in_array( $country_code, $apply_currency['countries'] ) ) {
			$html         = get_option( 'yay_currency_setting_notice_text', 'You are from %current-country%, price will be in %current-currency% (%current-currency-symbol%).' );
			$country_name = WC()->countries->countries[ $country_code ];

			$currencies    = get_woocommerce_currencies();
			$currency_code = $currency_code === $apply_currency['currency'] ? $currency_code : $apply_currency['currency'];
			$currency_name = isset( $currencies[ $currency_code ] ) ? $currencies[ $currency_code ] : false;

			if ( ! $country_name || ! $currency_name || empty( $html ) ) {
				return '';
			}

			$html = str_replace( '%current-country%', $country_name, $html );
			$html = str_replace( '%current-currency%', $currency_name, $html );
			$html = str_replace( '%current-currency-symbol%', $apply_currency['symbol'], $html );
		}

		return $html;

	}

	public static function get_force_payment_notice_html( $old_apply_currency = array() ) {
		$force_payment = self::force_payment_country();
		global $woocommerce;
		if ( ! $force_payment || ! isset( $woocommerce->customer ) || empty( $woocommerce->customer ) ) {
			return '';
		}

		$force_payment_specific_currency = self::force_payment_specific_currency();
		$html                            = $force_payment_specific_currency ['force_notice_text'];

		if ( empty( $html ) ) {
			return '';
		}
		$apply_currency            = $old_apply_currency ? $old_apply_currency : YayCurrencyHelper::detect_current_currency();
		$apply_currency_by_country = self::get_apply_currency_by_force_payment_country( true );
		if ( ! $apply_currency_by_country || $apply_currency['currency'] === $apply_currency_by_country['currency'] ) {
			$html = '';
		}
		$html = str_replace( '%currency-selected%', $apply_currency['currency'], $html );
		if ( isset( $apply_currency_by_country['currency'] ) ) {
			$html = str_replace( '%currency-by-country%', $apply_currency_by_country['currency'], $html );
		}

		$billing_country_code  = $woocommerce->customer->get_billing_country();
		$shipping_country_code = $woocommerce->customer->get_shipping_country();
		$billing_country_name  = WC()->countries->countries[ $billing_country_code ];
		$shipping_country_name = WC()->countries->countries[ $shipping_country_code ];
		if ( ! empty( $billing_country_name ) ) {
			$html = str_replace( '%billing-country%', $billing_country_name, $html );
		}
		if ( ! empty( $shipping_country_name ) ) {
			$html = str_replace( '%shipping-country%', $shipping_country_name, $html );
		}

		return $html;
	}

	public static function display_force_payment_notice_html( $html = false ) {
		$force_payment_specific_currency = self::force_payment_specific_currency();
		if ( isset( $force_payment_specific_currency['force_notice'] ) && 1 === intval( $force_payment_specific_currency['force_notice'] ) ) {
			$html = self::get_force_payment_notice_html();
		}
		if ( SupportHelper::detect_payment_reload_page() && isset( $_COOKIE['yay_fore_payment_notice_html'] ) ) {
			$html = wp_kses_post( $_COOKIE['yay_fore_payment_notice_html'] );
		}
		return apply_filters( 'yay_currency_display_force_payment_notice_html', $html );
	}
}
