!function (e) { "use strict"; var a = function () { var a = this; a.dokanOrderWrapper = "#order-filter", a.dokanWithdrawArea = ".dokan-withdraw-area", a.balanceArea = ".dokan-withdraw-area .dokan-panel.dokan-panel-default:first-child .dokan-w8", a.regularPrice = "#_regular_price", a.salePrice = "#_sale_price", a.dokanReporstArea = ".dokan-reports-area .dokan-dashboard-header .entry-title", a.approximatelyPrice = yay_dokan_data.approximately_price && "yes" === yay_dokan_data.approximately_price, a.init = function () { a.customDokanOrderTable(), a.approximatelyPrice && (a.convertMiniWithdrawAmount(), a.customWithDrawArea(), a.addNewProductAction(), a.customProductArea()), yay_dokan_data.dokan_pro && (e(a.dokanReporstArea).length && !e(a.dokanReporstArea + " .yay-currency-single-page-switcher").length && e(a.dokanReporstArea).append(yayCurrency.shortCode), a.customReportStatementArea(), a.customCouponsArea()) }, a.convertMiniWithdrawAmount = function () { let t = e(a.dokanWithdrawArea); if (t.length > 0) { let r = a.balanceArea + " p"; e(r).find("strong").each(function (a) { 0 != a && e(this).html(yay_dokan_data.withdraw_limit_currency) }) } }, a.customDokanOrderTable = function () { let t = e(a.dokanOrderWrapper).find(".dokan-order-earning"); t.length > 0 && (e(a.dokanOrderWrapper).css("opacity", .2), t.each(function (t) { let r = e(this), n = r.closest("tr"), o = n.find(".dokan-order-total"), d = n.find(".dokan-order-id a"), c = d.attr("href"), i = a.getValueinParam("order_id", c); e.ajax({ url: yay_dokan_data.ajax_url, type: "POST", data: { action: "yay_custom_earning_from_order_table", order_id: i, seller_id: yay_dokan_data.seller_id, _nonce: yay_dokan_data.nonce }, success: function t(n) { setTimeout(() => { e(a.dokanOrderWrapper).css("opacity", 1) }, 500), n.success && n.data.earning && n.data.order_total && (r.html(n.data.earning), o.html(n.data.order_total)) }, complete: function t() { setTimeout(() => { e(a.dokanOrderWrapper).css("opacity", 1) }, 500) } }) })) }, a.customWithDrawArea = function () { if (yay_dokan_data.last_payment_details) { let t = e(".dokan-panel.dokan-panel-default .dokan-w8"); t.each(function (a) { 1 == a && e(this).find("p").html(yay_dokan_data.last_payment_details) }) } yay_dokan_data.withdraw_approved_requests_page && "yes" === yay_dokan_data.withdraw_approved_requests_page && a.customApprovedWithdrawRequest(e(".dokan-withdraw-area .dokan-table.dokan-table-striped tbody")), yay_dokan_data.withdraw_cancelled_requests_page && "yes" === yay_dokan_data.withdraw_cancelled_requests_page && a.customCancelled_WithdrawRequest(e(".dokan-withdraw-area .dokan-table.dokan-table-striped tbody")) }, a.addNewProductAction = function () { e(document).on("click", ".dokan-add-new-product", function () { a.customProductArea("add-product") }) }, a.customProductArea = function (t = "edit-product") { let r = e(a.regularPrice), n = e(a.salePrice); if (r.length > 0 && yay_dokan_data.yay_dokan_regular_price) { let o = r.closest(".dokan-input-group"), d = o.closest("edit-product" === t ? ".regular-price" : ".content-half-part"); d.append(yay_dokan_data.yay_dokan_regular_price), e(document).on("input", a.regularPrice, function (t) { t.preventDefault(); let r = e(this).val(); a.customApproximatelyPrice(r, e(".yay-dokan-regular-price-wrapper")) }), e(a.regularPrice).trigger("input") } if (n.length > 0 && yay_dokan_data.yay_dokan_sale_price) { let c = n.closest(".dokan-input-group"), i = c.closest(".sale-price"); i.append(yay_dokan_data.yay_dokan_sale_price), e(document).on("input", a.salePrice, function (t) { t.preventDefault(); let r = e(this).val(); a.customApproximatelyPrice(r, e(".yay-dokan-sale-price-wrapper")) }), e(a.salePrice).trigger("input") } }, a.customCouponsArea = function (t = "edit-coupon") { if (yay_dokan_data.default_symbol && e('label[for="amount"]').append(" (" + yay_dokan_data.default_symbol + ")"), a.approximatelyPrice) { let r = "#coupon_amount"; e(r).closest(".dokan-w5").append(yay_dokan_data.yay_dokan_coupon_amount), e(document).on("input", r, function (t) { t.preventDefault(); let r = e(this).val(); a.customApproximatelyPrice(r, e(".yay-dokan-coupon-amount-wrapper")) }), e(r).trigger("input") } }, a.customApproximatelyPrice = function (a, t) { e.ajax({ url: yay_dokan_data.ajax_url, type: "POST", data: { action: "yay_dokan_custom_approximately_price", _price: a, _nonce: yay_dokan_data.nonce }, beforeSend: function (e) { }, success: function e(a) { a.success ? t.html(a.data.price_html) : t.html("") } }) }, a.customReportStatementArea = function () { yay_dokan_data.yay_dokan_report_statement_page && e.ajax({ url: yay_dokan_data.ajax_url, type: "POST", data: { action: "yay_dokan_custom_reports_statement", seller_id: yay_dokan_data.seller_id, start_date: yay_dokan_data.yay_dokan_report_statement_from, end_date: yay_dokan_data.yay_dokan_report_statement_to, opening_balance: yay_dokan_data.yay_dokan_report_statement_opening_balance, _nonce: yay_dokan_data.nonce }, beforeSend: function (a) { e(".dokan-report-wrap").css("opacity", .4) }, success: function a(t) { if (e(".dokan-report-wrap").css("opacity", 1), t.success && t.data.statements) { let r = e(".dokan-report-wrap table.table-striped tbody tr"), n = e(".dokan-report-wrap table.table-striped tbody tr:last td"), o = r.length; r.each(function (a) { let r = e(this).find("td"); ("yes" !== yay_dokan_data.yay_dokan_report_statement_opening_balance || 0 !== a) && o - 1 !== a && (e(r[4]).html(t.data.statements[a].debit), e(r[5]).html(t.data.statements[a].credit), e(r[6]).html(t.data.total_balance)) }), e(n[4]).find("b").html(t.data.total_debit), e(n[5]).find("b").html(t.data.total_credit), e(n[6]).find("b").html(t.data.total_balance) } } }) }, a.customApprovedWithdrawRequest = function (a) { e.ajax({ url: yay_dokan_data.ajax_url, type: "POST", data: { action: "yay_dokan_custom_approved_withdraw_request", seller_id: yay_dokan_data.seller_id, _nonce: yay_dokan_data.nonce }, beforeSend: function (e) { a.css("opacity", .6) }, success: function e(t) { a.css("opacity", 1), t.success ? a.html(t.data.html) : a.html("") } }) }, a.customCancelled_WithdrawRequest = function (a) { e.ajax({ url: yay_dokan_data.ajax_url, type: "POST", data: { action: "yay_dokan_custom_cancelled_withdraw_request", seller_id: yay_dokan_data.seller_id, _nonce: yay_dokan_data.nonce }, beforeSend: function (e) { a.css("opacity", .6) }, success: function e(t) { a.css("opacity", 1), t.success ? a.html(t.data.html) : a.html("") } }) }, a.getValueinParam = function (e, a) { let t = new URL(a); return t.searchParams.get(e) } }; jQuery(document).ready(function (e) { new a().init() }) }(jQuery);