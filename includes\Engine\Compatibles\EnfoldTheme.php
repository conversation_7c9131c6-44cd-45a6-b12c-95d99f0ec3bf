<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\Helper;
defined( 'ABSPATH' ) || exit;

class EnfoldTheme {

	use SingletonTrait;

	private $apply_currency = array();
	private $is_dis_checkout_diff_currency;

	public function __construct() {

		if ( 'enfold' !== wp_get_theme()->template ) {
			return;
		}

		$this->apply_currency                = YayCurrencyHelper::detect_current_currency();
		$this->is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $this->apply_currency );

		if ( $this->is_dis_checkout_diff_currency ) {
			add_action( 'woocommerce_email_before_order_table', array( $this, 'set_order_id' ), 9, 4 );
			add_filter( 'woocommerce_price_format', array( $this, 'change_price_format' ), 9999, 2 );
			add_filter( 'wc_price_args', array( $this, 'custom_wc_price_args' ), 9999, 1 );
			add_filter( 'woocommerce_get_price_html', array( $this, 'custom_price_html' ), 9999, 2 );
		}

	}

	public function set_order_id( $order, $sent_to_admin, $plain_text, $email ) {
		$order_id                                = $order->get_id();
		$_REQUEST['yay_currency_email_order_id'] = $order_id;
	}

	public function change_price_format( $format, $currency_position ) {

		if ( isset( $_REQUEST['yay_currency_email_order_id'] ) ) {
			$order_id       = sanitize_text_field( $_REQUEST['yay_currency_email_order_id'] );
			$order_id       = intval( $order_id );
			$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id );

			return Helper::change_price_format( $order_currency, $format );
		}

		return $format;
	}

	public function custom_wc_price_args( $args ) {
		if ( isset( $_REQUEST['yay_currency_email_order_id'] ) ) {
			$order_id       = sanitize_text_field( $_REQUEST['yay_currency_email_order_id'] );
			$order_id       = intval( $order_id );
			$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id );
			return YayCurrencyHelper::get_apply_currency_format_info( $order_currency );
		}

		return $args;
	}

	public function custom_price_html( $price_html, $product ) {
		if ( is_checkout() ) {
			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $price_html;
			}

			$product_price              = $product->get_price();
			$original_product_subtotal  = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $product_price, 1 );
			$converted_product_subtotal = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['current_currency'], $product_price, 1 );
			$product_fixed_price        = FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $this->apply_currency );
			if ( $product_fixed_price ) {
				$converted_product_subtotal = YayCurrencyHelper::format_price( $product_fixed_price );
			}
			$converted_product_subtotal_html = YayCurrencyHelper::converted_approximately_html( $converted_product_subtotal );
			$price_html                      = $original_product_subtotal . $converted_product_subtotal_html;
		}
		return $price_html;
	}
}
