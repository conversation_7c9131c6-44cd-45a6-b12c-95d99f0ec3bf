"use strict"; !function (e) { let r = () => { window.history.replaceState && window.history.replaceState(null, null, window.location.href) }; window.yayCurrencyHooks = { addFilter: function (e, r) { YayCurrency_Callback.Helper.addHook("filters", e, r) }, applyFilters: YayCurrency_Callback.Helper.applyFilters, addAction: function (e, r) { YayCurrency_Callback.Helper.addHook("actions", e, r) }, doAction: YayCurrency_Callback.Helper.doAction }, jQuery(document).ready(function (e) { r(e); let { yayCurrency: o } = window, c = YayCurrency_Callback.Helper.getCookie(o.cookie_name); yayCurrencyHooks.doAction("yayCurrencyCompatibleThirdParty", [{ currencyID: c }]), YayCurrency_Callback.Helper.handleFilterByPrice(c), Yay<PERSON>urrency_Callback.Helper.forcePaymentOnCheckoutPage(c), YayCurrency_Callback.Helper.forcePaymentOnCartPage(c), e(document.body).trigger("wc_fragment_refresh"), e(window).on("load resize scroll", YayCurrency_Callback.Helper.switcherUpwards()), o.yay_currency_use_params && o.yay_currency_param__name && c && YayCurrency_Callback.Helper.setCookie(o.cookie_switcher_name ?? "yay_currency_do_change_switcher", c, 1), YayCurrency_Callback.Helper.switcherAction(), YayCurrency_Callback.Helper.currencyConverter(), YayCurrency_Callback.Helper.wooCommerceBlocksForcePayment(c), "function" == typeof YayCurrency_Callback.Helper.approximatePriceCheckoutBlocks && "yes" === o.show_approximate_price && YayCurrency_Callback.Helper.approximatePriceCheckoutBlocks(c) }) }(jQuery);