
(function ($, window) {
    YayCurrency_Callback = window.YayCurrency_Callback || {};

    yay_currency_data_args = {
        common_data_args: {
            yayCurrencySymbolWrapper: 'span.woocommerce-Price-currencySymbol',
            yayCurrencySwitcher: '.yay-currency-single-page-switcher',
            yayCountryCurrency: '.yay-currency-country-currency-notice-wrapper',
            yayCurrencyLoading: 'yay-currency-cache-loading',
            yayCurrencyHide: 'yay-currency-force-payment-hide',
            yayCurrencyWidget: '.yay-currency-widget-switcher',
            yayCurrencyMenu: '.yay-currency-menu-shortcode-switcher',
            yayCurrencyShortcode: '.yay-currency-shortcode-switcher',
            yayCurrencyBlock: '.yay-currency-block-switcher',
            yayCurrencyShortcodePriceHtml: '.yay-currency-shortcode-price-html-wrapper',
            yayCurrencyShortcodeProductPriceHtml: '.yay-currency-shortcode-product-price-html-wrapper',
        },
        converter_args: {
            converterWrapper: '.yay-currency-converter-container',
            converterAmount: '.yay-currency-converter-amount',
            converterFrom: '.yay-currency-converter-from-currency',
            converterTo: '.yay-currency-converter-to-currency',
            converterResultWrapper: '.yay-currency-converter-result-wrapper',
            converterResultAmount: '.yay-currency-converter-amount-value',
            converterResultFrom: '.yay-currency-converter-from-currency-code',
            converterResultValue: '.yay-currency-converter-result-value',
            converterResultTo: '.yay-currency-converter-to-currency-code',
        },
        caching_data_args: {
            yayProductId: '.yay-currency-cache-product-id',
            yayVariationId: '.variations_form',
            yayDataProductId: 'yay_currency-product-id',
            yayCurrencyMiniCartContents: yay_callback_data.minicart_contents_class,
            yayCurrencyProduct: '.yay-currency-single-product-switcher',
        },
        switcher_data_args: {
            activeClass: 'active',
            upwardsClass: 'upwards',
            openClass: 'open',
            selectedClass: 'selected',
            currencySwitcher: '.yay-currency-switcher',
            currencyFlag: '.yay-currency-flag',
            currencySelectedFlag: '.yay-currency-flag.selected',
            customLoader: '.yay-currency-custom-loader',
            customOption: '.yay-currency-custom-options',
            customArrow: '.yay-currency-custom-arrow',
            customOptionArrow: '.yay-currency-custom-option-row',
            customOptionArrowSelected: '.yay-currency-custom-option-row.selected',
            selectTrigger: '.yay-currency-custom-select__trigger',
            selectWrapper: '.yay-currency-custom-select-wrapper',
            customSelect: '.yay-currency-custom-select',
            selectedOption: '.yay-currency-custom-select__trigger .yay-currency-selected-option',
        },
        blocks_data_args: {
            checkout: ".wp-block-woocommerce-checkout[data-block-name='woocommerce/checkout']",
            totalCheckout: '.wp-block-woocommerce-checkout .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-totals-footer-item',
            cart: ".wp-block-woocommerce-cart[data-block-name='woocommerce/cart']",
            totalCart: '.wp-block-woocommerce-cart .wp-block-woocommerce-cart-order-summary-block .wc-block-components-totals-footer-item',
            payments: {
                container: '.wc-block-checkout__payment-method#payment-method',
                options: 'input.wc-block-components-radio-control__input[name="radio-control-wc-payment-method-options"]',
                localStorageName: 'YayCurrencySelectedPaymentMethod',
                class: {
                    parent: '.wc-block-components-radio-control-accordion-option',
                    labelOption: 'label.wc-block-components-radio-control__option',
                }
            },
            filterPrice: {
                class: {
                    wrapper: '.wp-block-woocommerce-price-filter',
                    controls: '.wc-block-price-filter__controls',
                    filterSlideInput: '.wp-block-woocommerce-filter-wrapper[data-filter-type="price-filter"] .wc-block-price-slider input',
                    minPriceWrapper: '.wc-block-price-filter__range-input--min',
                    maxPriceWrapper: '.wc-block-price-filter__range-input--max',
                    minPriceInput: 'input.wc-block-price-filter__amount--min',
                    maxPriceInput: 'input.wc-block-price-filter__amount--max',
                    resetButton: '.wc-block-components-filter-reset-button',
                    progressRange: '.wc-block-price-filter__range-input-progress',
                }
            }
        },
        cookies_data_args: {
            cartBlocks: 'yay_cart_blocks_page',
            checkoutBlocks: 'yay_checkout_blocks_page',
            blocksForceNoticeHtml: 'yay_blocks_force_notice',
            forcePaymentHtml: 'yay_fore_payment_notice_html'
        }
    }

    YayCurrency_Callback.yay_force_payment_data_args = Object.assign({}, yay_currency_data_args.common_data_args, {
        yayCurrencyForcePaymentNotice: '.yay-currency-checkout-force-payment-notice'
    });

    YayCurrency_Callback.yay_caching_data_args = Object.assign({}, yay_currency_data_args.common_data_args, yay_currency_data_args.caching_data_args);

    // Define Hooks
    var yay_currency_hooks = {
        actions: {},
        filters: {}
    };

    YayCurrency_Callback.Helper = {
        // Define Hooks
        addHook: function (hookType, hookName, callback) {
            if (!yay_currency_hooks[hookType][hookName]) {
                yay_currency_hooks[hookType][hookName] = [];
            }
            yay_currency_hooks[hookType][hookName].push(callback);
        },
        // Filters
        applyFilters: function (hookName, value) {
            if (yay_currency_hooks.filters[hookName]) {
                yay_currency_hooks.filters[hookName].forEach(function (callback) {
                    value = callback(value);
                });
            }
            return value;
        },
        // Action
        doAction: function (hookName, args) {
            if (yay_currency_hooks.actions[hookName]) {
                yay_currency_hooks.actions[hookName].forEach(function (callback) {
                    callback.apply(null, args);
                });
            }
        },
        // Cookie
        setCookie: function (cname, value, days) {
            var expires = "";
            if (days) {
                var date = new Date();
                date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
                expires = "; expires=" + date.toUTCString();
            }
            document.cookie = cname + "=" + (value || "") + expires + "; path=/";
        },
        getCookie: function (cname) {
            let name = cname + '=';
            let decodedCookie = decodeURIComponent(document.cookie);
            let ca = decodedCookie.split(';');
            for (let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') {
                    c = c.substring(1);
                }
                if (c.indexOf(name) == 0) {
                    return c.substring(name.length, c.length);
                }
            }
            return '';
        },
        deleteCookie: function (cname) {
            YayCurrency_Callback.Helper.setCookie(cname, '', -1);
        },
        getCurrentCurrency: function (currency_id = false) {
            currency_id = currency_id ? currency_id : YayCurrency_Callback.Helper.getCookie(window.yayCurrency.cookie_name);
            let currentCurrency = false;
            if (window.yayCurrency.converted_currency) {
                window.yayCurrency.converted_currency.forEach((currency) => {
                    if (currency.ID === +currency_id) {
                        currentCurrency = currency;
                    }
                });
            }
            return yayCurrencyHooks.applyFilters('yayCurrencyGetCurrentCurrency', currentCurrency);
        },
        getCurrencyIDbyCurrencyName: function (currency_name) {
            let currentCurrency = false;
            if (window.yayCurrency.converted_currency) {
                window.yayCurrency.converted_currency.forEach((currency) => {
                    if (currency.currency === currency_name) {
                        currentCurrency = currency;
                    }
                });
            }
            return currentCurrency ? currentCurrency.ID : false;
        },
        getRateFeeByCurrency: function (current_currency = false) {
            current_currency = current_currency ? current_currency : YayCurrency_Callback.Helper.getCurrentCurrency();
            let rateFee = parseFloat(current_currency.rate);
            if ('percentage' === current_currency.fee.type) {
                rateFee = parseFloat(current_currency.rate) + parseFloat(current_currency.rate) * (parseFloat(current_currency.fee.value) / 100);
            } else {
                rateFee = parseFloat(current_currency.rate) + parseFloat(current_currency.fee.value);
            }

            return yayCurrencyHooks.applyFilters('yayCurrencyGetRateFeeByCurrency', rateFee);

        },
        // Common
        getBlockData: function () {
            let blocks = [];
            const yaySwitcherBlocks = $(yay_currency_data_args.common_data_args.yayCurrencyBlock);
            if (yaySwitcherBlocks.length) {
                yaySwitcherBlocks.each(function () {
                    var switcherObj = {
                        isBlockID: $(this).data('block-id'),
                        isShowFlag: $(this).data('show-flag'),
                        isShowCurrencyName: $(this).data('show-currency-name'),
                        isShowCurrencySymbol: $(this).data('show-currency-symbol'),
                        isShowCurrencyCode: $(this).data('show-currency-code'),
                        widgetSize: $(this).data('switcher-size')
                    };
                    blocks.push(switcherObj);
                });
            }
            return yayCurrencyHooks.applyFilters('yayCurrencyGetBlockData', blocks);
        },
        customResponseCommon: function (res) {
            let detect_change_switcher = false;
            /*change current currency In widget*/
            if (res.data.widget_content) {
                detect_change_switcher = true;
                $(yay_currency_data_args.common_data_args.yayCurrencyWidget).html(res.data.widget_content);
            }

            /*change current currency In menu*/
            if (res.data.menu_content) {
                detect_change_switcher = true;
                $(yay_currency_data_args.common_data_args.yayCurrencyMenu).html(res.data.menu_content);
            }

            /*change current currency In block shortcode*/
            if (res.data.shortcode_content) {
                detect_change_switcher = true;
                $(yay_currency_data_args.common_data_args.yayCurrencyShortcode).html(res.data.shortcode_content);
            }

            /*change current currency In gutenberg*/
            if (res.data.block_content) {
                detect_change_switcher = true;
                $(yay_currency_data_args.common_data_args.yayCurrencyBlock).each(function () {
                    const blockId = $(this).data('block-id');
                    if (blockId) {
                        $(this).html(res.data.block_content[blockId]);
                    }
                });
            }

            /*change country notice text area*/
            if (res.data.country_notice) {
                $(yay_currency_data_args.common_data_args.yayCountryCurrency).html(res.data.country_notice);
            }

            /*change price html when use shortcode*/
            if (res.data.shortcode_price_html) {
                $(yay_currency_data_args.common_data_args.yayCurrencyShortcodePriceHtml).each(function (index, price_html) {
                    if (res.data.shortcode_price_html[index]) {
                        $(price_html).html(res.data.shortcode_price_html[index])
                    }
                });
            }

            /*change product price html when use shortcode*/
            if (res.data.shortcode_product_price_html) {
                $(yay_currency_data_args.common_data_args.yayCurrencyShortcodeProductPriceHtml).each(function (index, price_html) {
                    if (res.data.shortcode_product_price_html[index]) {
                        $(price_html).html(res.data.shortcode_product_price_html[index])
                    }
                });
            }

            if (detect_change_switcher) {
                // Resize the scroll again because using AJAX overrides the old data.
                $(window).on('load resize scroll', YayCurrency_Callback.Helper.switcherUpwards());
            }

            yayCurrencyHooks.doAction('yayCurrencyResponseCommon', [{ response: res }]);

        },
        blockLoading: function (element) {
            $(element).addClass('processing').block({
                message: null,
                overlayCSS: {
                    background: '#fff',
                    opacity: 0.6
                }
            });
            yayCurrencyHooks.doAction('yayCurrencyCustomBlockLoading', [{ data: element }]);
        },
        unBlockLoading: function (element) {
            $(element).removeClass('processing').unblock();
            yayCurrencyHooks.doAction('yayCurrencyCustomUnBlockLoading', [{ data: element }]);
        },
        // Switcher Dropdown
        switcherUpwards: function () {
            const allSwitcher = $(yay_currency_data_args.common_data_args.yayCurrencySwitcher);

            allSwitcher.each(function () {
                const SWITCHER_LIST_HEIGT = 250;

                const offsetTop =
                    $(this).offset().top + $(this).height() - $(window).scrollTop();

                const offsetBottom =
                    $(window).height() -
                    $(this).height() -
                    $(this).offset().top +
                    $(window).scrollTop();

                if (
                    offsetBottom < SWITCHER_LIST_HEIGT &&
                    offsetTop > SWITCHER_LIST_HEIGT
                ) {
                    $(this).find(yay_currency_data_args.switcher_data_args.customOption).addClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                    $(this).find(yay_currency_data_args.switcher_data_args.customArrow).addClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                    $(this)
                        .find(yay_currency_data_args.switcher_data_args.selectTrigger)
                        .addClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                } else {
                    $(this).find(yay_currency_data_args.switcher_data_args.customOption).removeClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                    $(this).find(yay_currency_data_args.switcher_data_args.customArrow).removeClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                    $(this)
                        .find(yay_currency_data_args.switcher_data_args.selectTrigger)
                        .removeClass(yay_currency_data_args.switcher_data_args.upwardsClass);
                }
            });

            yayCurrencyHooks.doAction('yayCurrencyCustomSwitcherDropdown', [{ data: allSwitcher }]);

        },
        switcherAction: function () {
            const switcher_args = yay_currency_data_args.switcher_data_args;
            $(document).on('click', switcher_args.selectWrapper, function () {
                $(switcher_args.customSelect, this).toggleClass(switcher_args.openClass);
                $('#slide-out-widget-area')
                    .find(switcher_args.customOption)
                    .toggleClass('overflow-fix');
                $('[id^=footer]').toggleClass('z-index-fix');
                $(switcher_args.customSelect, this)
                    .parents('.handheld-navigation')
                    .toggleClass('overflow-fix');
            });

            $(document).on('click', switcher_args.customOptionArrow, function () {
                let currencyID = $(this).data('value') ? $(this).data('value') : $(this).data('currency-id');
                if (!currencyID) {
                    const className = $(this).attr('class');
                    const match = className.match(/yay-currency-id-(\d+)/);
                    if (match) {
                        currencyID = match[1];
                        YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name ?? 'yay_currency_widget', currencyID, 1);
                        location.reload();
                    }
                }
                const countryCode = $(this)
                    .children(switcher_args.currencyFlag)
                    .data('country_code');
                YayCurrency_Callback.Helper.refreshCartFragments();
                $(switcher_args.currencySwitcher).val(currencyID).change();
                YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name ?? 'yay_currency_do_change_switcher', currencyID, 1);
                if (!$(this).hasClass(switcher_args.selectedClass)) {
                    const clickedSwitcher = $(this).closest(switcher_args.customSelect);

                    $(this)
                        .parent()
                        .find(switcher_args.customOptionArrowSelected)
                        .removeClass(switcher_args.selectedClass);

                    $(this).addClass(switcher_args.selectedClass);

                    clickedSwitcher.find(switcher_args.currencySelectedFlag).css({
                        background: `url(${yayCurrency.yayCurrencyPluginURL}assets/dist/flags/${countryCode}.svg)`,
                    });

                    clickedSwitcher.find(switcher_args.selectedOption).text($(this).text());

                    clickedSwitcher.find(switcher_args.customLoader).addClass(switcher_args.activeClass);

                    clickedSwitcher.find(switcher_args.customArrow).hide();
                }
            });

            window.addEventListener('click', function (e) {
                const selects = document.querySelectorAll(yay_currency_data_args.switcher_data_args.customSelect);
                selects.forEach((select) => {
                    if (!select.contains(e.target)) {
                        select.classList.remove(yay_currency_data_args.switcher_data_args.openClass);
                    }
                });
            });
            yayCurrencyHooks.doAction('yayCurrencyCustomSwitcherAction', [{ data: switcher_args }]);
        },
        refreshCartFragments: function () {
            if (typeof wc_cart_fragments_params !== 'undefined' && wc_cart_fragments_params !== null) {
                sessionStorage.removeItem(wc_cart_fragments_params.fragment_name);
            }
            yayCurrencyHooks.doAction('yayCurrencyCustomRefreshCartFragments', []);
        },
        // Force Payment Currency
        getListDataForcePayment: function () {
            const yay_data_args = YayCurrency_Callback.yay_force_payment_data_args;
            let data = {
                action: 'yayCurrency_handle_force_payment_response',
                blocks: YayCurrency_Callback.Helper.getBlockData(),
                widget: $(yay_data_args.yayCurrencyWidget).length,
                menu: $(yay_data_args.yayCurrencyMenu).length,
                shortcode: $(yay_data_args.yayCurrencyShortcode).length,
                country_notice: $(yay_data_args.yayCountryCurrency).length,
                country_code: yayCurrency.country_code,
                foce_payment_notice: $(yay_data_args.yayCurrencyForcePaymentNotice).length,
                is_cart_page: yayCurrency.cart_page,
                nonce: yayCurrency.nonce
            };

            if ($(yay_data_args.yayCurrencyShortcodePriceHtml).length) {
                let shortcode_default_prices = [];
                $(yay_data_args.yayCurrencyShortcodePriceHtml).each(function (index, price) {
                    shortcode_default_prices[index] = $(price).data('default-price')
                });
                data.shortcode_default_price = shortcode_default_prices;
            }

            if ($(yay_data_args.yayCurrencyShortcodeProductPriceHtml).length) {
                let shortcode_product_ids = [];
                $(yay_data_args.yayCurrencyShortcodeProductPriceHtml).each(function (index, price) {
                    shortcode_product_ids[index] = $(price).data('shortcode-product-id');
                });
                data.shortcode_product_ids = shortcode_product_ids;
            }

            return yayCurrencyHooks.applyFilters('yayCurrencyGetDataForcePayment', data);

        },
        customResponseForcePayment: function (res) {
            if (res.success) {
                YayCurrency_Callback.Helper.customResponseCommon(res);
                /*change current currency In force payment notice*/
                if (res.data.force_payment_is_dis_checkout_diff_currency) {
                    if ($('.yay-currency-checkout-notice-force-payment-wrapper').length) {
                        $('.yay-currency-checkout-notice-force-payment-wrapper').html(res.data.force_payment_checkout_notice_html);
                    }
                } else {
                    if ($('.yay-currency-checkout-notice-force-payment-wrapper').length) {
                        $('.yay-currency-checkout-notice-force-payment-wrapper').html('');
                    }
                }

                if ('undefined' != res.data.force_payment_notice_html) {
                    const yay_data_args = YayCurrency_Callback.yay_force_payment_data_args;
                    if ($(yay_data_args.yayCurrencyForcePaymentNotice).length) {
                        const force_payment_html_notice = yay_currency_data_args.cookies_data_args.forcePaymentHtml;

                        if ('' != res.data.force_payment_notice_html && !res.data.force_payment_checkout_notice_html) {
                            let payment_notice_html = res.data.force_payment_notice_html
                            $(yay_data_args.yayCurrencyForcePaymentNotice).removeClass(yay_data_args.yayCurrencyHide);
                            $(yay_data_args.yayCurrencyForcePaymentNotice).html(payment_notice_html);

                            if (res.data.reload_page) {
                                YayCurrency_Callback.Helper.setCookie(force_payment_html_notice, payment_notice_html, +yayCurrency.cookie_lifetime_days);
                            }

                        } else {
                            const force_payment_notice = YayCurrency_Callback.Helper.getCookie(force_payment_html_notice);

                            if (!force_payment_notice) {
                                $(yay_data_args.yayCurrencyForcePaymentNotice).remove(); // Not show again when go back country first
                            } else {
                                YayCurrency_Callback.Helper.deleteCookie(force_payment_html_notice);
                            }
                        }

                    }

                }

                // Set Switcher Cookie again
                if (YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_switcher_name)) {
                    YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name));
                }

                yayCurrencyHooks.doAction('yayCurrencyResponseForcePayment', [{ response: res }]);

                if (res.data.reload_page) {
                    location.reload();
                }

            }
        },
        customResponseForceCurrencyByPaymentMethod: function (res) {
            if (res.success) {
                YayCurrency_Callback.Helper.customResponseCommon(res);
                /*change current currency In force payment notice*/
                if (res.data.force_payment_is_dis_checkout_diff_currency) {
                    if ($('.yay-currency-checkout-notice-force-payment-wrapper').length) {
                        $('.yay-currency-checkout-notice-force-payment-wrapper').html(res.data.force_payment_checkout_notice_html);
                    }
                } else {
                    if ($('.yay-currency-checkout-notice-force-payment-wrapper').length) {
                        $('.yay-currency-checkout-notice-force-payment-wrapper').html('');
                    }
                }

                // Set Switcher Cookie again
                if (YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_switcher_name)) {
                    YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name));
                }

                yayCurrencyHooks.doAction('yayCurrencyResponseForceCurrencyByPaymentMethod', [{ response: res }]);

                if (res.data.reload_page) {
                    location.reload();
                }

            }
        },
        forcePaymentCountryCurrency: function (data, $is_blocks = false) {
            $.ajax({
                url: yayCurrency.ajaxurl,
                type: 'POST',
                data: data,
                beforeSend: function (res) {
                    // Loading Switcher
                    YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher);
                    YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);

                },
                xhrFields: {
                    withCredentials: true
                },
                success: function success(res) {
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher);
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                    YayCurrency_Callback.Helper.customResponseForcePayment(res);
                    if ($is_blocks) {

                        if (res.data.force_payment_blocks_checkout_notice_html && '' != res.data.force_payment_blocks_checkout_notice_html) {
                            $(yay_currency_data_args.blocks_data_args.checkout).before(res.data.force_payment_blocks_checkout_notice_html);
                            $('.yay-currency-checkout-notice-current-currency').text(res.data.force_payment_blocks_current_currency);
                        }
                        $(document.body).trigger('wc_fragment_refresh');
                        if (res.data.force_payment_blocks_cart_subtotal_fallback) {
                            YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name));
                        }

                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher);
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                    console.log("Error responseText: ", xhr.responseText);
                }
            });
        },
        forceCurrencyByPaymentMethodSelected: function (data, $is_blocks = false) {
            $.ajax({
                url: yayCurrency.ajaxurl,
                type: 'POST',
                data: data,
                beforeSend: function (res) {
                    YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout);
                },
                xhrFields: {
                    withCredentials: true
                },
                success: function success(res) {
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout);
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                    YayCurrency_Callback.Helper.customResponseForceCurrencyByPaymentMethod(res);
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher);
                    YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                    console.log("Error responseText: ", xhr.responseText);
                }
            });
        },
        forcePaymentOnCheckoutPage: function (oldCurrencyID) {
            if (typeof wc_checkout_params !== 'undefined') {
                if (yayCurrency.force_payment) {
                    // Force Payment Currency in Checkout page
                    $(document.body).on('updated_checkout', function (e) {
                        // Recalculate Cart
                        $(document.body).trigger('wc_fragment_refresh');
                        YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(oldCurrencyID);
                    });
                }
                if (yayCurrency.force_currency) {

                    // Force Currency by Payment Method Selected
                    $(document.body).on('payment_method_selected', function (event, response) {
                        $(document.body).trigger('update_checkout');
                    });

                    $(document.body).on('updated_checkout', function (e) {
                        // Recalculate Cart
                        $(document.body).trigger('wc_fragment_refresh');
                        YayCurrency_Callback.Helper.reRenderHTMLAfterForceCurrencyByPaymentMethod(oldCurrencyID);
                    });

                } else {
                    if (localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName)) {
                        //Remove item from localStorage if Force Currency is not set
                        localStorage.removeItem(yay_currency_data_args.blocks_data_args.payments.localStorageName);
                    }
                }
            }
        },
        forcePaymentOnCartPage: function (oldCurrencyID) {
            if (yayCurrency.force_payment && '1' === yayCurrency.cart_page) {
                // Force Payment Currency in Cart page
                $(document.body).on('updated_cart_totals', function (e) {
                    const newCurrencyID = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name)
                    if (newCurrencyID !== oldCurrencyID) {
                        YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(oldCurrencyID);
                    }
                });
            }

        },
        reRenderHTMLAfterForceCountryCode: function (oldCurrencyID, $is_blocks = false) {
            let data = YayCurrency_Callback.Helper.getListDataForcePayment();
            data.yay_currency_old = oldCurrencyID;
            if ($is_blocks) {
                data.woocommerce_blocks = 'yes';
            } else {
                data.yay_currency_new = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name);
            }

            YayCurrency_Callback.Helper.forcePaymentCountryCurrency(data, $is_blocks);
        },
        reRenderHTMLAfterForceCurrencyByPaymentMethod: function (oldCurrencyID, $is_blocks = false) {
            var selectedPaymentMethod = $('input[name="payment_method"]:checked').val();
            let data = YayCurrency_Callback.Helper.getListDataForcePayment();
            data.action = "yayCurrency_handle_force_currency_by_payment_selected_response";
            data.yay_currency_old = oldCurrencyID;
            data.yay_currency_new = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name);
            data.payment_method_id = selectedPaymentMethod;

            YayCurrency_Callback.Helper.forceCurrencyByPaymentMethodSelected(data, $is_blocks);

        },
        // About Caching 
        detectAllowCaching: function (allow) {
            return yayCurrencyHooks.applyFilters('yayCurrencyDetectAllowCaching', allow);
        },
        getListProductIdsAvailable: function () {
            let productIds = [];
            let simpleProductIds = $(yay_currency_data_args.caching_data_args.yayProductId);
            if (simpleProductIds.length) {
                simpleProductIds.each(function (index, element) {
                    const productId = $(element).data(yay_currency_data_args.caching_data_args.yayDataProductId);
                    if (productId) {
                        productIds.push(productId);
                    }
                });
            }

            let variationProductIds = $(yay_currency_data_args.caching_data_args.yayVariationId);
            if (variationProductIds.length) {
                variationProductIds.each(function (index, variation) {
                    let data = $(variation).data('product_variations');
                    if (data.length) {
                        data.forEach((element) => {
                            productIds.push(element.variation_id);
                        });
                    }
                });
            }
            return yayCurrencyHooks.applyFilters('yayCurrencyGetListProductIds', productIds);
        },
        getListDataCaching: function (prices = false, productIds = []) {
            const data_args = YayCurrency_Callback.yay_caching_data_args;
            let data = {
                action: prices ? 'yay_caching_get_price_html' : 'yay_caching_generate_currency_switcher_html',
                blocks: YayCurrency_Callback.Helper.getBlockData(),
                widget: $(data_args.yayCurrencyWidget).length,
                product: $(data_args.yayCurrencyProduct).length,
                menu: $(data_args.yayCurrencyMenu).length,
                shortcode: $(data_args.yayCurrencyShortcode).length,
                country_notice: $(data_args.yayCountryCurrency).length,
                ip_address: yay_currency_caching_data.ip_address,
                cache_compatible: yayCurrency.cache_compatible ?? '0',
                _nonce: yay_currency_caching_data.nonce,
            };

            // detect by currency param
            const queryString = window.location.search;
            const urlParams = new URLSearchParams(queryString);
            const currencyCode = urlParams.get('currency');

            if (currencyCode) {
                data.currency_code_param = currencyCode;
            }


            if ($(data_args.yayCurrencyShortcodePriceHtml).length) {
                let shortcode_default_prices = [];
                $(data_args.yayCurrencyShortcodePriceHtml).each(function (index, price) {
                    shortcode_default_prices[index] = $(price).data('default-price');
                });
                data.shortcode_default_price = shortcode_default_prices;
            }

            if ($(data_args.yayCurrencyShortcodeProductPriceHtml).length) {
                let shortcode_product_ids = [];
                $(data_args.yayCurrencyShortcodeProductPriceHtml).each(function (index, price) {
                    shortcode_product_ids[index] = $(price).data('shortcode-product-id');
                });
                data.shortcode_product_ids = shortcode_product_ids;
            }

            if (prices) {
                data.productIds = productIds;
            }

            return yayCurrencyHooks.applyFilters('yayCurrencyGetListDataCaching', data);

        },
        resetCachingLoading: function () {
            const yay_wrapper_args = [yay_currency_data_args.common_data_args.yayCurrencySwitcher, yay_currency_data_args.common_data_args.yayCountryCurrency, yay_currency_data_args.common_data_args.yayCurrencyShortcodePriceHtml, yay_currency_data_args.common_data_args.yayCurrencyShortcodeProductPriceHtml, yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents];
            $.each(yay_wrapper_args, function (index, element) {
                if ($(element).length && $(element).hasClass(yay_currency_data_args.common_data_args.yayCurrencyLoading)) {
                    $(element).removeClass(yay_currency_data_args.common_data_args.yayCurrencyLoading);
                }
            });
            yayCurrencyHooks.doAction('yayCurrencyResetCachingLoading', [{ wrapper_args: yay_wrapper_args }]);
        },
        // WooCommerce Blocks: Cart, Checkout pages
        detectCheckoutBlocks: function () {
            var flag = false;
            if ($(yay_currency_data_args.blocks_data_args.checkout).length) {
                flag = true;
            }
            return yayCurrencyHooks.applyFilters('yayCurrencyDetectCheckoutBlocks', flag);
        },
        detectCartBlocks: function () {
            var flag = false;

            if ($(yay_currency_data_args.blocks_data_args.cart).length) {
                flag = true;
            }

            return yayCurrencyHooks.applyFilters('yayCurrencyDetectCartBlocks', flag);
        },
        wooCommerceBlocksForcePayment: function (oldCurrencyID) {
            YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.cartBlocks);
            YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.checkoutBlocks);
            if (YayCurrency_Callback.Helper.detectCheckoutBlocks()) {
                YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.checkoutBlocks, 'yes', +yayCurrency.cookie_lifetime_days);

                if ($(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).length && '1' !== yayCurrency.checkout_diff_currency) {
                    YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name));
                }

                if (yayCurrency.force_payment) {
                    YayCurrency_Callback.Helper.forcePaymentOnCheckoutBlocksPage(oldCurrencyID);
                }

                if (yayCurrency.force_currency) {
                    YayCurrency_Callback.Helper.forceCurrencyByPaymentMethodOnCheckoutBlocksPage(oldCurrencyID);
                }

                if (yayCurrency.checkout_notice_html) {

                    if ('' != yayCurrency.checkout_notice_html) {
                        $(yay_currency_data_args.blocks_data_args.checkout).before(yayCurrency.checkout_notice_html);
                    }

                    YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name));
                }

            }
            if (YayCurrency_Callback.Helper.detectCartBlocks()) {
                YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.cartBlocks, 'yes', +yayCurrency.cookie_lifetime_days);
                if (yayCurrency.force_payment) {
                    YayCurrency_Callback.Helper.forcePaymentOnCartBlocksPage(oldCurrencyID);
                }
            }
            yayCurrencyHooks.doAction('yayCurrencyHandleForcePaymentBlocks', [{ data_args: yay_currency_data_args, oldCurrencyID: oldCurrencyID }]);
        },
        detectForcePaymentInitDomChanges: function (oldCurrencyID) {
            window.onload = function () {
                const totalElement = yayCurrency.total_block_class ?? (yayCurrency.cart_page ? yay_currency_data_args.blocks_data_args.totalCart : yay_currency_data_args.blocks_data_args.totalCheckout);
                var targetNode = document.querySelector(totalElement);
                if (targetNode) {
                    var config = {
                        characterData: true,
                        subtree: true
                    };
                    var callback = function (mutationsList, observer) {
                        for (var mutation of mutationsList) {
                            if (mutation.type === 'characterData') {
                                YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(oldCurrencyID, true);
                            }
                        }
                    };
                    var observer = new MutationObserver(callback);
                    observer.observe(targetNode, config);
                }
            }
        },
        forcePaymentOnCheckoutBlocksPage: function (oldCurrencyID) {
            YayCurrency_Callback.Helper.detectForcePaymentInitDomChanges(oldCurrencyID);
            // Detect currency with country
            YayCurrency_Callback.Helper.detectApplyCurrencyForceCountryCode(oldCurrencyID);
            yayCurrencyHooks.doAction('yayCurrencyHandleForcePaymentBlocksCheckoutPage', [{ data_args: yay_currency_data_args, oldCurrencyID: oldCurrencyID }]);
        },
        forcePaymentOnCartBlocksPage: function (oldCurrencyID) {
            YayCurrency_Callback.Helper.detectForcePaymentInitDomChanges(oldCurrencyID);
            yayCurrencyHooks.doAction('yayCurrencyHandleForcePaymentBlocksCartPage', [{ data_args: yay_currency_data_args, oldCurrencyID: oldCurrencyID }]);
        },
        reCalculateCartSubtotalBlocksPage: function (oldCurrencyID) {
            $(document.body).on('wc_fragments_refreshed', function () {
                $.ajax({
                    url: yayCurrency.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'yayCurrency_get_cart_subtotal_blocks',
                        nonce: yayCurrency.nonce,
                        fallback_currency_code: yayCurrency.fallback_currency_code ? yayCurrency.fallback_currency_code : ''
                    },
                    beforeSend: function (res) {
                        // Loading Switcher
                        YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);

                    },
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function success(res) {

                        YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                        if (res.success && res.data.cart_subtotal) {
                            $(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).find('.woocommerce-Price-amount.amount').html(res.data.cart_subtotal);
                        }
                        yayCurrencyHooks.doAction('yayCurrencyReCalculateCartSubtotalBlocksPage', [{ response: res }]);
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents);
                        console.log("Error responseText: ", xhr.responseText);
                    }
                });
            });
        },
        detectApplyCurrencyForceCountryCode: function (oldCurrencyID) {
            const force_notice = YayCurrency_Callback.Helper.getCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml);
            if (force_notice) {
                $(yay_currency_data_args.blocks_data_args.checkout).before(force_notice);
                YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml);
            }

            if (yayCurrency.currency_code) {
                const currencyCode = yayCurrency.currency_code;
                let applyCurrency = null;
                window.yayCurrency.converted_currency.forEach((currency) => {
                    if (currency.currency == currencyCode) {
                        applyCurrency = currency;
                        return;
                    }
                });

                if (applyCurrency && applyCurrency.ID !== +oldCurrencyID) {
                    $.ajax({
                        url: yayCurrency.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'yayCurrency_recalculate_apply_currency_from_blocks',
                            nonce: yayCurrency.nonce,
                            currencyID: oldCurrencyID,
                            currentCurrency: applyCurrency
                        },
                        beforeSend: function (res) {
                            // Loading Switcher
                            YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout);
                        },
                        xhrFields: {
                            withCredentials: true
                        },
                        success: function success(res) {

                            if (res.success) {
                                if (res.data.force_payment_notice) {
                                    YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml, res.data.force_payment_notice, +yayCurrency.cookie_lifetime_days);
                                }
                                location.reload();
                            } else {
                                YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout);
                            }

                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout);
                            console.log("Error responseText: ", xhr.responseText);
                        }
                    });
                }

            }

        },

        // Checkout Blocks: Force Currency by Payment Method [Full]
        getCurrencyIDByMethodSelected: function (paymentMethod) {

            const paymentMethodOptions = yayCurrency.force_currency.force_currency_payment_options;

            var paymentFound = jQuery.grep(paymentMethodOptions, function (element) {
                return element.id === paymentMethod;
            });

            if (paymentFound.length) {
                const currencyCode = paymentFound[0].currency[0];
                if (currencyCode && 'default' !== currencyCode) {
                    const applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(currencyCode);
                    if (applyCurrency && applyCurrency.ID) {
                        let paymentMethods = applyCurrency.paymentMethods;
                        if ('0' == applyCurrency.status) {
                            const fallBackCurrency = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(yayCurrency.fallback_currency_code);
                            if (fallBackCurrency && fallBackCurrency.ID) {
                                paymentMethods = fallBackCurrency.paymentMethods;
                            }
                        }
                        if (paymentMethods.indexOf('all') !== -1 || paymentMethods.indexOf(paymentMethod) !== -1) {
                            return applyCurrency.ID;
                        }
                    }

                }

            }
            return false;
        },
        selectedPaymentMethodExists: function (selectedPaymentMethod) {
            if (!selectedPaymentMethod) {
                return false;
            }
            const paymentMethodSelected = $(yay_currency_data_args.blocks_data_args.payments.options + '[value="' + selectedPaymentMethod + '"]');
            return paymentMethodSelected.length ? true : false;
        },
        setInitWithPaymentOptionsExists: function (paymentOptions, oldCurrencyID) {
            let selectedPaymentMethod = localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName);
            const firstPaymentMethod = paymentOptions.first().val();
            const isExists = YayCurrency_Callback.Helper.selectedPaymentMethodExists(selectedPaymentMethod);

            if (!isExists || firstPaymentMethod === selectedPaymentMethod) {
                localStorage.setItem(yay_currency_data_args.blocks_data_args.payments.localStorageName, firstPaymentMethod);
                selectedPaymentMethod = firstPaymentMethod;
            }

            // Set the radio button as checked for the selected payment method (via Label click)
            const paymentMethodSelected = $(yay_currency_data_args.blocks_data_args.payments.options + '[value="' + selectedPaymentMethod + '"]'),
                parentPaymentLabelOption = paymentMethodSelected.closest(yay_currency_data_args.blocks_data_args.payments.class.parent).find(yay_currency_data_args.blocks_data_args.payments.class.labelOption);

            if (parentPaymentLabelOption.length) {
                YayCurrency_Callback.Helper.handleForceCurrencyByPaymentMethod(selectedPaymentMethod, oldCurrencyID);
                parentPaymentLabelOption.click();
            }

        },
        setInitPaymentMethodSelected: function (oldCurrencyID) {
            let paymentOptions = $(yay_currency_data_args.blocks_data_args.payments.options);
            if (!paymentOptions.length) {
                if ($(yay_currency_data_args.blocks_data_args.payments.container).length) {
                    let count = 1;
                    let intervalTime = setInterval(function () {
                        if ($(yay_currency_data_args.blocks_data_args.payments.options).length || 3 === count) {
                            paymentOptions = $(yay_currency_data_args.blocks_data_args.payments.options);
                            if (!paymentOptions.length) {
                                --count;
                            } else {
                                YayCurrency_Callback.Helper.setInitWithPaymentOptionsExists(paymentOptions, oldCurrencyID);
                                clearInterval(intervalTime);
                            }

                        }
                        ++count;
                    }, 500);
                }
            } else {
                YayCurrency_Callback.Helper.setInitWithPaymentOptionsExists(paymentOptions, oldCurrencyID);
            }
        },
        paymentMethodSelectedInitCheckoutBlocks: function (oldCurrencyID) {
            let isPaymentMethodDomChanges = false;
            // MutationObserver for DOM changes
            const observer = new MutationObserver((mutationsList) => {
                mutationsList.forEach((mutation) => {
                    if (mutation.type === "childList") {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1 && 'payment-method' === node.id) {
                                isPaymentMethodDomChanges = true;
                                YayCurrency_Callback.Helper.setInitPaymentMethodSelected(oldCurrencyID);
                            }
                        });
                    }
                });
            });
            observer.observe(document.body, { childList: true, subtree: true });

            setTimeout(() => {
                if (isPaymentMethodDomChanges) {
                    return;
                }
                YayCurrency_Callback.Helper.setInitPaymentMethodSelected(oldCurrencyID);
            }, 500);

        },
        handleChangePaymentMethod: function (oldCurrencyID) {
            $(document).on('change', yay_currency_data_args.blocks_data_args.payments.options, function () {
                const selectedPaymentMethod = localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName);
                const paymentMethodChanged = $(this).val();
                if (selectedPaymentMethod === paymentMethodChanged) {
                    return;
                }
                localStorage.setItem(yay_currency_data_args.blocks_data_args.payments.localStorageName, paymentMethodChanged);
                YayCurrency_Callback.Helper.handleForceCurrencyByPaymentMethod(paymentMethodChanged, oldCurrencyID)
            });
        },
        handleForceCurrencyByPaymentMethod: function (paymentMethodChanged, oldCurrencyID) {
            const currencyByMethod = YayCurrency_Callback.Helper.getCurrencyIDByMethodSelected(paymentMethodChanged);
            if (currencyByMethod && currencyByMethod !== +oldCurrencyID) {
                YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout);
                YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, currencyByMethod, +yayCurrency.cookie_lifetime_days);
                YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name, currencyByMethod, +yayCurrency.cookie_lifetime_days);
                location.reload();
            }
        },
        forceCurrencyByPaymentMethodOnCheckoutBlocksPage: function (oldCurrencyID) {
            YayCurrency_Callback.Helper.paymentMethodSelectedInitCheckoutBlocks(oldCurrencyID);
            YayCurrency_Callback.Helper.handleChangePaymentMethod(oldCurrencyID);
        },
        approximatePriceCheckoutBlocks: function (currencyID) {
            if (YayCurrency_Callback.Helper.detectCheckoutBlocks()) {
                const applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID);
                const turn_off_checkout = ('0' === yayCurrency.checkout_diff_currency && yayCurrency.default_currency_code !== applyCurrency.currency) || ('1' === yayCurrency.checkout_diff_currency && '0' === applyCurrency.status);
                if (turn_off_checkout) {
                    // Run on page load
                    YayCurrency_Callback.Helper.addApproximatePrices(applyCurrency);

                    // Observe DOM changes
                    const observer = new MutationObserver(function (mutations) {
                        YayCurrency_Callback.Helper.addApproximatePrices(applyCurrency);
                    });
                    observer.observe(document.querySelector('.wc-block-checkout'), { childList: true, subtree: true });

                    // Cleanup
                    $(window).on('unload', function () {
                        observer.disconnect();
                    });
                }
            }

        },
        addApproximatePrices: function (applyCurrency) {
            $('.wc-block-checkout__order-summary-item__total-price, .wc-block-formatted-money-amount').each(function () {
                if (!$(this).find('.yay-currency-checkout-converted-approximately').length) {
                    const priceText = $(this).text().trim(); // e.g., "1_234 56" or "1,234.56 €"
                    let numericValue = YayCurrency_Callback.Helper.parsePrice(priceText);

                    if (!isNaN(numericValue)) {
                        const approximatePriceHTML = YayCurrency_Callback.Helper.approximatePriceHTML(numericValue, applyCurrency);
                        $(this).append(approximatePriceHTML);
                    }
                }
            });
        },
        parsePrice: function (priceText) {
            // Remove all non-numeric characters except potential separators
            let cleanPrice = priceText.replace(/[^0-9\s_,.]/g, '').trim(); // Keep digits, spaces, commas, dots, underscores
            if (!cleanPrice) {
                return 0; // Return 0 if no numeric content
            }

            // Split by all possible separators
            let allParts = cleanPrice.split(/[\s_,.]+/); // Split by space, comma, dot, underscore
            if (allParts.length < 1) {
                return parseFloat(cleanPrice) || 0; // No separators, treat as whole number
            }

            // The last part is the decimal portion (keep all digits)
            let decimalPart = allParts.pop() || '0';
            let integerPart = allParts.join(''); // Join remaining parts as integer

            // Combine with a standard decimal separator
            let combinedPrice = integerPart + (decimalPart ? '.' + decimalPart : '');

            // Parse to float, preserving the original decimal places
            let numericValue = parseFloat(combinedPrice) || 0;

            return numericValue;
        },
        approximatePriceHTML: function (originalPrice, applyCurrency) {
            if (yayCurrency.default_currency_code !== yayCurrency.fallback_currency_code) {
                const fallbackCurrency = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(yayCurrency.fallback_currency_code);
                originalPrice = YayCurrency_Callback.Helper.handelRevertPrice(originalPrice, fallbackCurrency);
            }
            const approximatePrice = YayCurrency_Callback.Helper.formatPriceByCurrency(originalPrice, true, applyCurrency)
            const price_html = " <span class='yay-currency-checkout-converted-approximately'>(~" + approximatePrice + ")</span>";
            return price_html;
        },
        // Converter
        getCurrentCurrencyByCode: function (currency_code = false, converted_currency = false) {
            currency_code = currency_code ? currency_code : window.yayCurrency.default_currency_code;
            converted_currency = converted_currency ? converted_currency : window.yayCurrency.converted_currency;
            let currentCurrency = false;
            if (converted_currency) {
                converted_currency.forEach((convert_currency) => {
                    if (convert_currency.currency === currency_code) {
                        currentCurrency = convert_currency;
                    }
                });
            }
            return currentCurrency;
        },
        currencyConverter: function () {
            const currency_converter_el = yay_currency_data_args.converter_args.converterWrapper;
            if ($(currency_converter_el).length) {
                $(currency_converter_el).each(function (index, element) {
                    YayCurrency_Callback.Helper.doConverterCurrency($(element))
                });
            }
        },
        doFormatNumber: function (number, decimals, decPoint, thousandsSep, haveZeroInDecimal = false) {
            if (number === 'N/A' || number === '') {
                return number
            }
            // Strip all characters but numerical ones.
            number = (number + '').replace(/[^0-9+\-Ee.]/g, '')
            let n = !isFinite(+number) ? 0 : +number,
                prec = !isFinite(+decimals) ? 0 : Math.abs(decimals),
                sep = typeof thousandsSep === 'undefined' ? ',' : thousandsSep,
                dec = typeof decPoint === 'undefined' ? '.' : decPoint,
                s = '',
                toFixedFix = function (n, prec) {
                    let k = Math.pow(10, prec)
                    return '' + Math.round(n * k) / k
                }
            // Fix for IE parseFloat(0.55).toFixed(0) = 0;
            s = (prec ? toFixedFix(n, prec) : '' + Math.round(n)).split('.')
            if (s[0].length > 3) {
                s[0] = s[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, sep)
            }
            if ((s[1] || '').length < prec) {
                s[1] = s[1] || ''
                s[1] += new Array(prec - s[1].length + 1).join('0')
            }

            return haveZeroInDecimal
                ? s.join(dec)
                : s
                    .join(dec)
                    .replace(/([0-9]*\.0*[1-9]+)0+$/gm, '$1')
                    .replace(/.00+$/, '')
        },
        roundedAmountByCurrency: function (amount, applyCurrency) {
            if (!applyCurrency) {
                return amount;
            }
            const { numberDecimal, decimalSeparator, thousandSeparator } = applyCurrency;
            amount = YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(amount, applyCurrency);
            const formattedTestAmount = YayCurrency_Callback.Helper.doFormatNumber(
                amount,
                Number(numberDecimal),
                decimalSeparator,
                thousandSeparator,
                true
            );
            return formattedTestAmount;
        },
        handelRoundedPriceByCurrency: function (price, applyCurrency) {
            const { roundingType, roundingValue, subtractAmount } = applyCurrency;
            switch (roundingType) {
                case 'up':
                    price = Math.ceil(price / roundingValue) * roundingValue - subtractAmount;
                    break
                case 'down':
                    price = Math.floor(price / roundingValue) * roundingValue - subtractAmount;
                    break
                case 'nearest':
                    price = Math.round(price / roundingValue) * roundingValue - subtractAmount;
                    break
                default:
                    break;
            }
            return price;
        },
        handelConvertPrice: function (price = 0, applyCurrency = false) {
            if (!applyCurrency) {
                const currencyID = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name);
                applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID);
            }
            const rateFee = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(applyCurrency));
            if (!rateFee || 1 === rateFee) {
                return price;
            }
            return YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(price * rateFee, applyCurrency);
        },
        handelRevertPrice: function (price = 0, applyCurrency = false) {
            if (!applyCurrency) {
                const currencyID = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name);
                applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID);
            }
            const rateFee = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(applyCurrency));
            if (!rateFee || 1 === rateFee) {
                return price;
            }
            return price / rateFee;
        },
        decodeHtmlEntity: function (entity) {
            var textArea = document.createElement('textarea');
            textArea.innerHTML = entity;
            return textArea.value;
        },
        formatPricePosition: function (price = 0, character = '', position = 'left') {
            let formattedPrice = price;
            switch (position) {
                case 'left':
                    formattedPrice = character + formattedPrice;
                    break;
                case 'right':
                    formattedPrice = formattedPrice + character;
                    break;
                case 'left_space':
                    formattedPrice = character + ' ' + formattedPrice;
                    break;
                case 'right_space':
                    formattedPrice = formattedPrice + ' ' + character;
                    break;
                default:
                    break;
            }
            return formattedPrice;
        },
        formatPriceByCurrency: function (price = 0, applyRateFee = false, applyCurrency = false) {
            if (!applyCurrency) {
                const currencyID = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name);
                applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID);
            }

            if (applyRateFee) {
                const rateFee = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(applyCurrency));
                price = YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(price * rateFee, applyCurrency);
            }

            // Convert the price to a fixed decimal string
            var priceString = price.toFixed(applyCurrency.numberDecimal);

            // Split the price into whole and decimal parts (if decimals were used)
            var parts = priceString.split('.');

            // Add thousand separators to the whole part
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, applyCurrency.thousandSeparator);

            // Combine whole part and decimal part with the decimal separator
            var formattedPrice = parts.join(applyCurrency.decimalSeparator);

            // Decode HTML entity and position the currency symbol
            var decodedSymbol = YayCurrency_Callback.Helper.decodeHtmlEntity(applyCurrency.symbol);

            formattedPrice = YayCurrency_Callback.Helper.formatPricePosition(formattedPrice, decodedSymbol, applyCurrency.currencyPosition);

            if (applyCurrency.currencyCodePosition) {
                formattedPrice = YayCurrency_Callback.Helper.formatPricePosition(formattedPrice, applyCurrency.currency, applyCurrency.currencyCodePosition);
            }

            return formattedPrice;
        },
        doApplyResultConverter: function (_this, data) {
            const
                from_el = _this.find(yay_currency_data_args.converter_args.converterFrom),
                to_el = _this.find(yay_currency_data_args.converter_args.converterTo),
                from_currency_code = data.from_currency_code ? data.from_currency_code : $(from_el).val(),
                to_currency_code = data.to_currency_code ? data.to_currency_code : $(to_el).val();
            let amount = data.amount_value ? +data.amount_value : + $(_this.find(yay_currency_data_args.converter_args.converterAmount)).val();

            if (to_currency_code === from_currency_code) {
                $(_this.find(yay_currency_data_args.converter_args.converterResultValue)).text(amount);
            } else {
                const from_apply_currency = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(from_currency_code),
                    to_apply_currency = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(to_currency_code),
                    exchange_rate_fee = YayCurrency_Callback.Helper.getRateFeeByCurrency(to_apply_currency);
                if (from_apply_currency && from_currency_code !== yayCurrency.default_currency_code) {
                    const rate_after_fee = YayCurrency_Callback.Helper.getRateFeeByCurrency(from_apply_currency);
                    amount = amount * parseFloat(1 / rate_after_fee);
                }
                $(_this.find(yay_currency_data_args.converter_args.converterResultValue)).text(YayCurrency_Callback.Helper.roundedAmountByCurrency(amount * exchange_rate_fee, to_apply_currency));
            }
        },
        doConverterCurrency: function (_this) {
            const amount_el = _this.find(yay_currency_data_args.converter_args.converterAmount),
                from_el = _this.find(yay_currency_data_args.converter_args.converterFrom),
                to_el = _this.find(yay_currency_data_args.converter_args.converterTo),
                result_wrapper = _this.find(yay_currency_data_args.converter_args.converterResultWrapper);

            $(from_el).change(function () {
                $(_this.find(yay_currency_data_args.converter_args.converterResultFrom)).text($(this).val());
                YayCurrency_Callback.Helper.doApplyResultConverter(_this, {
                    'from_currency_code': $(this).val()
                });
            });
            $(to_el).change(function () {
                $(_this.find(yay_currency_data_args.converter_args.converterResultTo)).text($(this).val());
                YayCurrency_Callback.Helper.doApplyResultConverter(_this, {
                    'to_currency_code': $(this).val()
                });
            });
            $(amount_el).on("input", function () {
                const amount = $(this).val();
                $(this).val(amount.replace(/\D/g, '')); // do not allow enter character
                if (amount) {
                    $(result_wrapper).show();
                    $(_this.find(yay_currency_data_args.converter_args.converterResultAmount)).text(amount);
                    YayCurrency_Callback.Helper.doApplyResultConverter(_this, {
                        'amount_value': amount
                    });
                } else {
                    $(result_wrapper).hide();
                }
            });
            $(amount_el).trigger('input');
            $(from_el).trigger('change');
            $(to_el).trigger('change');
        },
        handleFilterByPrice: function (currencyID) {
            window.addEventListener('load', function () {
                YayCurrency_Callback.Helper.handleFilterByPriceClassicEditor(currencyID);
                YayCurrency_Callback.Helper.handleFilterByPriceBlock(currencyID);
            });
        },

        handleFilterByPriceClassicEditor: function (currencyID) {
            // use Widget classic editor
            if ($('.widget_price_filter .price_slider').length) {
                const applyCurrency = YayCurrency_Callback.Helper.getCurrentCurrency(currencyID);
                if (applyCurrency.currency === window.yayCurrency.default_currency_code) {
                    return;
                }

                let currentMinPrice = $('.price_slider_amount #min_price').val(),
                    currentMaxPrice = $('.price_slider_amount #max_price').val();
                if (!currentMinPrice || !currentMaxPrice) {
                    return;
                }

                $(document.body).on('price_slider_create price_slider_slide', function (event, min, max) {
                    $('.price_slider_amount span.from').html(YayCurrency_Callback.Helper.formatPriceByCurrency(min, true, applyCurrency));
                    $('.price_slider_amount span.to').html(YayCurrency_Callback.Helper.formatPriceByCurrency(max, true, applyCurrency));
                });

                $('.price_slider_amount span.from').html(YayCurrency_Callback.Helper.formatPriceByCurrency(currentMinPrice, true, applyCurrency));
                $('.price_slider_amount span.to').html(YayCurrency_Callback.Helper.formatPriceByCurrency(currentMaxPrice, true, applyCurrency));

                yayCurrencyHooks.doAction('yayCurrencyHandleFilterPriceClassicEditor', [{ current_currency_id: currencyID }]);
            }
        },
        handleFilterByPriceBlock: function (currencyID) {
            // use Block gutenberg
            if (!window.wc) {
                return;
            }
            const filterPriceControls = $(yay_currency_data_args.blocks_data_args.filterPrice.class.wrapper);
            if (filterPriceControls.length && filterPriceControls.find(yay_currency_data_args.blocks_data_args.filterPrice.class.controls).length) {
                let count = 1;
                let flagMarkPriceChange = false;

                let intervalTime = setInterval(function () {
                    let min_input_wrapper = $(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceWrapper);
                    let max_input_wrapper = $(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceWrapper);

                    if (min_input_wrapper.length && max_input_wrapper.length) {
                        const price_filter_controls = $(yay_currency_data_args.blocks_data_args.filterPrice.class.filterSlideInput).parents(yay_currency_data_args.blocks_data_args.filterPrice.class.controls);
                        const clone = price_filter_controls.clone();
                        price_filter_controls.replaceWith(clone)

                        const minPriceInput = min_input_wrapper.attr('aria-valuetext') ? +min_input_wrapper.attr('aria-valuetext') : false;
                        if (minPriceInput) {
                            $(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(minPriceInput, true));
                            $(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).css('pointer-events', 'none');
                        }

                        const maxPriceInput = max_input_wrapper.attr('aria-valuetext') ? +max_input_wrapper.attr('aria-valuetext') : false;
                        if (maxPriceInput) {
                            $(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(maxPriceInput, true));
                            $(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).css('pointer-events', 'none');
                        }

                        flagMarkPriceChange = true;

                    }
                    if (5 === count || flagMarkPriceChange) {
                        clearInterval(intervalTime);
                    }
                    ++count;
                }, 500);
            }

            $(document).on('input', yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceWrapper, function () {
                const minPrice = $(this).attr('aria-valuetext') ? +$(this).attr('aria-valuetext') : false;
                if (minPrice) {
                    $(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(minPrice, true));
                }
            });

            $(document).on('input', yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceWrapper, function () {
                const maxPrice = $(this).attr('aria-valuetext') ? +$(this).attr('aria-valuetext') : false;
                if (maxPrice) {
                    $(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(maxPrice, true));
                }

            });

            yayCurrencyHooks.doAction('yayCurrencyHandleFilterPriceBlock', [{ current_currency_id: currencyID }]);
        }
    };

})(jQuery, window);