<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

// link plugin : https://woo.com/products/all-products-for-woocommerce-subscriptions/

class WooAllProductsForSubscriptions {
	use SingletonTrait;

	private $apply_currency = array();

	public function __construct() {

		if ( ! class_exists( 'WCS_ATT' ) ) {
			return;
		}

		add_filter( 'yay_currency_caching_localize_args', array( $this, 'get_caching_localize_args' ), 10, 1 );
		add_filter( 'yay_currency_caching_get_price_details_results', array( $this, 'caching_get_price_details_results' ), 10, 5 );

	}

	public function get_caching_localize_args( $localize_args ) {
		$localize_args['wcs_att'] = 'yes';
		return $localize_args;
	}

	public function caching_get_price_details_results( $results, $data, $productId, $product, $apply_currency ) {
		if ( class_exists( 'WCS_ATT_Display_Product' ) ) {
			$details                                = \WCS_ATT_Display_Product::get_subscription_options_content( $product );
			$results['price_details'][ $productId ] = $details;
		}
		return $results;
	}
}
