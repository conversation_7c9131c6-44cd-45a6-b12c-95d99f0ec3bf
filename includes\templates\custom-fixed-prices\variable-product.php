<?php
defined( 'ABSPATH' ) || exit;
use Yay_Currency\Helpers\Helper;
$yay_currencies      = Helper::get_currencies_post_type();
$custom_fixed_prices = get_post_meta( $variation->ID, 'yay_currency_custom_fixed_prices', true );
$fixed_prices_nonce  = wp_create_nonce( 'yay-custom-fixed-prices-nonce' );
?>
<div class="yay-currency-product-custom-fixed-prices-variable">
	<div class="yay-currency-fixed-price-checkbox-wrapper">
		<p class="yay-currency-fixed-price-text"><?php esc_html_e( 'Fixed price for each currency', 'yay-currency' ); ?></p>
	</div>
	<div class='yay-currency-fixed-prices-input-wrapper'>
		<i class="checkbox-sub-text"><?php esc_html_e( 'You can manually set fixed price for each currency. Leave blank to get the rate automatically.', 'yay-currency' ); ?></i>
		<div class="yay-currency-fixed-prices-input">
		<?php
		foreach ( $yay_currencies as $currency ) {
			if ( Helper::default_currency_code() === $currency->post_title ) {
				continue;
			}

			woocommerce_wp_text_input(
				array(
					'id'            => 'regular_price_' . $currency->post_title . '_' . $index,
					'placeholder'   => 'Auto',
					'label'         => 'Regular Price (' . $currency->post_title . ')',
					'desc_tip'      => 'true',
					'wrapper_class' => 'form-row form-row-first',
					'value'         => ! empty( $custom_fixed_prices[ $currency->post_title ] ) ? $custom_fixed_prices[ $currency->post_title ]['regular_price'] : '',
				)
			);

			woocommerce_wp_text_input(
				array(
					'id'            => 'sale_price_' . $currency->post_title . '_' . $index,
					'placeholder'   => 'Auto',
					'label'         => 'Sale Price (' . $currency->post_title . ')',
					'desc_tip'      => 'true',
					'wrapper_class' => 'form-row form-row-last',
					'value'         => ! empty( $custom_fixed_prices[ $currency->post_title ] ) ? $custom_fixed_prices[ $currency->post_title ]['sale_price'] : '',
				)
			);

		}
		?>
		</div>
	</div>
	<input type="hidden" name="yay-custom-fixed-prices-nonce" value="<?php echo esc_attr( $fixed_prices_nonce ); ?>" />
</div>
<?php do_action( 'yay_currency_add_fixed_prices_variable_product', $yay_currencies, $variation->ID, $index ); ?>
