<?php

namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\CountryHelper;

use Yay_Currency\Utils\SingletonTrait;

defined( 'ABSPATH' ) || exit;
class WooCommerceCaching {

	use SingletonTrait;

	private $apply_currency        = array();
	private $cache_compatible_ajax = false;

	public function __construct() {

		$caching_priority = apply_filters( 'yay_currency_caching_priority', 9999 );

		add_action( 'wp_ajax_yay_caching_get_price_html', array( $this, 'ajax_caching_get_price_html' ) );
		add_action( 'wp_ajax_nopriv_yay_caching_get_price_html', array( $this, 'ajax_caching_get_price_html' ) );

		add_action( 'wp_ajax_yay_caching_generate_currency_switcher_html', array( $this, 'ajax_caching_generate_currency_switcher_html' ) );
		add_action( 'wp_ajax_nopriv_yay_caching_generate_currency_switcher_html', array( $this, 'ajax_caching_generate_currency_switcher_html' ) );

		add_filter( 'wc_price_args', array( $this, 'custom_wc_price_args_with_ajax' ), $caching_priority, 1 );
		add_filter( 'yay_currency_ajax_handle_response_caching_results', array( $this, 'ajax_handle_response_caching_results' ), $caching_priority, 3 );

		if ( ! $this->compatible_cache_ajax() ) {
			return;
		}

		add_action( 'yay_currency_enqueue_scripts', array( $this, 'yay_currency_enqueue_scripts' ) );

		$product_price_hooks = YayCurrencyHelper::get_product_price_hooks();

		foreach ( $product_price_hooks as $price_hook ) {
			add_filter( $price_hook, array( $this, 'convert_product_price_with_caching' ), $caching_priority, 2 );
		}

		add_filter( 'woocommerce_get_variation_prices_hash', array( $this, 'custom_variation_price_has_with_caching' ), $caching_priority, 1 );

		add_filter( 'woocommerce_get_price_html', array( $this, 'custom_woocommerce_get_price_html' ), $caching_priority, 2 );

		add_filter( 'yay_currency_get_id_selected_currency', array( $this, 'get_currency_selected_id_with_caching' ), $caching_priority, 1 );
		add_filter( 'yay_currency_switcher_class', array( $this, 'add_class_ajax_loading' ), $caching_priority, 1 );
		add_filter( 'yay_currency_country_currency_notice_html', array( $this, 'custom_country_currency_notice_html' ), $caching_priority, 1 );

	}

	public function compatible_cache_ajax() {
		return Helper::cache_enable() || $this->cache_compatible_ajax;
	}

	public function yay_currency_enqueue_scripts() {
		$suffix = defined( 'YAY_CURRENCY_SCRIPT_DEBUG' ) ? '' : '.min';

		$localize_args = array(
			'ajax_url'                 => admin_url( 'admin-ajax.php' ),
			'nonce'                    => wp_create_nonce( 'yay-caching-nonce' ),
			'is_switch_currency'       => isset( $_REQUEST['yay-currency-nonce'] ) ? 'yes' : 'no',
			'yay_currency_current_url' => Helper::get_current_url(),
		);

		if ( apply_filters( 'yay_currency_is_show_country', false ) ) {
			$country_info_args = array(
				'ip_address'     => CountryHelper::get_ip_address(),
				'country_info'   => CountryHelper::get_country_info_from_IP(),
				'country_header' => CountryHelper::get_country_code_from_headers(),
			);
			$localize_args     = array_merge( $localize_args, $country_info_args );
		}

		if ( Helper::loading_mark_enable() ) {
			$localize_args['is_loading_mark'] = 'yes';
		}

		if ( class_exists( 'GTranslate' ) ) {
			$localize_args['gtranslate_active'] = 'yes';
		}

		if ( is_product() || is_singular( 'product' ) ) {
			global $product;
			if ( ! $product || ! is_object( $product ) ) {
				global $post;
				if ( $post && isset( $post->post_type ) && 'product' === $post->post_type ) {
					$product = wc_get_product( $post->ID );
				}
			}

			if ( is_object( $product ) ) {
				$product_id                  = $product->get_id();
				$localize_args['product_id'] = $product_id;
				if ( 'variable' === $product->get_type() ) {
					$localize_args['product_is_variable']                 = true;
					$localize_args['product_single_variation_wrap_class'] = apply_filters( 'yay_currency_single_variation_wrap_class', '.single_variation_wrap' );
				}
				$localize_args = apply_filters( 'yay_currency_caching_product_args', $localize_args, $product );
			}
		}

		wp_enqueue_script( 'yay-currency-caching-script', YAY_CURRENCY_PLUGIN_URL . 'src/compatibles/cache/yay-caching' . $suffix . '.js', array( 'jquery' ), YAY_CURRENCY_VERSION, true );

		wp_localize_script(
			'yay-currency-caching-script',
			'yay_currency_caching_data',
			apply_filters( 'yay_currency_caching_localize_args', $localize_args )
		);

		wp_enqueue_style(
			'yay-currency-caching-frontend',
			YAY_CURRENCY_PLUGIN_URL . 'src/compatibles/cache/yay-caching.css',
			array(),
			YAY_CURRENCY_VERSION
		);
	}

	public function get_currency_selected_id_with_caching( $select_id ) {
		if ( ! $this->ajax_price_html_caching() || ! $this->compatible_cache_ajax() ) {
			return $select_id;
		}

		return isset( $this->apply_currency['ID'] ) ? $this->apply_currency['ID'] : $select_id;
	}

	public function add_class_ajax_loading( $class_name ) {

		if ( ! Helper::allow_detect_caching() ) {
			return $class_name;
		}

		$loading_class = Helper::loading_mark_enable();
		if ( ! wp_doing_ajax() && $this->compatible_cache_ajax() && $loading_class ) {
			$class_name .= ' yay-currency-cache-loading';
		}

		return $class_name;

	}

	public function custom_country_currency_notice_html( $html ) {
		$notice_class = $this->add_class_ajax_loading( 'yay-currency-country-currency-notice-wrapper' );
		$html         = '<span class="' . esc_attr( $notice_class ) . '">' . $html . '</span>';
		return $html;
	}

	public function convert_product_price_with_caching( $price, $product ) {

		if ( ! $this->apply_currency || ! $this->ajax_price_html_caching() || ! $this->compatible_cache_ajax() ) {
			return $price;
		}

		if ( SupportHelper::detect_original_product_price( false, $price, $product ) ) {
			return $price;
		}

		$price = apply_filters( 'yay_currency_product_price_with_caching', $price, $product );

		$conditions_3rd_plugin = apply_filters( 'yay_currency_3rd_plugins_conditions', false, $product, $this->apply_currency );

		if ( $conditions_3rd_plugin ) {

			$price_with_conditions = SupportHelper::detect_price_with_conditions( $price, $product, $this->apply_currency );

			if ( $price_with_conditions ) {
				return $price_with_conditions;
			}

			return YayCurrencyHelper::convert_product_price( $price, $product, $this->apply_currency, true );

		}

		return YayCurrencyHelper::convert_product_price( $price, $product, $this->apply_currency, false );

	}

	public function custom_variation_price_has_with_caching( $price_hash ) {
		if ( ! $this->apply_currency || ! $this->ajax_price_html_caching() || ! $this->compatible_cache_ajax() ) {
			return $price_hash;
		}
		$price_hash[] = $this->apply_currency['ID'];
		return $price_hash;
	}

	public function custom_woocommerce_get_price_html( $price, $product ) {

		if ( ! Helper::allow_detect_caching() ) {
			return $price;
		}

		if ( wp_doing_ajax() ) {
			$approximate_price_info = Helper::approximate_price_info();
			if ( isset( $approximate_price_info['status'] ) && $approximate_price_info['status'] ) {
				$args_show_on = isset( $approximate_price_info['show_on'] ) ? $approximate_price_info['show_on'] : [];
				if ( in_array( 'product_price_html', $args_show_on, true ) || in_array( 'default', $args_show_on, true ) ) {
					$country_info    = CountryHelper::get_country_info_from_IP();
					$formatted_price = SupportHelper::get_formatted_product_price( $product, $country_info, $this->apply_currency );
					if ( ! $formatted_price ) {
						return $price;
					}
					$price = SupportHelper::get_formatted_approximately_price( $price, $formatted_price );
				}
			}

			return $price;
		}

		$price_tag_wrapper = 'span';

		if ( strpos( $price, '<div' ) !== false || strpos( $price, '<p' ) !== false ) {
			$price_tag_wrapper = 'div';
		}

		$price_class = $this->add_class_ajax_loading( 'yay-currency-cache-product-id' );

		return "<{$price_tag_wrapper} class='" . esc_attr( $price_class ) . "' data-yay_currency-product-id='{$product->get_id()}'>" . $price . "</{$price_tag_wrapper}>";
	}

	public function ajax_price_html_caching() {
		if ( ! $this->apply_currency || ! wp_doing_ajax() ) {
			return false;
		}
		if ( SupportHelper::detect_ajax_caching_doing() ) {
			return true;
		}
		return false;
	}

	public function custom_wc_price_args_with_ajax( $args ) {

		if ( $this->ajax_price_html_caching() ) {
			if ( $this->compatible_cache_ajax() ) {
				$args = YayCurrencyHelper::get_apply_currency_format_info( $this->apply_currency );
			}
		}

		return $args;

	}

	public function ajax_handle_response_caching_results( $results, $data, $apply_currency ) {

		$results['current_currency'] = $apply_currency;

		// Block gutenberg YayCurrency switcher
		$blocks = ! empty( $data['blocks'] ) ? map_deep( wp_unslash( $data['blocks'] ), 'sanitize_text_field' ) : array();
		if ( $blocks ) {
			foreach ( $blocks as $block ) {
				$results['block_content'][ $block['isBlockID'] ] = Helper::ajax_get_currency_switcher_html( 'block', $block );
			}
		}
		// Widget YayCurrency switcher
		$widget = isset( $data['widget'] ) ? intval( sanitize_text_field( $data['widget'] ) ) : false;
		if ( $widget ) {
			$results['widget_content'] = Helper::ajax_get_currency_switcher_html();
		}
		// Menu ShortCode YayCurrency switcher
		$menu = isset( $data['menu'] ) ? intval( sanitize_text_field( $data['menu'] ) ) : false;
		if ( $menu ) {
			$results['menu_content'] = Helper::ajax_get_currency_switcher_html( 'menu' );
		}
		// ShortCode YayCurrency switcher
		$shortcode = isset( $data['shortcode'] ) ? intval( sanitize_text_field( $data['shortcode'] ) ) : false;
		if ( $shortcode ) {
			$results['shortcode_content'] = Helper::ajax_get_currency_switcher_html( 'shortcode' );
		}
		// Single product page YayCurrency switcher
		$product = isset( $data['product'] ) ? intval( sanitize_text_field( $data['product'] ) ) : false;
		if ( $product ) {
			$results['product_content'] = Helper::ajax_get_currency_switcher_html( 'product' );
		}
		// Country notice html
		$country_notice = isset( $data['country_notice'] ) ? intval( sanitize_text_field( $data['country_notice'] ) ) : false;
		if ( $country_notice ) {
			$results['country_notice'] = CountryHelper::get_country_currency_notice( $apply_currency );
		}

		// ShortCode price html
		if ( isset( $data['shortcode_default_price'] ) ) {
			foreach ( $data['shortcode_default_price'] as $key => $price ) {
				$results['shortcode_price_html'][ $key ] = YayCurrencyHelper::calculate_price_by_currency_html( $apply_currency, $price );
			}
		}

		// ShortCode product price html
		if ( isset( $data['shortcode_product_ids'] ) ) {
			foreach ( $data['shortcode_product_ids'] as $key => $product_id ) {
				$product = wc_get_product( $product_id );
				if ( ! $product ) {
					continue;
				}
				$results['shortcode_product_price_html'][ $key ] = $product->get_price_html();
			}
		}

		$results['current_currency_id'] = $apply_currency['ID'];
		$results['current_symbol']      = $apply_currency['symbol'];

		if ( apply_filters( 'yay_currency_is_show_country', false ) ) {
			$results['ip_address']     = CountryHelper::get_ip_address();
			$results['country_info']   = CountryHelper::get_country_info_from_IP();
			$results['country_header'] = CountryHelper::get_country_code_from_headers();
		}

		if ( isset( $data['product_variable_id'] ) ) {
			$product_id = intval( $data['product_variable_id'] );
			$product    = wc_get_product( $product_id );
			$prices     = $product->get_variation_prices( true );
			if ( ! empty( $prices['price'] ) ) {
				$min_price = current( $prices['price'] );
				$max_price = end( $prices['price'] );
				if ( $min_price === $max_price ) {
					$results['product_variable_should_not_render_html'] = true;
				}
			}
		}

		return apply_filters( 'yay_currency_ajax_generate_results_3rd_plugins', $results, $data, $apply_currency );
	}

	public function get_apply_currency_by_caching() {
		$language_plugins_3rd = false;
		$cookie_name          = YayCurrencyHelper::get_cookie_name();
		$cookie_switcher_name = YayCurrencyHelper::get_cookie_name( 'switcher' );
		if ( Helper::use_yay_currency_params() && isset( $_COOKIE[ $cookie_name ] ) ) {
			$apply_currency = YayCurrencyHelper::detect_current_currency();
		} elseif ( isset( $_COOKIE[ $cookie_name ] ) && isset( $_COOKIE[ $cookie_switcher_name ] ) ) {
			$apply_currency = YayCurrencyHelper::detect_current_currency();
			if ( $_COOKIE[ $cookie_switcher_name ] !== $_COOKIE[ $cookie_name ] ) {
				$currency_id    = sanitize_text_field( $_COOKIE[ $cookie_switcher_name ] );
				$apply_currency = YayCurrencyHelper::get_currency_by_ID( $currency_id );
			}
		} else {
			$converted_currency     = YayCurrencyHelper::converted_currency();
			$default_apply_currency = YayCurrencyHelper::get_default_apply_currency( $converted_currency );
			$apply_currency         = YayCurrencyHelper::auto_detect_countries( $default_apply_currency, $converted_currency );

			if ( apply_filters( 'yay_currency_multiple_language_active', false ) ) {
				$language_plugins_3rd = true;
				$apply_currency       = apply_filters( 'yay_currency_get_current_currency_by_caching_with_multiple_language', $apply_currency, $converted_currency );
			}
		}

		if ( ! $language_plugins_3rd ) {
			$apply_currency = apply_filters( 'yay_currency_caching_get_current_currency', $apply_currency );
		}

		return $apply_currency;
	}

	public function ajax_caching_get_price_html() {
		$nonce = isset( $_POST['_nonce'] ) ? sanitize_text_field( $_POST['_nonce'] ) : false;

		if ( ! $nonce || ( ! wp_verify_nonce( sanitize_key( $nonce ), 'yay-caching-nonce' ) && is_user_logged_in() ) ) {
			wp_send_json_error( array( 'message' => __( 'Nonce invalid', 'yay-currency' ) ) );
		}

		$compatible_cache            = isset( $_POST['cache_compatible'] ) && 1 === intval( $_POST['cache_compatible'] ) ? true : false;
		$this->cache_compatible_ajax = $compatible_cache;

		$results = array();

		if ( ! $this->compatible_cache_ajax() ) {
			wp_send_json_success( $results );
		}

		if ( isset( $_POST['productIds'] ) ) {
			$productIds = ! empty( $_POST['productIds'] ) ? map_deep( wp_unslash( $_POST['productIds'] ), 'sanitize_text_field' ) : array();

			$currency_code_param = isset( $_POST['currency_code_param'] ) ? sanitize_text_field( $_POST['currency_code_param'] ) : false;
			if ( $currency_code_param && Helper::use_yay_currency_params() ) {
				$this->apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code_param );
			} else {
				$this->apply_currency = $this->get_apply_currency_by_caching();
			}

			if ( ! empty( $productIds ) ) {
				foreach ( $productIds as $productId ) {
					$product = wc_get_product( $productId );
					if ( $product ) {
						$price_html                          = $product->get_price_html();
						$price_html                          = apply_filters( 'yay_currency_caching_price_html', $price_html, $product->get_price(), $product, $this->apply_currency );
						$results['price_html'][ $productId ] = $price_html;
						$results                             = apply_filters( 'yay_currency_caching_get_price_details_results', $results, $_POST, $productId, $product, $this->apply_currency );
					}
				}
			}
		}

		$results = apply_filters( 'yay_currency_ajax_handle_response_caching_results', $results, $_POST, $this->apply_currency );

		wp_send_json_success( $results );
	}

	public function ajax_caching_generate_currency_switcher_html() {
		$nonce = isset( $_POST['_nonce'] ) ? sanitize_text_field( $_POST['_nonce'] ) : false;

		if ( ! $nonce || ( ! wp_verify_nonce( sanitize_key( $nonce ), 'yay-caching-nonce' ) && is_user_logged_in() ) ) {
			wp_send_json_error( array( 'message' => __( 'Nonce invalid', 'yay-currency' ) ) );
		}

		$compatible_cache            = isset( $_POST['cache_compatible'] ) && 1 === intval( $_POST['cache_compatible'] ) ? true : false;
		$this->cache_compatible_ajax = $compatible_cache;

		$results = array();

		if ( ! $this->compatible_cache_ajax() ) {
			wp_send_json_success( $results );
		}

		$currency_code_param = isset( $_POST['currency_code_param'] ) ? sanitize_text_field( $_POST['currency_code_param'] ) : false;
		if ( $currency_code_param && Helper::use_yay_currency_params() ) {
			$this->apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code_param );
		} else {
			$this->apply_currency = $this->get_apply_currency_by_caching();
		}

		$results = apply_filters( 'yay_currency_ajax_handle_response_caching_results', $results, $_POST, $this->apply_currency );

		wp_send_json_success( $results );
	}
}
