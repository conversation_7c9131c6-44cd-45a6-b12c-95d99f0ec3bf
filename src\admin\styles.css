/* Switcher as a menu admin */
.yay-currency-menu-item-custom-fields {
  margin-bottom: 16px;
}
.yay-currency-menu-item-custom-fields__title {
  display: inline-block;
  font-weight: bold;
  margin: 10px 0;
}

.yay-currency-menu-item-custom-fields__field:not(:last-child) {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.yay-currency-menu-item-custom-fields__field--checkbox {
  margin: 0 6px 0 0 !important;
}

.yay-currency-menu-item-custom-field__field-group {
  display: flex;
  gap: 12px;
}

.yay-currency-menu-item-custom-fields__field--radio {
  margin: 2px !important;
}

/* Switcher as a classic widget admin */
.yay-currency-widget-custom-fields {
  margin-bottom: 16px;
}
.yay-currency-widget-custom-fields__title {
  display: inline-block;
  font-weight: bold;
  margin: 10px 0;
}

.yay-currency-widget-custom-fields__field:not(:last-child) {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.yay-currency-widget-custom-fields__field--checkbox {
  margin: 0 6px 0 0 !important;
}

.yay-currency-widget-custom-field__field-group {
  display: flex;
  gap: 12px;
}

.yay-currency-widget-custom-fields__field--radio {
  margin: 2px !important;
}

/* Fixed price for simple product */
.yay-currency-product-custom-fixed-prices-simple {
  .yay-currency-fixed-price-checkbox-wrapper {
    display: flex;
    align-items: center;
    margin-left: 12px;
  }

  .checkbox-sub-text {
    display: block;
    margin: 0 0 32px 12px;
  }

  .yay-currency-fixed-prices-input {
    box-shadow: inset 4px 0 #ffc106;
  }
}

/* Fixed price for variant product */
.yay-currency-product-custom-fixed-prices-variable {
  display: table;
  width: 100%;

  .checkbox-sub-text {
    display: block;
    margin: 0 0 12px 0;
  }

  .form-row {
    position: relative;
  }

  .form-row-first label::before {
    content: "";
    position: absolute;
    top: 2px;
    left: -26px;
    width: 4px;
    height: 70px;
    box-shadow: inset 4px 0 #ffc106;
  }
}

.yay-currency-fixed-price-text {
  color: #1d2327;
  font-size: 1.3em;
  font-weight: bold;
}

.yay-currency-fixed-price-checkbox-wrapper .form-field {
  display: flex;
  gap: 12px;
}

.wpml-compatible-table,
.polylang-compatible-table {
  margin-top: 20px;
}
/*LearnPress plugin - Fixed Prices option*/
.lp-meta-box__course-tab__content
  .yay-currency-product-custom-fixed-prices-simple {
  .yay-currency-fixed-price-checkbox-wrapper {
    margin-left: unset;
    margin-top: 10px;
    border-top: 2px solid #efefef;
  }

  .checkbox-sub-text {
    margin-left: unset;
  }

  .yay-currency-fixed-prices-input {
    margin: 10px 0 30px 0;

    .form-field {
      padding: 0 15px 25px 15px;
    }

    .form-field[class*="sale_price_"] {
      padding-bottom: 0;
    }
  }
}
