<?php

namespace Yay_Currency\Engine\FEPages;

use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\TaxHelper;
use Yay_Currency\Utils\SingletonTrait;

defined( 'ABSPATH' ) || exit;
class WooCommerceCheckoutPage {
	use SingletonTrait;

	public function __construct() {

		// CUSTOM PRODUCT SUBTOTAL, SUBTOTAL, TOTAL BY CURRENT CURRENCY --- ( FIXED PRODUCT PRICE OR ROUNDING PRICE ENABLE)
		add_filter( 'yay_currency_checkout_converted_product_subtotal_fixed', array( $this, 'checkout_converted_product_subtotal_fixed' ), 10, 4 );
		add_filter( 'yay_currency_checkout_converted_subtotal_fixed', array( $this, 'checkout_converted_subtotal_fixed' ), 10, 2 );
		add_filter( 'yay_currency_checkout_converted_total_tax_fixed', array( $this, 'checkout_converted_total_tax_fixed' ), 10, 3 );
		add_filter( 'yay_currency_checkout_converted_taxes_total_fixed', array( $this, 'checkout_converted_taxes_total_fixed' ), 10, 2 );
		add_filter( 'yay_currency_checkout_converted_total_fixed', array( $this, 'checkout_converted_total_fixed' ), 10, 3 );
		add_filter( 'yay_currency_checkout_converted_total_fallback_currency_enable', array( $this, 'checkout_converted_total_fallback_currency' ), 10, 2 );

		// DISCOUNT
		add_filter( 'yay_currency_checkout_formatted_percent_discount_price', array( $this, 'checkout_formatted_percent_discount_price' ), 10, 4 );
		add_filter( 'yay_currency_checkout_formatted_fixed_cart_or_product_discount_price', array( $this, 'checkout_formatted_fixed_cart_or_product_discount_price' ), 10, 5 );

		// SHIPPING
		add_filter( 'yay_currency_formatted_shipping_flat_rate_fee', array( $this, 'formatted_shipping_flat_rate_fee' ), 10, 3 );

		// CUSTOM WITH FALLBACK CURRENCY IS NOT DEFAULT CURRENCY ( FIXED PRODUCT PRICE OR ROUNDING PRICE ENABLE)
		add_filter( 'yay_currency_get_original_product_subtotal', array( $this, 'get_original_product_subtotal' ), 10, 5 );
		add_filter( 'yay_currency_get_original_cart_subtotal', array( $this, 'get_original_cart_subtotal' ), 10, 2 );
		add_filter( 'yay_currency_get_fixed_cart_or_product_discount_amount_html', array( $this, 'get_fixed_cart_or_product_discount_amount_html' ), 10, 3 );
		add_filter( 'yay_currency_get_percent_discount_amount_html', array( $this, 'get_percent_discount_amount_html' ), 10, 3 );
		add_filter( 'yay_currency_get_fallback_currency_shipping_fee', array( $this, 'get_fallback_currency_shipping_fee' ), 10, 4 );
		add_filter( 'yay_currency_get_fee_amount_html', array( $this, 'get_fee_amount_html' ), 10, 3 );
		add_filter( 'yay_currency_get_fallback_tax_amount', array( $this, 'get_fallback_tax_amount' ), 10, 4 );
		add_filter( 'yay_currency_get_original_cart_total', array( $this, 'get_original_cart_total' ), 10, 3 );

		// Force By Country Code

		add_filter( 'yay_currency_detect_current_currency', array( $this, 'get_apply_currency_force_country_code' ), 20, 1 );
		add_filter( 'yay_currency_ajax_handle_response_force_payment_results', array( $this, 'yay_currency_ajax_handle_response_force_payment_results' ), 20, 4 );
		add_filter( 'yay_currency_ajax_handle_response_force_currency_results', array( $this, 'yay_currency_ajax_handle_response_force_currency_results' ), 20, 4 );
	}

	// CUSTOM PRODUCT SUBTOTAL, SUBTOTAL, TOTAL BY CURRENT CURRENCY --- ( FIXED PRODUCT PRICE OR ROUNDING PRICE ENABLE)

	public function checkout_converted_product_subtotal_fixed( $converted_product_subtotal, $product, $apply_currency, $quantity ) {
		// Check if Product is sign_up fee
		$sign_up_fee = apply_filters( 'yay_currency_checkout_product_sign_up_fee', false, $product, $apply_currency );
		if ( $sign_up_fee ) {
			$converted_product_subtotal = apply_filters( 'yay_currency_converted_product_subtotal_sign_up_fee', $converted_product_subtotal, $product, $quantity );
			return $converted_product_subtotal;
		}

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency ) ) {
			$fixed_product_price    = (float) FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $apply_currency );
			$fixed_product_price    = apply_filters( 'yay_currency_get_product_fixed_price_3rd', $fixed_product_price, $product, $apply_currency );
			$fixed_product_price    = apply_filters( 'yay_currency_get_product_price_apply_currency', $fixed_product_price, $product, 1 );
			$fixed_product_subtotal = SupportHelper::get_product_subtotal_fixed_by_3rd_plugin( $fixed_product_price, $product, $quantity, $apply_currency );

			if ( $fixed_product_subtotal ) {
				$converted_product_subtotal = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $fixed_product_subtotal );
			}

			return $converted_product_subtotal;
		}

		return $converted_product_subtotal;

	}

	public function checkout_converted_subtotal_fixed( $converted_subtotal, $apply_currency ) {

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency ) ) {
			$subtotal_price_fixed = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
			$converted_subtotal   = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $subtotal_price_fixed );
			return $converted_subtotal;
		}

		$converted_subtotal = apply_filters( 'yay_currency_checkout_converted_subtotal_with_3rd_plugin', $converted_subtotal, $apply_currency );
		return $converted_subtotal;
	}

	public function checkout_converted_total_tax_fixed( $formatted_converted_tax_amount, $tax_rate_id, $apply_currency ) {

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency ) ) {
			$price = TaxHelper::get_price_by_tax_rate_id( $apply_currency, $tax_rate_id );
			if ( apply_filters( 'yay_currency_is_more_one_tax_apply_in_cart', false ) ) {
				$price = apply_filters( 'yay_currency_recalculate_total_tax_by_rate_id', $price, $tax_rate_id, $apply_currency );
			} else {
				$price = apply_filters( 'yay_currency_recalculate_total_tax', $price, $apply_currency );
			}

			$formatted_converted_tax_amount = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $price );
		}

		$formatted_converted_tax_amount = apply_filters( 'yay_currency_checkout_converted_total_tax_with_3rd_plugin', $formatted_converted_tax_amount, $tax_rate_id, $apply_currency );
		return $formatted_converted_tax_amount;

	}

	public function checkout_converted_taxes_total_fixed( $converted_taxes_total_html, $apply_currency ) {

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency ) ) {
			$converted_taxes_total      = apply_filters( 'yay_currency_get_total_tax', 0, $apply_currency );
			$converted_taxes_total_html = YayCurrencyHelper::get_formatted_total_by_convert_currency( $converted_taxes_total, $apply_currency, $apply_currency['currency'] );
		}
		return $converted_taxes_total_html;
	}

	public function checkout_converted_total_fixed( $converted_total, $apply_currency, $enable_fallback_currency = false ) {
		$enable_fixed_or_rounding = YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency );
		if ( $enable_fixed_or_rounding ) {
			$total_price_fixed = SupportHelper::recalculate_checkout_cart_total( $apply_currency );
			$converted_total   = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $total_price_fixed );
			return $converted_total;
		}

		if ( $enable_fallback_currency && ! $enable_fixed_or_rounding ) {
			$converted_total = apply_filters( 'yay_currency_checkout_converted_total_fallback_currency_enable', $converted_total, $apply_currency );
		}

		return $converted_total;
	}

	public function checkout_converted_total_fallback_currency( $converted_total, $apply_currency ) {
		$cart_total = apply_filters( 'yay_currency_get_cart_total', 0, $apply_currency, false );
		if ( apply_filters( 'yay_currency_is_more_one_tax_apply_in_cart', false ) ) {
			$cart_total = SupportHelper::recalculate_checkout_cart_total( $apply_currency );
		}
		$converted_total = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $cart_total );
		return $converted_total;
	}

	// DISCOUNT

	public function checkout_formatted_percent_discount_price( $currencies_data, $coupon, $coupon_html, $discount_amount ) {
		$apply_currency  = $currencies_data['current_currency'];
		$discount_totals = WC()->cart->get_coupon_discount_totals();
		if ( apply_filters( 'yay_currency_incl_tax_enable', false ) ) {
			$discount_tax_totals = WC()->cart->get_coupon_discount_tax_totals();
			$discount_totals     = wc_array_merge_recursive_numeric( $discount_totals, $discount_tax_totals );
		}

		$discount_price           = $discount_totals[ $coupon->get_code() ];
		$converted_discount_price = YayCurrencyHelper::calculate_price_by_currency( $discount_price, true, $apply_currency );
		$discount_amount_html     = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $discount_price );
		$discount_amount_html     = apply_filters( 'yay_currency_get_percent_discount_amount_html', $discount_amount_html, $discount_amount, $currencies_data['fallback_currency'] );
		$converted_approximately  = SupportHelper::display_approximately_converted_price( $apply_currency );
		if ( ! $converted_approximately ) {
			return '-' . $discount_amount_html . ' ' . substr( $coupon_html, strpos( $coupon_html, '<a' ) );
		}

		$formatted_discount_price = YayCurrencyHelper::format_price( $converted_discount_price );

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $apply_currency ) ) {
			$coupon_data  = $coupon->get_data();
			$products_ids = isset( $coupon_data['product_ids'] ) && ! empty( $coupon_data['product_ids'] ) ? $coupon_data['product_ids'] : false;
			if ( SupportHelper::woo_discount_rules_active() && $products_ids ) {
				$cart_subtotal_fixed      = SupportHelper::calculate_subtotal_apply_discount( $converted_discount_price, $discount_amount, $products_ids, $apply_currency );
				$formatted_discount_price = YayCurrencyHelper::format_price( $cart_subtotal_fixed );
			} else {
				$cart_subtotal_fixed      = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
				$cart_subtotal_fixed      = ( $cart_subtotal_fixed * $discount_amount ) / 100;
				$formatted_discount_price = YayCurrencyHelper::format_price( $cart_subtotal_fixed );
			}
		}

		//  Display approximate price only on the checkout page
		if ( SupportHelper::display_approximate_price_checkout_only() ) {
			return '-' . $formatted_discount_price;
		}

		$formatted_discount_price_html = YayCurrencyHelper::converted_approximately_html( $formatted_discount_price );

		$custom_coupon_html = '-' . $discount_amount_html . $formatted_discount_price_html . substr( $coupon_html, strpos( $coupon_html, '<a' ) );
		return $custom_coupon_html;
	}

	public function checkout_formatted_fixed_cart_or_product_discount_price( $currencies_data, $coupon, $coupon_html, $discount_type, $discount_amount ) {

		if ( 'fixed_product' === $discount_type ) {
			$discount_amount = (float) $coupon->get_data()['amount'] * SupportHelper::get_product_quantity_item_qty( true );
		}
		if ( apply_filters( 'yay_currency_incl_tax_enable', false ) ) {
			$discount_totals     = WC()->cart->get_coupon_discount_totals();
			$discount_tax_totals = WC()->cart->get_coupon_discount_tax_totals();
			$discount_totals     = wc_array_merge_recursive_numeric( $discount_totals, $discount_tax_totals );
			$discount_amount     = $discount_totals[ $coupon->get_code() ];
		}
		if ( apply_filters( 'yay_currency_excl_tax_enable', false ) ) {
			$tax_rate_percent = apply_filters( 'yay_currency_get_rate_percent_in_cart', false );
			if ( $tax_rate_percent ) {
				$discount_amount = $discount_amount / ( 1 + $tax_rate_percent );
			}
		}

		$converted_discount_amount = YayCurrencyHelper::calculate_price_by_currency( $discount_amount, true, $currencies_data['current_currency'] );
		$formatted_discount_amount = YayCurrencyHelper::format_price( $converted_discount_amount );
		$discount_amount_html      = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $discount_amount );
		$discount_amount_html      = apply_filters( 'yay_currency_get_fixed_cart_or_product_discount_amount_html', $discount_amount_html, $discount_amount, $currencies_data['fallback_currency'] );
		$converted_approximately   = SupportHelper::display_approximately_converted_price( $currencies_data['current_currency'] );

		if ( ! $converted_approximately ) {
			return '-' . $discount_amount_html . ' ' . substr( $coupon_html, strpos( $coupon_html, '<a' ) ) . '';
		}

		//  Display approximate price only on the checkout page
		if ( SupportHelper::display_approximate_price_checkout_only() ) {
			return '-' . $formatted_discount_amount;
		}
		$formatted_discount_amount_html = YayCurrencyHelper::converted_approximately_html( $formatted_discount_amount );
		$formatted_coupon_html          = '-' . $discount_amount_html . $formatted_discount_amount_html . substr( $coupon_html, strpos( $coupon_html, '<a' ) ) . '';
		return $formatted_coupon_html;
	}

	// SHIPPING

	public function formatted_shipping_flat_rate_fee( $formatted_shipping_fee, $method, $apply_currency ) {
		if ( 'flat_rate' === $method->method_id ) {
			$data         = array( 'apply_currency' => $apply_currency );
			$shipping_fee = SupportHelper::get_total_shipping_fee_flat_rate_method( 0, $method, $data );
			if ( ! $shipping_fee || empty( $shipping_fee ) ) {
				return $formatted_shipping_fee;
			}
			$formatted_shipping_fee = YayCurrencyHelper::format_price( $shipping_fee );
		}
		return $formatted_shipping_fee;
	}

	// CUSTOM WITH FALLBACK CURRENCY IS NOT DEFAULT CURRENCY ( FIXED PRODUCT PRICE OR ROUNDING PRICE ENABLE)

	public function get_original_product_subtotal( $original_product_subtotal, $product_price, $product, $quantity, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency, 'product', $product ) ) {
			$product_id                = $product->get_id();
			$product_price             = SupportHelper::get_product_price( $product_id, $fallback_currency );
			$product_price             = apply_filters( 'yay_currency_get_product_price_fallback_by_3rd_plugin', $product_price, $product, $fallback_currency );
			$product_price             = apply_filters( 'yay_currency_get_product_price_apply_currency', $product_price, $product, 1 );
			$original_product_subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $product_price * $quantity, $fallback_currency, $fallback_currency['currency'] );
		}
		return $original_product_subtotal;
	}

	public function get_original_cart_subtotal( $original_subtotal, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$cart_subtotal     = apply_filters( 'yay_currency_get_cart_subtotal', 0, $fallback_currency );
			$original_subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $cart_subtotal, $fallback_currency, $fallback_currency['currency'] );
		}
		return $original_subtotal;
	}

	public function get_fixed_cart_or_product_discount_amount_html( $discount_amount_html, $discount_amount, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$discount_price       = YayCurrencyHelper::calculate_price_by_currency( $discount_amount, true, $fallback_currency );
			$discount_amount_html = YayCurrencyHelper::get_formatted_total_by_convert_currency( $discount_price, $fallback_currency, $fallback_currency['currency'] );
		}
		return $discount_amount_html;
	}

	public function get_percent_discount_amount_html( $discount_amount_html, $discount_amount, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$cart_subtotal        = apply_filters( 'yay_currency_get_cart_subtotal', 0, $fallback_currency );
			$discount_price       = ( $cart_subtotal * $discount_amount ) / 100;
			$discount_amount_html = YayCurrencyHelper::get_formatted_total_by_convert_currency( $discount_price, $fallback_currency, $fallback_currency['currency'] );
		}
		return $discount_amount_html;
	}

	public function get_fallback_currency_shipping_fee( $formatted_fallback_currency_shipping_fee, $shipping_fee, $method, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$fallback_shipping_fee = YayCurrencyHelper::calculate_price_by_currency( $shipping_fee, true, $fallback_currency );
			if ( 'flat_rate' === $method->method_id ) {
				$data                  = array(
					'apply_currency' => $fallback_currency,
					'is_fallback'    => true,
				);
				$fallback_shipping_fee = SupportHelper::get_total_shipping_fee_flat_rate_method( $shipping_fee, $method, $data );
			}
			$formatted_fallback_currency_shipping_fee = YayCurrencyHelper::get_formatted_total_by_convert_currency( $fallback_shipping_fee, $fallback_currency, $fallback_currency['currency'] );
		}
		return $formatted_fallback_currency_shipping_fee;
	}

	public function get_fee_amount_html( $fee_amount_html, $fee_amount, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$fee_amount      = YayCurrencyHelper::calculate_price_by_currency( $fee_amount, true, $fallback_currency );
			$fee_amount_html = YayCurrencyHelper::get_formatted_total_by_convert_currency( $fee_amount, $fallback_currency, $fallback_currency['currency'] );
		}
		return $fee_amount_html;
	}

	public function get_fallback_tax_amount( $fallback_tax_amount, $tax_amount, $tax_rate_id, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$total_tax = SupportHelper::get_total_tax_is_fallback( $fallback_currency );
			if ( TaxHelper::is_apply_multiple_taxes() && TaxHelper::is_use_excl_tax() ) {
				$total_tax = TaxHelper::recalculate_total_tax_excl_by_rate_id( $total_tax, $tax_rate_id, $fallback_currency );
			} elseif ( apply_filters( 'yay_currency_is_more_one_tax_apply_in_cart', false ) ) {
					$total_tax = apply_filters( 'yay_currency_recalculate_total_tax_by_rate_id', $total_tax, $tax_rate_id, $fallback_currency );
			} else {
				$total_tax = apply_filters( 'yay_currency_recalculate_total_tax', $total_tax, $fallback_currency );
			}

			$fallback_tax_amount = YayCurrencyHelper::get_formatted_total_by_convert_currency( $total_tax, $fallback_currency, $fallback_currency['currency'] );
		}
		return $fallback_tax_amount;
	}

	public function get_original_cart_total( $original_total, $total_price, $fallback_currency ) {
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$original_total = apply_filters( 'yay_currency_get_cart_total', 0, $fallback_currency, true );
			if ( apply_filters( 'yay_currency_is_more_one_tax_apply_in_cart', false ) ) {
				$original_total = SupportHelper::recalculate_checkout_cart_total( $fallback_currency );
			}

			$original_total = YayCurrencyHelper::get_formatted_total_by_convert_currency( $original_total, $fallback_currency, $fallback_currency['currency'] );
		}
		return $original_total;
	}

	// FORCE PAYMENT BY COUNTRY CODE
	public function get_apply_currency_force_country_code( $apply_currency ) {
		if ( CountryHelper::force_payment_country() ) {

			// Force via Country in Checkout page
			if ( CountryHelper::detect_force_country_by_checkout_page() ) {
				$currency_force_country_code = CountryHelper::get_apply_currency_by_force_payment_country();
				if ( $currency_force_country_code ) {
					return $currency_force_country_code;
				}
			}

			// Force via Shipping in Cart page
			if ( CountryHelper::detect_force_country_by_cart_page() ) {
				$currency_force_country_code = CountryHelper::get_apply_currency_by_force_shipping_country_cart_page();
				if ( $currency_force_country_code ) {
					return $currency_force_country_code;
				}
			}

			// Force via Checkout or Cart page ( Blocks )
			$apply_currency = apply_filters( 'yay_currency_detect_with_blocks_pages', $apply_currency );

		}

		$force_currency = Helper::detect_force_currency_by_payment_method();
		if ( $force_currency && isset( $force_currency['force_currency_payment_options'] ) ) {
			$apply_currency_by_payment_selected = YayCurrencyHelper::get_apply_currency_by_force_payment_selected( $force_currency );
			if ( $apply_currency_by_payment_selected ) {
				return $apply_currency_by_payment_selected;
			}
		}

		return $apply_currency;
	}

	public function yay_currency_ajax_handle_response_force_payment_results( $results, $data, $old_apply_currency, $new_apply_currency ) {
		$results['current_symbol'] = $new_apply_currency['symbol'];

		// Block gutenberg YayCurrency switcher
		$blocks = ! empty( $data['blocks'] ) ? map_deep( wp_unslash( $data['blocks'] ), 'sanitize_text_field' ) : array();
		if ( $blocks ) {
			foreach ( $blocks as $block ) {
				$results['block_content'][ $block['isBlockID'] ] = Helper::ajax_get_currency_switcher_html( 'block', $block );
			}
		}

		// Widget YayCurrency switcher
		$widget = isset( $data['widget'] ) ? intval( sanitize_text_field( $data['widget'] ) ) : false;
		if ( $widget ) {
			$results['widget_content'] = Helper::ajax_get_currency_switcher_html();
		}
		// Menu ShortCode YayCurrency switcher
		$menu = isset( $data['menu'] ) ? intval( sanitize_text_field( $data['menu'] ) ) : false;
		if ( $menu ) {
			$results['menu_content'] = Helper::ajax_get_currency_switcher_html( 'menu' );
		}
		// ShortCode YayCurrency switcher
		$shortcode = isset( $data['shortcode'] ) ? intval( sanitize_text_field( $data['shortcode'] ) ) : false;
		if ( $shortcode ) {
			$results['shortcode_content'] = Helper::ajax_get_currency_switcher_html( 'shortcode' );
		}

		// Country notice html
		$country_notice = isset( $data['country_notice'] ) ? intval( sanitize_text_field( $data['country_notice'] ) ) : false;
		if ( $country_notice ) {
			$results['country_notice'] = CountryHelper::get_country_currency_notice( $new_apply_currency );
		}

		// ShortCode price html
		if ( isset( $data['shortcode_default_price'] ) ) {
			foreach ( $data['shortcode_default_price'] as $key => $price ) {
				$results['shortcode_price_html'][ $key ] = YayCurrencyHelper::calculate_price_by_currency_html( $new_apply_currency, $price );
			}
		}

		// ShortCode product price html
		if ( isset( $data['shortcode_product_ids'] ) ) {
			foreach ( $data['shortcode_product_ids'] as $key => $product_id ) {
				$product = wc_get_product( $product_id );
				if ( ! $product ) {
					continue;
				}
				$results['shortcode_product_price_html'][ $key ] = $product->get_price_html();
			}
		}

		$foce_payment_notice = isset( $data['foce_payment_notice'] ) ? intval( sanitize_text_field( $data['foce_payment_notice'] ) ) : false;
		if ( $foce_payment_notice ) {
			$country_code_old                     = isset( $data['country_code'] ) ? sanitize_text_field( $data['country_code'] ) : false;
			$country_code_new                     = CountryHelper::detect_country_code();
			$results['force_payment_notice_html'] = $country_code_old === $country_code_new ? CountryHelper::get_force_payment_notice_html( $old_apply_currency ) : '';
		}

		if ( YayCurrencyHelper::is_dis_checkout_diff_currency( $new_apply_currency ) ) {
			if ( isset( $data['is_cart_page'] ) && '' === $data['is_cart_page'] ) {
				$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $new_apply_currency, YayCurrencyHelper::converted_currency() );
				if ( isset( $data['woocommerce_blocks'] ) ) {
					$results['force_payment_blocks_current_currency']       = $new_apply_currency['currency'];
					$results['force_payment_blocks_cart_subtotal_fallback'] = 'yes';
					$results['force_payment_blocks_checkout_notice_html']   = apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $currencies_data, Helper::get_current_theme() );
				} else {
					$results['force_payment_is_dis_checkout_diff_currency'] = 'yes';
					$results['force_payment_checkout_notice_html']          = apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $currencies_data, Helper::get_current_theme() );
				}
			}
		}

		if ( SupportHelper::detect_payment_reload_page() ) {
			if ( $old_apply_currency['currency'] !== $new_apply_currency['currency'] ) {
				$results['reload_page'] = 'yes';
			}
		}

		if ( apply_filters( 'yay_currency_multiple_language_active', false ) ) {
			$cookie_switcher_name = YayCurrencyHelper::get_cookie_name( 'switcher' );
			YayCurrencyHelper::set_cookie( $cookie_switcher_name, $new_apply_currency['ID'] );
		}

		return $results;
	}

	public function yay_currency_ajax_handle_response_force_currency_results( $results, $data, $old_apply_currency, $new_apply_currency ) {

		$results['current_symbol'] = $new_apply_currency['symbol'];
		// Block gutenberg YayCurrency switcher
		$blocks = ! empty( $data['blocks'] ) ? map_deep( wp_unslash( $data['blocks'] ), 'sanitize_text_field' ) : array();
		if ( $blocks ) {
			foreach ( $blocks as $block ) {
				$results['block_content'][ $block['isBlockID'] ] = Helper::ajax_get_currency_switcher_html( 'block', $block );
			}
		}
		// Widget YayCurrency switcher
		$widget = isset( $data['widget'] ) ? intval( sanitize_text_field( $data['widget'] ) ) : false;
		if ( $widget ) {
			$results['widget_content'] = Helper::ajax_get_currency_switcher_html();
		}
		// Menu ShortCode YayCurrency switcher
		$menu = isset( $data['menu'] ) ? intval( sanitize_text_field( $data['menu'] ) ) : false;
		if ( $menu ) {
			$results['menu_content'] = Helper::ajax_get_currency_switcher_html( 'menu' );
		}
		// ShortCode YayCurrency switcher
		$shortcode = isset( $data['shortcode'] ) ? intval( sanitize_text_field( $data['shortcode'] ) ) : false;
		if ( $shortcode ) {
			$results['shortcode_content'] = Helper::ajax_get_currency_switcher_html( 'shortcode' );
		}

		// Country notice html
		$country_notice = isset( $data['country_notice'] ) ? intval( sanitize_text_field( $data['country_notice'] ) ) : false;
		if ( $country_notice ) {
			$results['country_notice'] = CountryHelper::get_country_currency_notice( $new_apply_currency );
		}

		// ShortCode price html
		if ( isset( $data['shortcode_default_price'] ) ) {
			foreach ( $data['shortcode_default_price'] as $key => $price ) {
				$results['shortcode_price_html'][ $key ] = YayCurrencyHelper::calculate_price_by_currency_html( $new_apply_currency, $price );
			}
		}

		// ShortCode product price html
		if ( isset( $data['shortcode_product_ids'] ) ) {
			foreach ( $data['shortcode_product_ids'] as $key => $product_id ) {
				$product = wc_get_product( $product_id );
				if ( ! $product ) {
					continue;
				}
				$results['shortcode_product_price_html'][ $key ] = $product->get_price_html();
			}
		}

		if ( YayCurrencyHelper::is_dis_checkout_diff_currency( $new_apply_currency ) ) {
			if ( isset( $data['is_cart_page'] ) && '' === $data['is_cart_page'] ) {
				$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $new_apply_currency, YayCurrencyHelper::converted_currency() );
				if ( isset( $data['woocommerce_blocks'] ) ) {
					$results['force_payment_blocks_current_currency']       = $new_apply_currency['currency'];
					$results['force_payment_blocks_cart_subtotal_fallback'] = 'yes';
					$results['force_payment_blocks_checkout_notice_html']   = apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $currencies_data, Helper::get_current_theme() );
				} else {
					$results['force_payment_is_dis_checkout_diff_currency'] = 'yes';
					$results['force_payment_checkout_notice_html']          = apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $currencies_data, Helper::get_current_theme() );
				}
			}
		}

		if ( SupportHelper::detect_force_currency_reload_page() ) {
			if ( $old_apply_currency['currency'] !== $new_apply_currency['currency'] ) {
				$results['reload_page'] = 'yes';
			}
		}

		return $results;
	}
}
