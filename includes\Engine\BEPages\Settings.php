<?php
namespace Yay_Currency\Engine\BEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\RateHelper;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\TranslateHelper;

defined( 'ABSPATH' ) || exit;
/**
 * Settings Page
 */
class Settings {
	use SingletonTrait;

	private $currency_update;

	protected function __construct() {

		// Register Custom Post Type
		add_action( 'init', array( $this, 'register_post_type' ) );

		add_action( 'admin_menu', array( $this, 'admin_menu' ), YAY_CURRENCY_MENU_PRIORITY );
		add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );

		add_filter( 'plugin_action_links_' . YAY_CURRENCY_BASE_NAME, array( $this, 'addActionLinks' ) );
		add_filter( 'plugin_row_meta', array( $this, 'addDocumentSupportLinks' ), 10, 2 );
		add_filter( 'woocommerce_general_settings', array( $this, 'add_multi_currencies_button' ), 10, 1 );

		// WooCommerce Setting General Option
		add_filter( 'woocommerce_admin_settings_sanitize_option_woocommerce_currency', array( $this, 'update_currency_option' ), 10, 3 );
		add_filter( 'woocommerce_admin_settings_sanitize_option_woocommerce_currency_pos', array( $this, 'update_currency_meta_option' ), 10, 3 );
		add_filter( 'woocommerce_admin_settings_sanitize_option_woocommerce_price_thousand_sep', array( $this, 'update_currency_meta_option' ), 10, 3 );
		add_filter( 'woocommerce_admin_settings_sanitize_option_woocommerce_price_decimal_sep', array( $this, 'update_currency_meta_option' ), 10, 3 );
		add_filter( 'woocommerce_admin_settings_sanitize_option_woocommerce_price_num_decimals', array( $this, 'update_currency_meta_option' ), 10, 3 );

		// Cryptocurrencies
		add_filter( 'woocommerce_currencies', array( $this, 'add_cryptocurrencies_to_woocommerce_currencies' ) );
		add_filter( 'woocommerce_currency_symbols', array( $this, 'add_cryptocurrencies_to_woocommerce_currency_symbols' ) );

	}

	public function register_post_type() {
		$labels                 = array(
			'name'          => __( 'Currencies Manage', 'yay-currency' ),
			'singular_name' => __( 'Currency Manage', 'yay-currency' ),
		);
		$yay_currency_post_type = Helper::get_post_type();
		$args                   = array(
			'labels'            => $labels,
			'description'       => __( 'Currency Manage', 'yay-currency' ),
			'public'            => false,
			'show_ui'           => false,
			'has_archive'       => true,
			'show_in_admin_bar' => false,
			'show_in_rest'      => true,
			'show_in_menu'      => false,
			'query_var'         => $yay_currency_post_type,
			'supports'          => array(
				'title',
				'thumbnail',
			),
			'capabilities'      => array(
				'edit_post'          => 'manage_options',
				'read_post'          => 'manage_options',
				'delete_post'        => 'manage_options',
				'edit_posts'         => 'manage_options',
				'edit_others_posts'  => 'manage_options',
				'delete_posts'       => 'manage_options',
				'publish_posts'      => 'manage_options',
				'read_private_posts' => 'manage_options',
			),
		);

		register_post_type( $yay_currency_post_type, $args );

	}

	public function add_multi_currencies_button( $sections ) {
		$update_sections = array();
		foreach ( $sections as $section ) {
			if ( array_key_exists( 'id', $section ) && 'pricing_options' === $section['id'] ) {
				$section['desc'] = '<a class="button" href="' . esc_url( admin_url( '/admin.php?page=yay_currency' ) ) . '">' . esc_html__( 'Configure multi-currency', 'yay-currency' ) . '</a><br>' . esc_html__( 'The following options affect how prices are displayed on the frontend', 'yay-currency' );
			}
			$update_sections[] = $section;
		}
		return $update_sections;
	}

	public function addActionLinks( $links ) {
		$links = array_merge(
			array(
				'<a href="' . esc_url( admin_url( '/admin.php?page=yay_currency' ) ) . '">' . __( 'Settings', 'yay-currency' ) . '</a>',
			),
			$links
		);

		return $links;
	}

	public function addDocumentSupportLinks( $links, $file ) {
		if ( strpos( $file, YAY_CURRENCY_BASE_NAME ) !== false ) {
			$new_links = array(
				'doc'     => '<a href="https://yaycommerce.gitbook.io/yaycurrency/" target="_blank">' . __( 'Docs', 'yay-currency' ) . '</a>',
				'support' => '<a href="https://yaycommerce.com/support/" target="_blank" aria-label="' . esc_attr__( 'Visit community forums', 'yay-currency' ) . '">' . esc_html__( 'Support', 'yay-currency' ) . '</a>',
			);
			$links     = array_merge( $links, $new_links );
		}
		return $links;
	}

	public function admin_menu() {
		$page_title = __( 'YayCurrency', 'yay-currency' );
		$menu_title = __( 'YayCurrency', 'yay-currency' );
		add_submenu_page( 'yaycommerce', $page_title, $menu_title, 'manage_woocommerce', 'yay_currency', array( $this, 'submenu_page_callback' ), 0 );
	}

	public function admin_enqueue_scripts( $hook_suffix ) {

		do_action( 'yay_currency_admin_enqueue_scripts' );

		$allow_hook_suffixes = array( 'yaycommerce_page_yay_currency', 'nav-menus.php', 'widgets.php', 'post-new.php' );

		$is_set_fixed_price = get_option( 'yay_currency_set_fixed_price', 0 );
		if ( $is_set_fixed_price ) {
			array_push( $allow_hook_suffixes, 'post.php' );
		}

		if ( ! in_array( $hook_suffix, $allow_hook_suffixes ) ) {
			return;
		}

		if ( 'yaycommerce_page_yay_currency' === $hook_suffix ) {
			SupportHelper::detect_deregister_script();
		}

		wp_register_script( 'yay-currency', YAY_CURRENCY_PLUGIN_URL . 'assets/dist/js/main.js', array(), YAY_CURRENCY_VERSION, true );
		wp_localize_script(
			'yay-currency',
			'yayCurrency',
			array(
				'admin_url'                    => admin_url( 'admin.php?page=wc-settings' ),
				'plugin_url'                   => YAY_CURRENCY_PLUGIN_URL,
				'ajaxurl'                      => admin_url( 'admin-ajax.php' ),
				'nonce'                        => wp_create_nonce( 'yay-currency-nonce' ),
				'currenciesData'               => Helper::convert_currencies_data(),
				'listCurrencies'               => Helper::woo_list_currencies(),
				'listApproximatePriceElements' => array(
					'product_price_html'    => __( 'Product Price HTML', 'yay-currency' ),
					'product_price_in_cart' => __( 'Product Price in Cart', 'yay-currency' ),
					'product_subtotal'      => __( 'Product Subtotal', 'yay-currency' ),
					'subtotal'              => __( 'Subtotal', 'yay-currency' ),
					'coupon'                => __( 'Coupon', 'yay-currency' ),
					'shipping'              => __( 'Shipping', 'yay-currency' ),
					'fee'                   => __( 'Fee', 'yay-currency' ),
					'tax'                   => __( 'Tax', 'yay-currency' ),
					'total'                 => __( 'Total', 'yay-currency' ),
				),
				'approximatePricePosition'     => array(
					array(
						'label' => __( 'Before original price', 'yay-currency' ),
						'value' => 'before',
					),
					array(
						'label' => __( 'After original price', 'yay-currency' ),
						'value' => 'after',
					),
				),
				'currencyCodeByCountryCode'    => CountryHelper::currency_code_by_country_code(),
				'rest_url'                     => esc_url_raw( rest_url( 'yaycurrency/v1' ) ),
				'rest_nonce'                   => wp_create_nonce( 'wp_rest' ),
				'i18n'                         => TranslateHelper::get_translations(),
			)
		);

		wp_enqueue_style(
			'yay-currency',
			YAY_CURRENCY_PLUGIN_URL . 'assets/dist/main.css',
			array(
				'woocommerce_admin_styles',
				'wp-components',
			),
			YAY_CURRENCY_VERSION
		);

		wp_enqueue_style(
			'yay-currency-admin-styles',
			YAY_CURRENCY_PLUGIN_URL . 'src/admin/styles.css',
			array(),
			YAY_CURRENCY_VERSION
		);

		wp_enqueue_script( 'yay-currency' );
	}

	public function submenu_page_callback() {
		echo '<div id="yay-currency"></div>';
	}

	// Update Currency when save WooCommerce Setting General
	public function update_currency_option( $value, $option, $raw_value ) {
		$currencies = Helper::get_currencies_post_type();
		if ( $currencies ) {
			$this->currency_update = $value;
			$currency_update       = Helper::get_yay_currency_by_currency_code( $value );
			if ( ! $currency_update ) {
				Helper::create_new_currency( $value, true );
			} else {
				update_post_meta( $currency_update->ID, 'rate', '1' );
				update_post_meta(
					$currency_update->ID,
					'fee',
					array(
						'value' => '0',
						'type'  => get_post_meta(
							$currency_update->ID,
							'fee'
						)[0]['type'],
					)
				);
			}
			RateHelper::update_exchange_rate_currency( $currencies, $value );
			if ( class_exists( 'WC_Cache_Helper' ) ) {
				\WC_Cache_Helper::get_transient_version( 'product', true ); // Update product price (currency) after change value.
			}
		}
		return $value;
	}

	public function update_currency_meta_option( $value, $option, $raw_value ) {
		if ( ! empty( $this->currency_update ) ) {
			$currency_update = Helper::get_yay_currency_by_currency_code( $this->currency_update );
			$option_name     = isset( $option['id'] ) && ! empty( $option['id'] ) ? $option['id'] : false;
			$currency_id     = isset( $currency_update->ID ) && $currency_update->ID ? $currency_update->ID : false;
			if ( $currency_id && $option_name ) {
				$option_key = false;
				switch ( $option_name ) {
					case 'woocommerce_currency_pos':
						$option_key = 'currency_position';
						break;
					case 'woocommerce_price_thousand_sep':
						$option_key = 'thousand_separator';
						break;
					case 'woocommerce_price_decimal_sep':
						$option_key = 'decimal_separator';
						break;
					case 'woocommerce_price_num_decimals':
						$option_key = 'number_decimal';
						break;
					default:
						break;
				}
				if ( $option_key ) {
					update_post_meta( $currency_id, $option_key, $value );
				}
			}
		}

		return $value;
	}

	public function add_cryptocurrencies_to_woocommerce_currencies( $currencies ) {
		if ( ! isset( $currencies['ETH'] ) ) {
			$currencies['ETH'] = 'Ethereum';
		}

		return $currencies;
	}

	public function add_cryptocurrencies_to_woocommerce_currency_symbols( $currency_symbols ) {
		if ( ! isset( $currency_symbols['ETH'] ) ) {
			$currency_symbols['ETH'] = 'Ξ';
		}
		return $currency_symbols;
	}
}
