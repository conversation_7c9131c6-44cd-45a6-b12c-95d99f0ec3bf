<?php
namespace Yay_Currency\Engine;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\RateHelper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;

class Hooks {
	use SingletonTrait;

	public function __construct() {

		add_filter( 'wc_price_args', array( $this, 'custom_wc_price_args' ), 99, 1 );

		// GET CURRENCY RATE
		add_filter( 'yay_currency_rate', array( $this, 'get_yay_currency_rate' ), 10, 1 );
		add_filter( 'yay_currency_order_rate', array( $this, 'get_yay_currency_order_rate' ), 10, 3 );

		// GET DATA SHIPPING INFO
		add_filter( 'yay_currency_get_data_info_from_shipping_method', array( $this, 'get_data_info_from_shipping_method' ), 10, 4 );

		// DETECT BLOCK CONVERT PRICE
		add_filter( 'yay_currency_detect_block_convert_price', array( $this, 'detect_block_convert_price' ), 10, 1 );

		// NOTICE HTML CHECKOUT PAYMENT METHODS
		add_filter( 'yay_currency_notice_checkout_payment_methods', array( $this, 'get_notice_checkout_payment_methods' ), 10, 3 );

		// CUSTOM FIXED PRODUCT PRICE HTML

		add_filter( 'woocommerce_stripe_request_body', array( $this, 'custom_stripe_request_total_amount' ), 10, 2 );

		// CALCULATE TOTAL: CART SUBTOTAL, CART TOTAL --- DEFAULT CURRENCY
		add_filter( 'yay_currency_get_cart_subtotal_default', array( $this, 'calculate_cart_subtotal_default' ), 10, 1 );

		// CALCULATE TOTAL BY CURRENCY : PRICE, SUBTOTAL, DISCOUNT, SHIPPING, FEE, TAX, CART TOTAL,
		add_filter( 'yay_currency_get_cart_item_price', array( $this, 'get_cart_item_price' ), 10, 3 );
		add_filter( 'yay_currency_get_cart_subtotal', array( $this, 'get_cart_subtotal' ), 10, 2 );
		add_filter( 'yay_currency_get_discount_total', array( $this, 'calculate_discount_total' ), 10, 2 );
		add_filter( 'yay_currency_get_shipping_total', array( $this, 'calculate_shipping_total' ), 10, 3 );
		add_filter( 'yay_currency_get_fee_total', array( $this, 'calculate_fee_total' ), 10, 2 );
		add_filter( 'yay_currency_get_total_tax', array( $this, 'calculate_total_tax' ), 10, 2 );
		add_filter( 'yay_currency_get_cart_total', array( $this, 'calculate_cart_total' ), 10, 3 );

		// RELATED WITH ORDER
		add_action( 'yay_currency_set_order_original_total', array( $this, 'custom_set_yay_currency_order_info' ), 10, 5 );
		add_action( 'yay_currency_create_order_fallback_currency', array( $this, 'custom_checkout_create_order_fallback_currency' ), 10, 3 );
		add_action( 'yay_currency_create_create_order_line_item_fallback_currency', array( $this, 'create_create_order_line_item_fallback_currency' ), 10, 5 );
		add_action( 'yay_currency_create_order_shipping_item_fallback_currency', array( $this, 'create_order_shipping_item_fallback_currency' ), 10, 5 );
		add_action( 'yay_currency_create_order_fee_item_fallback_currency', array( $this, 'create_order_fee_item_fallback_currency' ), 10, 5 );
		add_action( 'yay_currency_create_order_tax_item_fallback_currency', array( $this, 'custom_order_tax_item_fallback_currency' ), 10, 4 );

		// RELATE WITH FALLBACK CURRENCY
		add_filter( 'yay_currency_get_price_fallback_in_checkout_page', array( $this, 'get_price_fallback_in_checkout_page' ), 10, 3 );
		add_filter( 'yay_currency_get_coupon_amount_fallback_currency', array( $this, 'get_coupon_amount_fallback_currency' ), 20, 2 );
		add_action( 'yay_currency_recalculate_cart_fees_fallback_currency', array( $this, 'recalculate_cart_fees_fallback_currency' ), 20, 2 );

		// ACTION - HOOKS
		add_action( 'yay_currency_redirect_to_url', array( $this, 'yay_currency_redirect_to_url' ), 10, 2 );
		add_action( 'yay_currency_admin_enqueue_scripts', array( $this, 'yay_currency_admin_enqueue_scripts' ) );
		add_action( 'yay_currency_callback_enqueue_scripts', array( $this, 'yay_currency_callback_enqueue_scripts' ), 10, 3 );

		// SHIPPING METHOD
		add_action( 'yay_currency_recalculate_shipping_cost', array( $this, 'recalculate_shipping_cost' ), 10, 5 );
		add_filter( 'yay_currency_recalculate_flat_rate_method_with_shortcode', array( $this, 'recalculate_flat_rate_method_with_shortcode' ), 10, 4 );
		add_filter( 'yay_currency_recalculate_flat_rate_shipping_class_cost', array( $this, 'recalculate_shipping_class_cost' ), 10, 4 );
		add_action( 'yay_currency_recalculate_flat_rate_method_cost', array( $this, 'recalculate_flat_rate_method_cost' ), 10, 3 );
		add_action( 'yay_currency_recalculate_tax_shipping_method_cost', array( $this, 'recalculate_tax_shipping_method_cost' ), 10, 2 );

		// RELATE WITH MANUAL ORDER
		add_action( 'yay_currency_handle_manual_order_line_items', array( $this, 'handle_manual_order_line_items' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_order_fee_lines', array( $this, 'handle_manual_order_fee_lines' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_order_shipping_lines', array( $this, 'handle_manual_order_shipping_lines' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_order_tax_lines', array( $this, 'handle_manual_order_tax_lines' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_order_coupon_lines', array( $this, 'handle_manual_order_coupon_lines' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_order_totals', array( $this, 'handle_manual_order_totals' ), 10, 3 );
		add_action( 'yay_currency_handle_manual_set_order_data', array( $this, 'handle_manual_set_order_data' ), 10, 3 );

		// CALCULATE PRICE
		add_filter( 'yay_currency_convert_price', array( $this, 'convert_price_callback' ), 10, 2 );
		add_filter( 'yay_currency_revert_price', array( $this, 'revert_price_callback' ), 10, 2 );
		add_filter( 'yay_currency_revert_product_price', array( $this, 'revert_product_price_callback' ), 10, 4 );

		// DETECT USING THE COUNTRY CODE ( USER'S BILLING COUNTRY )
		add_filter( 'yay_currency_by_country_code', array( $this, 'get_current_currency_by_country_code' ), 10, 1 );
		add_filter( 'yay_currency_by_billing_country_code', array( $this, 'get_current_currency_by_billing_country_code' ), 10, 1 );

		// REVERT PRICE TO DEFAULT
		add_filter( 'yay_currency_formatted_amount', array( $this, 'formatted_amount_callback' ), 10, 2 );
		add_filter( 'yay_currency_checkout_get_subtotal_price', array( $this, 'get_cart_subtotal_default_conditions' ), 10, 4 );
		add_filter( 'yay_currency_get_product_price_default_conditions', array( $this, 'get_product_price_default_conditions' ), 10, 5 );

		// EMAIL
		add_action( 'woocommerce_email_order_details', array( $this, 'set_email_order_id' ), 9, 4 );
	}

	public function custom_wc_price_args( $args ) {

		if ( isset( $args['currency'] ) && ! empty( $args['currency'] ) ) {

			$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $args['currency'] );

			if ( ! $apply_currency || YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {
				return $args;
			}

			$args['price_format'] = Helper::change_price_format( $apply_currency, $args['price_format'] );

		}

		return apply_filters( 'yay_currency_get_price_format', $args );
	}

	public function get_yay_currency_rate( $rate = 1 ) {
		$apply_currency = YayCurrencyHelper::detect_current_currency();
		$rate           = YayCurrencyHelper::get_rate_fee( $apply_currency );
		return $rate;
	}

	public function get_yay_currency_order_rate( $order_id, $rate_fee, $converted_currency ) {
		$converted_currency = $converted_currency ? $converted_currency : YayCurrencyHelper::converted_currency();
		$order              = wc_get_order( $order_id );

		if ( ! $order ) {
			return 1;
		}

		$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id, $converted_currency );

		if ( ! $order_currency ) {
			$rate_fee = RateHelper::get_rate_fee_from_currency_not_exists_in_list( $order->get_currency() );
			return $rate_fee ? $rate_fee : 1;
		}

		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$rate_fee = $order->get_meta( 'yay_currency_order_rate', true ) ? (float) $order->get_meta( 'yay_currency_order_rate', true ) : false;
		} else {
			$rate_fee = get_post_meta( $order_id, 'yay_currency_order_rate', true ) ? (float) get_post_meta( $order_id, 'yay_currency_order_rate', true ) : false;
		}

		return $rate_fee ? $rate_fee : YayCurrencyHelper::get_rate_fee( $order_currency );
	}

	public function get_data_info_from_shipping_method( $data, $method_id, $package_content_cost, $apply_currency ) {
		$methods = array( 'slovakparcelservice_pickupplace', 'slovakparcelservice_address' );
		if ( in_array( $method_id, $methods, true ) && isset( $data['free'] ) && ! empty( $data['free'] ) && is_numeric( $data['free'] ) ) {
			$free = YayCurrencyHelper::calculate_price_by_currency( $data['free'], true, $apply_currency );
			if ( $package_content_cost >= $free ) {
				$data['cost'] = 0;
			}
		}
		return $data;
	}

	public function detect_block_convert_price( $flag ) {
		// Detect Mobile App
		if ( isset( $_SERVER['HTTP_BAGGAGE'] ) ) {
			$user_agent = isset( $_SERVER['HTTP_USER_AGENT'] ) && ! empty( $_SERVER['HTTP_USER_AGENT'] ) ? sanitize_text_field( $_SERVER['HTTP_USER_AGENT'] ) : false;
			if ( $user_agent && ( stripos( $user_agent, 'wc-ios' ) !== false || stripos( $user_agent, 'wc-android' ) !== false ) ) {
				$flag = true;
			}
		}
		return $flag;
	}

	public function custom_stripe_request_total_amount( $request, $api ) {
		if ( isset( $request['currency'] ) && isset( $request['metadata'] ) && isset( $request['metadata']['order_id'] ) ) {
			$array_zero_decimal_currencies = array(
				'BIF',
				'CLP',
				'DJF',
				'GNF',
				'JPY',
				'KMF',
				'KRW',
				'MGA',
				'PYG',
				'RWF',
				'UGX',
				'VND',
				'VUV',
				'XAF',
				'XOF',
				'XPF',
			);
			if ( in_array( strtoupper( $request['currency'] ), $array_zero_decimal_currencies ) ) {
				$order_id = $request['metadata']['order_id'];
				if ( ! empty( $order_id ) ) {
					$order = wc_get_order( $order_id );
					if ( ! $order ) {
						return $request;
					}
					$order_total       = YayCurrencyHelper::get_total_by_order( $order );
					$request['amount'] = floatval( $order_total );
				}
			}
		}
		return $request;
	}

	// Calculate Total to Default

	public function calculate_cart_subtotal_default( $subtotal ) {
		$subtotal = SupportHelper::get_cart_subtotal_default();
		return $subtotal;
	}

	public function get_cart_subtotal( $cart_subtotal, $apply_currency ) {
		$cart_subtotal = SupportHelper::get_cart_subtotal( $apply_currency );
		return $cart_subtotal;
	}

	public function calculate_discount_total( $discount_total, $apply_currency = false ) {
		if ( ! $apply_currency ) {
			$cart_subtotal = apply_filters( 'yay_currency_get_cart_subtotal_default', 0 );
		} else {
			$cart_subtotal = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
		}
		$discount_total = SupportHelper::get_total_coupons( $cart_subtotal, $apply_currency );
		return $discount_total;
	}

	public function calculate_shipping_total( $shipping_total, $apply_currency, $is_fallback = false ) {
		$calculate_default = $apply_currency ? false : true;
		$shipping_total    = SupportHelper::get_shipping_total_selected( $apply_currency, $calculate_default, false, $is_fallback );
		return $shipping_total;
	}

	public function calculate_fee_total( $fee_total, $apply_currency ) {
		$calculate_default = $apply_currency ? false : true;
		$fee_total         = SupportHelper::get_total_fees( $apply_currency, false, $calculate_default );
		return $fee_total;
	}

	public function calculate_total_tax( $total_tax, $apply_currency ) {
		$total_tax = SupportHelper::get_total_tax( $apply_currency );
		return $total_tax;
	}

	public function calculate_cart_total( $total, $apply_currency, $is_fallback ) {
		$total = SupportHelper::get_cart_total( $apply_currency, $is_fallback );
		return $total;
	}

	public function custom_set_yay_currency_order_info( $order, $data, $default_currency_code, $fallback_currency, $current_currency ) {

		if ( 0 === intval( $current_currency['status'] ) && $fallback_currency['currency'] !== $default_currency_code ) {
			$rate_fee = YayCurrencyHelper::get_rate_fee( $fallback_currency );
		} else {
			$rate_fee = YayCurrencyHelper::get_rate_fee( $current_currency );
		}

		$rate_fee = $rate_fee ? $rate_fee : 1;

		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$order->delete_meta_data( '_yay_currency_order_synced' );
			$order->update_meta_data( 'yay_currency_order_rate', $rate_fee );
			$order->save();
		} else {
			$order_id = $order->get_id();
			delete_post_meta( $order_id, '_yay_currency_order_synced' );
			update_post_meta( $order_id, 'yay_currency_order_rate', $rate_fee );
		}
	}

	public function custom_checkout_create_order_fallback_currency( $order, $data, $fallback_currency ) {
		$order_subtotal       = YayCurrencyHelper::calculate_price_by_currency( $order->get_subtotal(), true, $fallback_currency );
		$order_total          = YayCurrencyHelper::calculate_price_by_currency( $order->get_total(), true, $fallback_currency );
		$order_discount_total = YayCurrencyHelper::calculate_price_by_currency( $order->get_discount_total(), true, $fallback_currency );
		$order_shipping_total = YayCurrencyHelper::calculate_price_by_currency( $order->get_shipping_total(), true, $fallback_currency );
		$order_cart_tax       = YayCurrencyHelper::calculate_price_by_currency( $order->get_cart_tax(), true, $fallback_currency );
		$order_shipping_tax   = YayCurrencyHelper::calculate_price_by_currency( $order->get_shipping_tax(), true, $fallback_currency );

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$order_subtotal       = apply_filters( 'yay_currency_get_cart_subtotal', 0, $fallback_currency );
			$order_discount_total = apply_filters( 'yay_currency_get_discount_total', 0, $fallback_currency );
			$order_shipping_total = apply_filters( 'yay_currency_get_shipping_total', 0, $fallback_currency, true );
			$order_total          = apply_filters( 'yay_currency_get_cart_total', 0, $fallback_currency, true );
		}

		$order->subtotal = $order_subtotal;
		$order->set_currency( $fallback_currency['currency'] );
		$order->set_total( $order_total );
		$order->set_discount_total( $order_discount_total );
		$order->set_shipping_total( $order_shipping_total );
		$order->set_cart_tax( $order_cart_tax );
		$order->set_shipping_tax( $order_shipping_tax );

		$rate_fee = YayCurrencyHelper::get_rate_fee( $fallback_currency );

		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$order->add_meta_data( 'yay_currency_order_rate', $rate_fee );
			$order->add_meta_data( '_yay_currency_order_synced', 'yes' );
		} else {
			$order_id = $order->get_id();
			update_post_meta( $order_id, 'yay_currency_order_rate', $rate_fee );
			update_post_meta( $order_id, '_yay_currency_order_synced', 'yes' );
		}

		$order->save();

	}

	public function create_create_order_line_item_fallback_currency( $item, $cart_item_key, $values, $order, $fallback_currency ) {
		$item_subtotal     = YayCurrencyHelper::calculate_price_by_currency( $values['line_subtotal'], true, $fallback_currency );
		$item_subtotal_tax = YayCurrencyHelper::calculate_price_by_currency( $values['line_subtotal_tax'], true, $fallback_currency );
		$item_total        = YayCurrencyHelper::calculate_price_by_currency( $values['line_total'], true, $fallback_currency );
		$item_total_tax    = YayCurrencyHelper::calculate_price_by_currency( $values['line_tax'], true, $fallback_currency );
		$product           = $item->get_product();

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency, 'product', $product ) ) {
			$product_info      = SupportHelper::get_product_subtotal_info( $product, $item->get_quantity(), $fallback_currency );
			$item_subtotal     = $product_info['subtotal'];
			$item_subtotal_tax = $product_info['subtotal_tax'];
			$item_total        = $item_subtotal;
			$item_total_tax    = $item_subtotal_tax;
		}

		$item->set_subtotal( $item_subtotal );
		$item->set_subtotal_tax( $item_subtotal_tax );
		$item->set_total( $item_total );
		$item->set_total_tax( $item_total_tax );

		$line_tax_data = $values['line_tax_data'];
		if ( $line_tax_data ) {
			if ( isset( $line_tax_data['subtotal'] ) ) {
				$rateId                               = key( $line_tax_data['subtotal'] );
				$line_tax_data['subtotal'][ $rateId ] = $item_subtotal_tax;
			}

			if ( isset( $line_tax_data['total'] ) ) {
				$rateId                            = key( $line_tax_data['total'] );
				$line_tax_data['total'][ $rateId ] = $item_total_tax;
			}
			$item->set_taxes( $line_tax_data );
		}

		$item->save();
	}

	public function create_order_shipping_item_fallback_currency( $item, $package_key, $package, $order, $fallback_currency ) {
		$item_total = YayCurrencyHelper::calculate_price_by_currency( $item->get_total(), true, $fallback_currency );
		$taxes      = $item->get_taxes();
		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$item_total              = apply_filters( 'yay_currency_get_shipping_total', 0, $fallback_currency, true );
			$chosen_shipping_methods = WC()->session->get( 'chosen_shipping_methods' );
			$shipping_rate           = $package['rates'][ $chosen_shipping_methods[ $package_key ] ];
			if ( $shipping_rate && isset( $shipping_rate->taxes ) && $shipping_rate->taxes ) {
				$rateId = key( $shipping_rate->taxes );
				if ( $rateId ) {
					$rate_percent   = \WC_Tax::get_rate_percent_value( $rateId );
					$taxes['total'] = array(
						$rateId => $item_total * $rate_percent / 100,
					);
				}
			}
		}

		$item->set_total( $item_total );
		if ( $taxes && isset( $taxes['total'] ) ) {
			$item->set_taxes( $taxes );
		}

		$item->save();
	}

	public function create_order_fee_item_fallback_currency( $item, $fee_key, $fee, $order, $fallback_currency ) {
		$item_total     = YayCurrencyHelper::calculate_price_by_currency( $fee->total, true, $fallback_currency );
		$item_total_tax = YayCurrencyHelper::calculate_price_by_currency( $fee->tax, true, $fallback_currency );
		$taxes          = $item->get_taxes();
		$item->set_total( $item_total );
		$item->set_total_tax( $item_total_tax );

		if ( $fee->tax_data ) {
			foreach ( $fee->tax_data as $rateId => $tax ) {
				$fee->tax_data[ $rateId ] = YayCurrencyHelper::calculate_price_by_currency( $tax, true, $fallback_currency );
			}
			$taxes['total'] = $fee->tax_data;
			$item->set_taxes( $taxes );
		}

		$item->save();
	}

	public function custom_order_tax_item_fallback_currency( $item, $tax_rate_id, $order, $fallback_currency ) {
		$cart               = WC()->cart;
		$tax_total          = $cart->get_tax_amount( $tax_rate_id );
		$tax_total          = YayCurrencyHelper::calculate_price_by_currency( $tax_total, true, $fallback_currency );
		$shipping_total_tax = $cart->get_shipping_tax_amount( $tax_rate_id );
		$shipping_total_tax = YayCurrencyHelper::calculate_price_by_currency( $shipping_total_tax, true, $fallback_currency );

		if ( YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $fallback_currency ) ) {
			$rate_percent       = \WC_Tax::get_rate_percent_value( $tax_rate_id );
			$sum_tax_total      = SupportHelper::get_total_tax_is_fallback( $fallback_currency );
			$sum_shipping_total = apply_filters( 'yay_currency_get_shipping_total', 0, $fallback_currency, true );
			$shipping_total_tax = $sum_shipping_total * $rate_percent / 100;
			$tax_total          = $sum_tax_total - $shipping_total_tax;
		}
		$item->set_tax_total( $tax_total );
		$item->set_shipping_tax_total( $shipping_total_tax );
		$item->save();
	}

	// RELATED WITH FALLBACK CURRENCY

	public function get_price_fallback_in_checkout_page( $price, $product, $fallback_currency ) {
		if ( SupportHelper::detect_recalculate_value_with_fallback_currency() ) {
			$price = YayCurrencyHelper::convert_product_price( $price, $product, $fallback_currency, false );
		}
		return $price;
	}

	public function get_coupon_amount_fallback_currency( $price, $fallback_currency ) {
		if ( SupportHelper::detect_recalculate_value_with_fallback_currency() ) {
			$price = YayCurrencyHelper::calculate_price_by_currency( $price, true, $fallback_currency );
		}
		return $price;
	}

	public function recalculate_cart_fees_fallback_currency( $cart, $fallback_currency ) {
		if ( SupportHelper::detect_recalculate_value_with_fallback_currency() ) {
			foreach ( $cart->get_fees() as $fee ) {
				$amount      = YayCurrencyHelper::calculate_price_by_currency( $fee->amount, true, $fallback_currency );
				$fee->amount = $amount;
			}
		}
	}

	// ACTION - HOOKS

	public function yay_currency_redirect_to_url( $current_url, $currency_ID ) {
		$current_currency    = YayCurrencyHelper::get_currency_by_ID( $currency_ID );
		$currency_param_name = apply_filters( 'yay_currency_param_name', 'yay-currency' );
		$current_url         = add_query_arg( array( $currency_param_name => $current_currency['currency'] ), $current_url );
		if ( wp_safe_redirect( $current_url ) ) {
			exit;
		}
	}

	public function get_notice_checkout_payment_methods( $notice_html, $currencies_data, $current_theme ) {
		if ( isset( $currencies_data['current_currency'] ) && isset( $currencies_data['fallback_currency'] ) ) {
			if ( Helper::default_currency_code() === $currencies_data['current_currency']['currency'] ) {
				return $notice_html;
			}
			$custom_notice_html = '';
			if ( CountryHelper::force_payment_country() || Helper::detect_force_currency_by_payment_method() ) {
				$custom_notice_html .= '<div class="yay-currency-checkout-notice-force-payment-wrapper">';
			}
			$custom_notice_html .= '<div class="yay-currency-checkout-notice user yay-currency-with-' . esc_attr( $current_theme ) . '"><span>' . esc_html__( 'The current payment method for ', 'yay-currency' ) . '<strong class="yay-currency-checkout-notice-current-currency">' . wp_kses_post( html_entity_decode( esc_html__( $currencies_data['current_currency']['currency'], 'yay-currency' ) ) ) . '</strong></span><span>' . esc_html__( ' is not supported in your location. ', 'yay-currency' ) . '</span><span>' . esc_html__( 'So your payment will be recorded in ', 'yay-currency' ) . '</span><strong class="yay-currency-checkout-notice-fallback-currency">' . wp_kses_post( html_entity_decode( esc_html__( $currencies_data['fallback_currency']['currency'], 'yay-currency' ) ) ) . '.</strong></span></div>';
			if ( current_user_can( 'manage_options' ) ) {
				$custom_notice_html .= "<div class='yay-currency-checkout-notice-admin yay-currency-with-" . esc_attr( $current_theme ) . "'><span>" . esc_html__( 'Are you the admin? You can change the checkout options for payment methods ', 'yay-currency' ) . '<a href=' . esc_url( admin_url( '/admin.php?page=yay_currency&tabID=1' ) ) . '>' . esc_html__( 'here', 'yay-currency' ) . '</a>.</span><br><span><i>' . esc_html__( '(Only logged in admin can see this.)', 'yay-currency' ) . '</i></span></div>';
			}
			if ( CountryHelper::force_payment_country() || Helper::detect_force_currency_by_payment_method() ) {
				$custom_notice_html .= '</div>';
			}
			return apply_filters( 'yay_currency_checkout_notice_html', $custom_notice_html, $currencies_data, $current_theme );
		}
		return $notice_html;
	}

	protected function get_sync_currencies() {
		$default_currency = Helper::default_currency_code();
		$currencies       = Helper::woo_list_currencies();
		unset( $currencies[ $default_currency ] );
		return apply_filters( 'yay_currency_sync_currencies', array_keys( $currencies ) );
	}

	public function yay_currency_admin_enqueue_scripts() {
		$sync_notice_args = array(
			'reverted'      => apply_filters( 'yay_currency_convert_all_orders_to_base', get_option( 'yay_currency_orders_synced_to_base', 'no' ) ),
			'notice_title'  => __( 'YayCurrency database update', 'yay-currency' ),
			'notice_desc'   => __( 'Recommended: You can force a database update for past orders so that the revenue recorded in different currencies will be recorded in your default currency. This action will convert the sales based on the current exchange rate.', 'yay-currency' ),
			'notice_button' => __( 'Convert all orders', 'yay-currency' ),
		);
		$localize_args    = array(
			'sync_orders'     => $sync_notice_args,
			'sync_currencies' => self::get_sync_currencies(),
			'nonce'           => wp_create_nonce( 'yay-currency-admin-nonce' ),
		);
		wp_enqueue_script( 'yay-currency-admin-script', YAY_CURRENCY_PLUGIN_URL . 'src/admin/script.js', array( 'jquery' ), YAY_CURRENCY_VERSION, true );
		wp_localize_script(
			'yay-currency-admin-script',
			'yayCurrency_Admin',
			apply_filters( 'yay_currency_admin_localize_args', $localize_args )
		);
	}

	public function yay_currency_callback_enqueue_scripts( $localize_args, $force_payment, $apply_currency ) {

		if ( $localize_args ) {
			$localize_args['minicart_contents_class'] = apply_filters( 'yay_currency_minicart_contents_class', 'a.cart-contents', $localize_args );
		}

		wp_localize_script(
			'yay-currency-callback',
			'yay_callback_data',
			apply_filters( 'yay_currency_callback_localize_args', $localize_args )
		);
	}

	public function recalculate_shipping_cost( $methods, $package, $apply_currency, $fallback_currency, $default_currency ) {
		$apply_currency = YayCurrencyHelper::get_current_currency( $apply_currency );

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout', $apply_currency ) || apply_filters( 'yay_currency_is_original_default_currency', false, $apply_currency ) ) {
			if ( apply_filters( 'yay_currency_is_original_default_currency', false, $apply_currency ) || YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout', $apply_currency ) ) {
				foreach ( $methods as $key => $method ) {
					if ( 'flat_rate' !== $method->method_id ) {
						continue;
					}
					$has_fee_costs = false;
					$shipping      = new \WC_Shipping_Flat_Rate( $method->instance_id );
					$cost          = $shipping->get_option( 'cost' );
					// Calculate the costs.
					$rate = array(
						'id'      => $method->id,
						'label'   => $method->label,
						'cost'    => 0,
						'package' => $package,
					);

					if ( ! empty( $cost ) && ! is_numeric( $cost ) ) {
						$has_fee_costs         = true;
						$package_contents_cost = $package['contents_cost'];
						$package_contents_cost = apply_filters( 'yay_currency_get_cart_subtotal', 0, $fallback_currency );
						$rate['cost']          = SupportHelper::evaluate_cost(
							$cost,
							array(
								'qty'  => $shipping->get_package_item_qty( $package ),
								'cost' => $package_contents_cost,
							),
							$default_currency === $fallback_currency['currency'] ? true : false,
							$default_currency === $fallback_currency['currency'] ? false : true
						);

						if ( is_numeric( $rate['cost'] ) && ! strpos( $cost, 'fee' ) ) {
							$rate['cost'] = YayCurrencyHelper::calculate_price_by_currency( $rate['cost'], true, $fallback_currency );
						}
					}

					$shipping_classes = WC()->shipping->get_shipping_classes();

					if ( ! empty( $shipping_classes ) ) {
						$product_shipping_classes = $shipping->find_shipping_classes( $package );
						$shipping_classes_cost    = 0;

						foreach ( $product_shipping_classes as $shipping_class => $products ) {
							$shipping_class_term = get_term_by( 'slug', $shipping_class, 'product_shipping_class' );
							$class_cost_string   = $shipping_class_term && $shipping_class_term->term_id ? $shipping->get_option( 'class_cost_' . $shipping_class_term->term_id, $shipping->get_option( 'class_cost_' . $shipping_class, '' ) ) : $shipping->get_option( 'no_class_cost', '' );

							if ( '' === $class_cost_string ) {
								continue;
							}
							if ( ! empty( $class_cost_string ) && ! is_numeric( $class_cost_string ) ) {
								$has_fee_costs = true;
								$class_cost    = SupportHelper::evaluate_cost(
									$class_cost_string,
									array(
										'qty'  => array_sum( wp_list_pluck( $products, 'quantity' ) ),
										'cost' => array_sum( wp_list_pluck( $products, 'line_total' ) ),
									)
								);
								if ( ! empty( $cost ) && is_numeric( $cost ) ) {
									$class_cost += $cost;
								}
							} else {
								if ( ! empty( $cost ) && is_numeric( $cost ) ) {
									$cost += $class_cost_string;
								}
								$class_cost = $class_cost_string;
							}

							if ( is_numeric( $class_cost ) && ! strpos( $class_cost_string, 'fee' ) ) {
								$class_cost = YayCurrencyHelper::calculate_price_by_currency( $class_cost, false, $fallback_currency );
							}

							if ( 'class' === $shipping->type ) {
								$rate['cost'] += $class_cost;
							} else {
								$shipping_classes_cost = $class_cost > $shipping_classes_cost ? $class_cost : $shipping_classes_cost;
							}
						}

						if ( 'order' === $shipping->type && $shipping_classes_cost ) {
							$rate['cost'] += $shipping_classes_cost;
						}
					}

					if ( ! $has_fee_costs ) {
						$rate['cost'] = YayCurrencyHelper::calculate_price_by_currency( $cost, false, $fallback_currency );
					} else {
						$_REQUEST['yay_currency_recalculate_fee_cost'] = true;
					}
					$method->set_cost( $rate['cost'] );
					// Set tax for shipping method
					if ( count( $method->get_taxes() ) > 0 ) {
						$tax_new = array();
						foreach ( $method->get_taxes() as $key => $tax ) {
							$tax_currency   = YayCurrencyHelper::calculate_price_by_currency( $tax, true, $fallback_currency );
							$shipping_rates = \WC_Tax::get_shipping_tax_rates();
							if ( isset( $cost ) && ! is_numeric( $cost ) ) {
								$tax_calculate   = \WC_Tax::calc_shipping_tax( $rate['cost'], $shipping_rates );
								$tax_new[ $key ] = is_array( $tax_calculate ) ? array_sum( $tax_calculate ) : $tax_currency;
							} else {
								if ( $shipping_rates && isset( $shipping_rates[ $key ] ) && isset( $apply_currency['roundingType'] ) && 'disabled' !== $apply_currency['roundingType'] ) {
									$shipping_rate = isset( $shipping_rates[ $key ]['rate'] ) ? $shipping_rates[ $key ]['rate'] : 0;
									$rate_percent  = floatval( $shipping_rate / 100 );
									$tax_currency  = $method->cost * $rate_percent;
								}
								$tax_new[ $key ] = $tax_currency;
							}
						}
						$method->set_taxes( $tax_new );
					}
				}
			}
		}
	}

	public function recalculate_flat_rate_method_with_shortcode( $shipping_args, $package, $shipping, $data ) {

		$cost                           = $shipping_args['cost'];
		$rate_cost                      = $shipping_args['rate_cost'];
		$shipping_args['has_fee_costs'] = true;
		$apply_currency                 = YayCurrencyHelper::get_current_currency( $data['apply_currency'] );
		$is_original_default_currency   = $data['is_original_default_currency'];
		$package_contents_cost          = $package['contents_cost'];
		$calculate_default              = false;
		$is_fallback                    = false;
		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || $is_original_default_currency ) {
			if ( $is_original_default_currency && $data['is_fallback_currency'] ) {
				$package_contents_cost = apply_filters( 'yay_currency_get_cart_subtotal', 0, $data['fallback_currency'] );
				$is_fallback           = true;
			} else {
				$package_contents_cost = apply_filters( 'yay_currency_get_cart_subtotal_default', 0 );
				$calculate_default     = true;
			}
		}
		$rate_cost = SupportHelper::evaluate_cost(
			$cost,
			array(
				'qty'  => $shipping->get_package_item_qty( $package ),
				'cost' => $package_contents_cost,
			),
			$calculate_default,
			$is_fallback
		);

		if ( is_numeric( $rate_cost ) && ! strpos( $cost, 'fee' ) ) {
			if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || $is_original_default_currency ) {
				if ( $is_original_default_currency && $data['is_fallback_currency'] ) {
					$rate_cost = YayCurrencyHelper::calculate_price_by_currency( $rate_cost, false, $data['fallback_currency'] );
				}
			} else {
				$rate_cost = YayCurrencyHelper::calculate_price_by_currency( $rate_cost, false, $apply_currency );
			}
		}
		$shipping_args['rate_cost'] = $rate_cost;
		return $shipping_args;
	}

	public function recalculate_shipping_class_cost( $shipping_class_args, $package, $shipping, $data ) {
		$shipping_classes = WC()->shipping->get_shipping_classes();

		if ( ! empty( $shipping_classes ) ) {
			$apply_currency               = YayCurrencyHelper::get_current_currency( $data['apply_currency'] );
			$is_original_default_currency = $data['is_original_default_currency'];
			$product_shipping_classes     = $shipping->find_shipping_classes( $package );
			$shipping_classes_cost        = 0;
			$rate_cost                    = $shipping_class_args['rate_cost'];
			$cost                         = $shipping_class_args['cost'];
			foreach ( $product_shipping_classes as $shipping_class => $products ) {
				$shipping_class_term = get_term_by( 'slug', $shipping_class, 'product_shipping_class' );
				$class_cost_string   = $shipping_class_term && $shipping_class_term->term_id ? $shipping->get_option( 'class_cost_' . $shipping_class_term->term_id, $shipping->get_option( 'class_cost_' . $shipping_class, '' ) ) : $shipping->get_option( 'no_class_cost', '' );

				if ( empty( $class_cost_string ) ) {
					continue;
				}
				if ( ! empty( $class_cost_string ) && ! is_numeric( $class_cost_string ) ) {
					$shipping_class_args['has_fee_costs'] = true;
					$class_cost                           = SupportHelper::evaluate_cost(
						$class_cost_string,
						array(
							'qty'  => array_sum( wp_list_pluck( $products, 'quantity' ) ),
							'cost' => array_sum( wp_list_pluck( $products, 'line_total' ) ),
						)
					);
					if ( ! empty( $cost ) && is_numeric( $cost ) ) {
						$class_cost += $cost;
					}
				} else {
					if ( ! empty( $cost ) && is_numeric( $cost ) ) {
						$cost += $class_cost_string;
					}
					$class_cost = $class_cost_string;
				}

				if ( is_numeric( $class_cost ) && ! strpos( $class_cost_string, 'fee' ) ) {
					if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || $is_original_default_currency ) {
						if ( $is_original_default_currency && $data['is_fallback_currency'] ) {
							$class_cost = YayCurrencyHelper::calculate_price_by_currency( $class_cost, false, $data['fallback_currency'] );
						}
					} else {
						$class_cost = YayCurrencyHelper::calculate_price_by_currency( $class_cost, false, $apply_currency );
					}

					if ( 'class' === $shipping->type ) {
						$rate_cost += $class_cost;
					} else {
						$shipping_classes_cost = $class_cost > $shipping_classes_cost ? $class_cost : $shipping_classes_cost;
					}
				}

				if ( 'order' === $shipping->type && $shipping_classes_cost ) {
					$rate_cost += $shipping_classes_cost;
				}
			}
			$shipping_class_args['rate_cost'] = $rate_cost;
			$shipping_class_args['cost']      = $cost;

		}

		return $shipping_class_args;
	}

	public function recalculate_flat_rate_method_cost( $method, $rate, $data ) {
		$apply_currency               = YayCurrencyHelper::get_current_currency( $data['apply_currency'] );
		$is_original_default_currency = $data['is_original_default_currency'];
		$cost                         = ! empty( $data['cost'] ) ? $data['cost'] : $method->cost;
		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || $is_original_default_currency ) {
			if ( $is_original_default_currency && $data['is_fallback_currency'] ) {
				$rate_cost = YayCurrencyHelper::calculate_price_by_currency( $cost, false, $data['fallback_currency'] );
			} else {
				$rate_cost = $cost;
			}
			$shipping_cost = apply_filters( 'yay_currency_get_original_shipping_cost', $rate_cost, $apply_currency );
		} else {
			$shipping_cost = YayCurrencyHelper::calculate_price_by_currency( $cost, false, $apply_currency );
		}

		$rate['cost'] = apply_filters( 'yay_currency_get_shipping_cost', $shipping_cost, $method, $apply_currency );

		$method->set_cost( $rate['cost'] );
	}

	public function recalculate_tax_shipping_method_cost( $method, $data ) {
		if ( count( $method->get_taxes() ) > 0 ) {
			$is_original_default_currency = $data['is_original_default_currency'];
			$is_fallback_currency         = $data['is_fallback_currency'];
			$apply_currency               = YayCurrencyHelper::get_current_currency( $data['apply_currency'] );
			if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || $is_original_default_currency ) {
				if ( ! $is_fallback_currency ) {
					return;
				}
				$apply_currency = $data['fallback_currency'];
			}
			$tax_new = array();
			foreach ( $method->get_taxes() as $key => $tax ) {
				$tax_currency   = YayCurrencyHelper::calculate_price_by_currency( $tax, true, $apply_currency );
				$shipping_rates = \WC_Tax::get_shipping_tax_rates();
				if ( 'flat_rate' === $method->method_id && isset( $data['cost'] ) && ! is_numeric( $data['cost'] ) ) {
					$tax_calculate   = \WC_Tax::calc_shipping_tax( $data['rate_cost'], $shipping_rates );
					$tax_new[ $key ] = is_array( $tax_calculate ) ? array_sum( $tax_calculate ) : $tax_currency;
				} else {
					if ( $shipping_rates && isset( $shipping_rates[ $key ] ) && isset( $apply_currency['roundingType'] ) && 'disabled' !== $apply_currency['roundingType'] ) {
						$shipping_rate = isset( $shipping_rates[ $key ]['rate'] ) ? $shipping_rates[ $key ]['rate'] : 0;
						$rate_percent  = floatval( $shipping_rate / 100 );
						$tax_currency  = $method->cost * $rate_percent;
					}
					$tax_new[ $key ] = $tax_currency;
				}
			}
			$method->set_taxes( $tax_new );
		}
	}

	// Action Hook Manual Order

	public function handle_manual_order_line_items( $order, $apply_currency, $parent_rate_fee ) {
		$line_items = $order->get_items( 'line_item' );
		foreach ( $line_items as $item ) {
			$line_subtotal     = (float) ( $item['line_subtotal'] / $parent_rate_fee );
			$line_subtotal_tax = (float) ( $item['line_subtotal_tax'] / $parent_rate_fee );
			$line_total        = (float) ( $item['line_total'] / $parent_rate_fee );
			$line_tax          = (float) ( $item['line_tax'] / $parent_rate_fee );

			$item_subtotal     = YayCurrencyHelper::calculate_price_by_currency( $line_subtotal, false, $apply_currency );
			$item_subtotal_tax = YayCurrencyHelper::calculate_price_by_currency( $line_subtotal_tax, false, $apply_currency );
			$item_total        = YayCurrencyHelper::calculate_price_by_currency( $line_total, false, $apply_currency );
			$item_total_tax    = YayCurrencyHelper::calculate_price_by_currency( $line_tax, false, $apply_currency );

			$item->set_subtotal( $item_subtotal );
			$item->set_subtotal_tax( $item_subtotal_tax );
			$item->set_total( $item_total );
			$item->set_total_tax( $item_total_tax );

			$line_tax_data = $item['line_tax_data'];
			$has_tax       = false;

			if ( $line_tax_data ) {
				if ( isset( $line_tax_data['subtotal'] ) && $line_tax_data['subtotal'] ) {
					$rateId                               = key( $line_tax_data['subtotal'] );
					$line_tax_data['subtotal'][ $rateId ] = $item_subtotal_tax;
					$has_tax                              = true;
				}

				if ( isset( $line_tax_data['total'] ) && $line_tax_data['total'] ) {
					$rateId                            = key( $line_tax_data['total'] );
					$line_tax_data['total'][ $rateId ] = $item_total_tax;
					$has_tax                           = true;
				}

				if ( $has_tax ) {
					$item->set_taxes( $line_tax_data );
				}
			}

			$item->save();

		}

	}

	public function handle_manual_order_fee_lines( $order, $apply_currency, $parent_rate_fee ) {
		$fee_items = $order->get_items( 'fee' );
		foreach ( $fee_items as $fee ) {
			$fee_data = $fee->get_data();

			$fee_total     = (float) ( $fee_data['total'] / $parent_rate_fee );
			$fee_total_tax = (float) ( $fee_data['total_tax'] / $parent_rate_fee );

			$item_total     = YayCurrencyHelper::calculate_price_by_currency( $fee_total, true, $apply_currency );
			$item_total_tax = YayCurrencyHelper::calculate_price_by_currency( $fee_total_tax, true, $apply_currency );

			$taxes = $fee->get_taxes();
			$fee->set_total( $item_total );
			$fee->set_total_tax( $item_total_tax );

			if ( isset( $taxes['total'] ) ) {
				foreach ( $taxes['total'] as $rateId => $tax ) {
					$tax = ! empty( $tax ) ? floatval( $tax ) : false;
					if ( $tax ) {
						$tax_total                 = (float) ( $tax / $parent_rate_fee );
						$taxes['total'][ $rateId ] = YayCurrencyHelper::calculate_price_by_currency( $tax_total, true, $apply_currency );
					}
				}

				$fee->set_taxes( $taxes );
			}

			$fee->save();

		}

	}

	public function handle_manual_order_shipping_lines( $order, $apply_currency, $parent_rate_fee ) {
		$shipping_items = $order->get_items( 'shipping' );
		foreach ( $shipping_items as $shipping ) {
			// custom shipping total tax
			$shipping_taxes = $shipping->get_taxes();
			if ( isset( $shipping_taxes['total'] ) && $shipping_taxes['total'] ) {
				foreach ( $shipping_taxes['total'] as $rateId => $shipping_tax ) {

					$shipping_tax = ! empty( $shipping_tax ) ? floatval( $shipping_tax ) : false;
					if ( $shipping_tax ) {
						$shipping_tax_total                 = (float) ( $shipping_tax / $parent_rate_fee );
						$shipping_taxes['total'][ $rateId ] = YayCurrencyHelper::calculate_price_by_currency( $shipping_tax_total, true, $apply_currency );
					}
				}
				$shipping->set_taxes( $shipping_taxes );
			}
			// custom shipping total
			$shipping_data       = $shipping->get_data();
			$shipping_data_total = (float) ( $shipping_data['total'] / $parent_rate_fee );
			$shipping_total      = YayCurrencyHelper::calculate_price_by_currency( $shipping_data_total, true, $apply_currency );
			$shipping->set_total( $shipping_total );
			$shipping->save();
		}
	}

	public function handle_manual_order_tax_lines( $order, $apply_currency, $parent_rate_fee ) {
		$tax_items = $order->get_items( 'tax' );
		foreach ( $tax_items as $tax ) {
			$tax_data                = $tax->get_data();
			$tax_data_total          = (float) ( $tax_data['tax_total'] / $parent_rate_fee );
			$shipping_data_tax_total = (float) ( $tax_data['shipping_tax_total'] / $parent_rate_fee );
			$tax_total               = YayCurrencyHelper::calculate_price_by_currency( $tax_data_total, true, $apply_currency );
			$shipping_tax_total      = YayCurrencyHelper::calculate_price_by_currency( $shipping_data_tax_total, true, $apply_currency );
			$tax->set_tax_total( $tax_total );
			$tax->set_shipping_tax_total( $shipping_tax_total );
			$tax->save();
		}
	}

	public function handle_manual_order_coupon_lines( $order, $apply_currency, $parent_rate_fee ) {
		$coupon_items = $order->get_items( 'coupon' );
		foreach ( $coupon_items as $coupon ) {
			$coupon_total     = (float) ( $coupon->get_discount() / $parent_rate_fee );
			$coupon_tax_total = (float) ( $coupon->get_discount_tax() / $parent_rate_fee );

			$discount     = YayCurrencyHelper::calculate_price_by_currency( $coupon_total, true, $apply_currency );
			$discount_tax = YayCurrencyHelper::calculate_price_by_currency( $coupon_tax_total, true, $apply_currency );
			$coupon->set_discount( $discount );
			$coupon->set_discount_tax( $discount_tax );
			$coupon->save();
		}
	}

	public function handle_manual_order_totals( $order, $apply_currency, $parent_rate_fee ) {
		$order_shipping_total     = (float) ( $order->get_shipping_total() / $parent_rate_fee );
		$order_coupon_total       = (float) ( $order->get_discount_total() / $parent_rate_fee );
		$order_coupon_tax_total   = (float) ( $order->get_discount_tax() / $parent_rate_fee );
		$order_shipping_tax_total = (float) ( $order->get_shipping_tax() / $parent_rate_fee );

		$shipping_total = YayCurrencyHelper::calculate_price_by_currency( $order_shipping_total, true, $apply_currency );
		$discount_total = YayCurrencyHelper::calculate_price_by_currency( $order_coupon_total, true, $apply_currency );
		$discount_tax   = YayCurrencyHelper::calculate_price_by_currency( $order_coupon_tax_total, true, $apply_currency );
		$shipping_tax   = YayCurrencyHelper::calculate_price_by_currency( $order_shipping_tax_total, true, $apply_currency );

		$order_get_total = (float) ( $order->get_total() / $parent_rate_fee );
		$order_total     = YayCurrencyHelper::calculate_price_by_currency( $order_get_total, true, $apply_currency );

		$order->set_shipping_total( $shipping_total );
		$order->set_shipping_tax( $shipping_tax );
		$order->set_discount_total( $discount_total );
		$order->set_discount_tax( $discount_tax );
		$order->set_total( $order_total );
	}

	public function handle_manual_set_order_data( $order, $new_rate_fee, $order_currency_code ) {

		if ( $order_currency_code && ! empty( $order_currency_code ) ) {
			$order->set_currency( $order_currency_code );
		}

		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$order->update_meta_data( 'yay_currency_order_rate', $new_rate_fee );
		} else {
			$order_id = $order->get_id();
			update_post_meta( $order_id, 'yay_currency_order_rate', $new_rate_fee );
		}

		$order->save();

	}

	public function convert_price_callback( $price = 0, $apply_currency = array() ) {
		$apply_currency = $apply_currency ? $apply_currency : YayCurrencyHelper::detect_current_currency();
		$price          = YayCurrencyHelper::calculate_price_by_currency( $price, false, $apply_currency );
		return $price;
	}

	public function revert_price_callback( $price = 0, $apply_currency = array() ) {
		$price = YayCurrencyHelper::reverse_calculate_price_by_currency( $price, $apply_currency );
		return $price;
	}

	public function revert_product_price_callback( $original_price, $product, $apply_currency, $converted_currency = array() ) {

		$fixed_product_price = FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $apply_currency );

		if ( ! $fixed_product_price ) {
			return $original_price;
		}

		$original_price = apply_filters( 'yay_currency_revert_price', $fixed_product_price, $apply_currency );
		$original_price = self::formatted_amount_callback( $original_price, $converted_currency );

		return $original_price;

	}

	public function get_current_currency_by_country_code( $country_code = '' ) {

		$apply_currency = array();

		if ( ! empty( $country_code ) ) {
			$currency_code  = CountryHelper::get_currency_code_by_country_code( $country_code );
			$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );
		}

		return $apply_currency;

	}

	public function get_current_currency_by_billing_country_code( $country_code = '' ) {
		if ( is_user_logged_in() ) {
			// Get the current user
			$current_user = wp_get_current_user();
			// Get the currency based on the user's billing country
			if ( $current_user && isset( $current_user->ID ) ) {
				$country_code = get_user_meta( $current_user->ID, 'billing_country', true );
			}
		}

		$apply_currency = self::get_current_currency_by_country_code( $country_code );

		return $apply_currency;

	}

	public function formatted_amount_callback( $amount = 0, $converted_currency = array() ) {
		$default_currency_apply_currency = YayCurrencyHelper::get_currency_by_currency_code( Helper::default_currency_code(), $converted_currency );
		if ( ! $default_currency_apply_currency ) {
			$number_decimals = get_option( 'woocommerce_price_num_decimals' );
			$decimal_sep     = get_option( 'woocommerce_price_decimal_sep' );
			$thousand_sep    = get_option( 'woocommerce_price_thousand_sep' );
		} else {
			$number_decimals = $default_currency_apply_currency['numberDecimal'];
			$decimal_sep     = $default_currency_apply_currency['decimalSeparator'];
			$thousand_sep    = $default_currency_apply_currency['thousandSeparator'];
		}

		$formatted_value = number_format( $amount, (int) $number_decimals, $decimal_sep, $thousand_sep );

		// Step 1: Remove thousand separator
		$value_without_thousand_sep = str_replace( $thousand_sep, '', $formatted_value );
		// Step 2: Replace the decimal separator with a dot
		$value_with_dot_decimal = str_replace( $decimal_sep, '.', $value_without_thousand_sep );
		// Step 3: Convert to a float
		if ( is_numeric( $value_with_dot_decimal ) ) {
			return (float) $value_with_dot_decimal;
		}
		return $amount;
	}

	public function get_cart_subtotal_default_conditions( $cart_subtotal = 0, $apply_currency = array(), $fallback_currency = array(), $converted_currency = array() ) {
		if ( ! FixedPriceHelper::is_set_fixed_price() || ! FixedPriceHelper::has_fixed_price_product_in_cart( $apply_currency ) ) {
			return $cart_subtotal;
		}
		if ( ! apply_filters( 'yay_currency_allow_convert_product_fixed_price', false ) ) {
			return $cart_subtotal;
		}
		$cart_subtotal = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
		$cart_subtotal = apply_filters( 'yay_currency_revert_price', $cart_subtotal, $apply_currency );

		$cart_subtotal = self::formatted_amount_callback( $cart_subtotal, $converted_currency );

		return $cart_subtotal;
	}

	public function get_product_price_default_conditions( $price, $product, $apply_currency, $fallback_currency = array(), $converted_currency = array() ) {

		if ( ! FixedPriceHelper::is_set_fixed_price() || ! $apply_currency || ! apply_filters( 'yay_currency_allow_convert_product_fixed_price', false ) ) {
			return $price;
		}

		$price = self::revert_product_price_callback( $price, $product, $apply_currency, $converted_currency );

		return $price;
	}

	// Email

	public function set_email_order_id( $order, $sent_to_admin, $plain_text, $email ) {

		$_REQUEST['yay_currency_email_order_id'] = $order->get_id();

		add_filter( 'woocommerce_currency', array( $this, 'filter_email_currency_code' ), PHP_INT_MAX, 2 ); // only run in email
		add_filter( 'woocommerce_currency_symbol', array( $this, 'filter_email_currency_symbol' ), PHP_INT_MAX, 2 ); // only run in email

	}

	protected function get_email_order_currency_code() {
		// only run in Email
		if ( doing_action( 'woocommerce_email' ) || doing_action( 'woocommerce_email_order_details' ) ) {
			if ( isset( $_REQUEST['yay_currency_email_order_id'] ) ) {
				$order_id = intval( sanitize_text_field( $_REQUEST['yay_currency_email_order_id'] ) );
				if ( ! empty( $order_id ) && $order_id ) {
					$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id );
					return $order_currency;
				}
			}
		}
		return false;
	}

	public function filter_email_currency_code( $currency_code ) {
		$order_currency = self::get_email_order_currency_code();
		$currency_code  = isset( $order_currency['currency'] ) ? wp_kses_post( html_entity_decode( $order_currency['currency'] ) ) : $currency_code;
		return $currency_code;
	}

	public function filter_email_currency_symbol( $currency_symbol, $currency ) {
		$order_currency  = self::get_email_order_currency_code();
		$currency_symbol = isset( $order_currency['symbol'] ) ? wp_kses_post( html_entity_decode( $order_currency['symbol'] ) ) : $currency_symbol;
		return $currency_symbol;
	}
}
