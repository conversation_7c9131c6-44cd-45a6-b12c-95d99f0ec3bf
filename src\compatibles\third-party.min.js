"use strict"; !function (e) { let r = { opacityClass: "yay-currency-cache-opacity", productAddonPricesWrapper: "form.cart .wc-pao-addons-container" }; yayCurrencyHooks.addAction("yayCurrencyBeforeSendCaching", function (c) { c.response, yay_currency_caching_data.wc_product_addons_active && e(r.productAddonPricesWrapper).addClass(r.opacityClass) }), yayCurrencyHooks.addAction("yayCurrencyResponseCaching", function (c) { let a = c.response, t = a.data.current_currency.currency, o = !!a.data.price_details && a.data.price_details; if ("fluxwp" === yayCurrency.current_theme && e("#currencyButtonContainer").length) { document.getElementById("selectedCurrency").innerText = t; var i = document.getElementById("currencyFlag"), n = getFlagUrlByCurrency(t); i.style.backgroundImage = "url(" + n + ")" } if (yay_currency_caching_data.wcs_att && yay_currency_caching_data.product_id && o && e(".wcsatt-options-wrapper").length && e(".product form.cart").length) { for (let p in o) p === yay_currency_caching_data.product_id && e(".wcsatt-options-wrapper").html(o[p]); e(".product form.cart").each(function () { e(this).data("satt_script") && e(this).removeData("satt_script") }), e(document.body).trigger("wcsatt-initialize") } yay_currency_caching_data.wc_product_addons_active && e(r.productAddonPricesWrapper).length && a.data.yay_currency_addon_display_html && (e(r.productAddonPricesWrapper).replaceWith(a.data.yay_currency_addon_display_html), a.data.current_symbol && (woocommerce_addons_params.currency_format_symbol = a.data.current_symbol), e("body").find(".cart:not(.cart_group)").each(function () { new WC_PAO.Form(e(this)) })) }), yayCurrencyHooks.addAction("yayCurrencyResponseCaching", function (c) { yay_currency_caching_data.wc_product_addons_active && e(r.productAddonPricesWrapper).removeClass(r.opacityClass) }), yayCurrencyHooks.addAction("yayCurrencyCompatibleThirdParty", function (r) { let c = r.currencyID; if (window.wc_price_calculator_params) { let a = YayCurrency_Callback.Helper.getCurrentCurrency(c), t = YayCurrency_Callback.Helper.getRateFeeByCurrency(a); window.wc_price_calculator_params.woocommerce_currency_pos = a.currencyPosition, window.wc_price_calculator_params.woocommerce_price_decimal_sep = a.decimalSeparator, window.wc_price_calculator_params.woocommerce_price_num_decimals = a.numberDecimal, window.wc_price_calculator_params.woocommerce_price_thousand_sep = a.thousandSeparator, window.wc_price_calculator_params.pricing_rules && window.wc_price_calculator_params.pricing_rules.forEach(e => { e.price = (parseFloat(e.price) * t).toString(), e.regular_price = (parseFloat(e.regular_price) * t).toString(), e.sale_price = (parseFloat(e.sale_price) * t).toString() }) } if (window.yayCurrency.ppc_paypal) { yayCurrency.checkout_diff_currency && "1" === yayCurrency.checkout_diff_currency && jQuery(document).ready(function (e) { yayCurrency.checkout_page && "1" === yayCurrency.checkout_page || e(document.body).trigger("wc_fragment_refresh") }); let o = (e, r) => { r ? YayCurrency_Callback.Helper.setCookie(e, "yes", +yayCurrency.cookie_lifetime_days) : YayCurrency_Callback.Helper.getCookie(e) && YayCurrency_Callback.Helper.deleteCookie(e) }, i = () => { o("ppc_paypal_cart_or_product_page", "1" === yayCurrency.cart_page || "1" === yayCurrency.product_page), o("ppc_paypal_checkout_page", yayCurrency.checkout_page && "1" === yayCurrency.checkout_page) }; i(), e(document).on("visibilitychange", function () { "visible" === document.visibilityState && i() }) } }), yayCurrencyHooks.addFilter("yayCurrencyGetDataCaching", function (c) { return yay_currency_caching_data.wc_product_addons_active && e(r.productAddonPricesWrapper).length && yay_currency_caching_data.product_id && (c.product_id = yay_currency_caching_data.product_id), c }), yayCurrencyHooks.addFilter("yayCurrencyDetectAllowCaching", function (e) { return ("undefined" == typeof yay_dokan_data || "yes" !== yay_dokan_data.dashboard_page) && e }) }(jQuery);