<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;


class WooCommerceGutenbergBlocks {
	use SingletonTrait;

	private $enable_force_payment = false;
	private $is_checkout_different_currency;
	public function __construct() {

		$this->enable_force_payment           = CountryHelper::force_payment_country();
		$this->is_checkout_different_currency = (int) get_option( 'yay_currency_checkout_different_currency', 0 );

		if ( $this->enable_force_payment ) {
			add_action( 'woocommerce_init', array( $this, 'woocommerce_detect_call_rest_api_init' ) );
			add_filter( 'yay_currency_localize_args', array( $this, 'yay_currency_add_localize_args' ), 20, 1 );

			add_filter( 'yay_currency_get_id_selected_currency', array( $this, 'yay_currency_change_currency_id_force_country_code' ), 20, 1 );
			add_filter( 'yay_currency_detect_with_blocks_pages', array( $this, 'get_apply_currency_detect_blocks_pages' ), 20, 1 );
			add_filter( 'yay_currency_force_payment_country_with_blocks', array( $this, 'get_apply_currency_force_payment_country_with_blocks' ), 20, 2 );
		}

		add_action( 'wp_ajax_yayCurrency_get_cart_subtotal_blocks', array( $this, 'ajax_handle_get_cart_subtotal_blocks' ) );
		add_action( 'wp_ajax_nopriv_yayCurrency_get_cart_subtotal_blocks', array( $this, 'ajax_handle_get_cart_subtotal_blocks' ) );

		add_action( 'wp_ajax_yayCurrency_recalculate_apply_currency_from_blocks', array( $this, 'ajax_handle_recalculate_apply_currency_from_blocks' ) );
		add_action( 'wp_ajax_nopriv_yayCurrency_recalculate_apply_currency_from_blocks', array( $this, 'ajax_handle_recalculate_apply_currency_from_blocks' ) );
	}

	public function yay_currency_add_localize_args( $localize_args ) {
		if ( isset( $localize_args['country_code'] ) && ! empty( $localize_args['country_code'] ) ) {
			$currency_code = CountryHelper::get_currency_code_by_country_code( $localize_args['country_code'] );
			if ( ! empty( $currency_code ) ) {
				$localize_args['currency_code'] = $currency_code;
			}
		}
		return $localize_args;
	}

	protected function detect_rest_api_doing() {

		if ( ! WC()->is_rest_api_request() ) {
			return false;
		}

		$rest_route = CountryHelper::get_rest_route_via_rest_api();

		$endpoints = SupportHelper::rest_api_endpoints();

		if ( $rest_route && in_array( $rest_route, $endpoints, true ) && isset( $_REQUEST['_locale'] ) ) {
			return true;
		}

		return false;
	}

	public function woocommerce_detect_call_rest_api_init() {

		if ( ! self::detect_rest_api_doing() || ! $this->is_checkout_different_currency ) {
			return;
		}

		WC()->frontend_includes();

		if ( null === WC()->cart && function_exists( 'wc_load_cart' ) ) {
			wc_load_cart();
		}
	}

	public function yay_currency_change_currency_id_force_country_code( $select_id ) {
		if ( wp_doing_ajax() && isset( $_REQUEST['action'] ) && 'yayCurrency_handle_force_payment_response' === $_REQUEST['action'] ) {
			if ( isset( $_REQUEST['woocommerce_blocks'] ) && 'yes' === $_REQUEST['woocommerce_blocks'] ) {
				$currency_id_blocks = YayCurrencyHelper::get_currency_id_blocks_name();
				$currency_id_force  = get_option( $currency_id_blocks, false );
				$select_id          = $currency_id_force ? $currency_id_force : $select_id;
			}
		}
		return $select_id;
	}

	public function get_apply_currency_detect_blocks_pages( $apply_currency ) {
		// Force via Checkout page ( Blocks )
		if ( CountryHelper::detect_force_country_by_checkout_blocks_page() ) {
			$currency_force_country_code = CountryHelper::get_apply_currency_by_force_payment_country();
			if ( $currency_force_country_code ) {
				return $currency_force_country_code;
			}
		}
		// Force via Cart page ( Blocks )
		if ( CountryHelper::detect_force_country_by_cart_blocks_page() ) {
			$currency_force_country_code = CountryHelper::get_apply_currency_by_force_shipping_country_cart_page();
			if ( $currency_force_country_code ) {
				return $currency_force_country_code;
			}
		}
		return $apply_currency;
	}

	public function get_apply_currency_force_payment_country_with_blocks( $apply_currency, $is_checkout ) {
		$country_code = '';
		if ( $is_checkout ) {
			if ( CountryHelper::detect_force_country_by_checkout_blocks_page() ) {
				$country_code = CountryHelper::detect_country_code_with_rest_api();
			}
		} elseif ( CountryHelper::detect_force_country_by_cart_blocks_page() ) {
			$country_code = CountryHelper::detect_country_code_with_rest_api( true );
		}

		if ( ! empty( $country_code ) ) {
			$currency_code = CountryHelper::get_currency_code_by_country_code( $country_code );
			$currency_code = apply_filters( 'yay_currency_custom_currency_code_by_force_country', $currency_code );
			if ( ! empty( $currency_code ) ) {
				$new_apply_currency = CountryHelper::get_current_currency_by_force_specific_country( $country_code, $currency_code );
				if ( $new_apply_currency ) {
					$flag = $is_checkout ? CountryHelper::detect_force_country_by_checkout_blocks_page() : true;
					if ( $flag ) {
						$currency_id_blocks = YayCurrencyHelper::get_currency_id_blocks_name();
						update_option( $currency_id_blocks, $new_apply_currency['ID'] );
						return $new_apply_currency;
					}
				}
			}
		}

		return $apply_currency;
	}

	// GET FORMAT PRICE WITH DEFAULT CURRENCY  APPLY FOR FRONTEND
	protected function convert_price_to_default_currency( $price, $apply_default_currency ) {
		$price           = YayCurrencyHelper::format_price_currency( $price, $apply_default_currency );
		$currency_symbol = YayCurrencyHelper::get_symbol_by_currency_code( Helper::default_currency_code() );
		$format          = YayCurrencyHelper::format_currency_symbol( $apply_default_currency );
		$formatted_price = sprintf( $format, '<span class="woocommerce-Price-currencySymbol">' . $currency_symbol . '</span>', $price );
		$return          = '<bdi>' . $formatted_price . '</bdi></span>';
		return $return;
	}

	public function ajax_handle_get_cart_subtotal_blocks() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		$results        = array();
		$apply_currency = YayCurrencyHelper::detect_current_currency();
		if ( $apply_currency ) {
			$is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $apply_currency );
			if ( (int) $is_dis_checkout_diff_currency ) {
				$fallback_currency_code   = isset( $_POST['fallback_currency_code'] ) ? sanitize_text_field( $_POST['fallback_currency_code'] ) : Helper::default_currency_code();
				$fallback_currency        = YayCurrencyHelper::get_currency_by_currency_code( $fallback_currency_code );
				$cart_subtotal            = apply_filters( 'yay_currency_get_cart_subtotal', 0, $fallback_currency );
				$results['cart_subtotal'] = wc_price(
					$cart_subtotal,
					YayCurrencyHelper::get_apply_currency_format_info( $fallback_currency )
				);
				wp_send_json_success( $results );
			}
		}
		wp_send_json_error();
	}

	public function ajax_handle_recalculate_apply_currency_from_blocks() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		$results            = array();
		$currencyID         = isset( $_POST['currencyID'] ) ? sanitize_key( $_POST['currencyID'] ) : false;
		$new_apply_currency = array();
		if ( $currencyID ) {
			$old_apply_currency = YayCurrencyHelper::get_currency_by_ID( $currencyID );
			if ( isset( $_POST['currentCurrency'] ) ) {

				$new_apply_currency = array_map( 'sanitize_key', $_POST['currentCurrency'] );

				$is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $new_apply_currency );

				$currency_ID = $new_apply_currency['ID'];

				YayCurrencyHelper::set_cookie( YayCurrencyHelper::get_cookie_name(), $currency_ID );
				YayCurrencyHelper::set_cookie( YayCurrencyHelper::get_cookie_name( 'switcher' ), $currency_ID );

				$force_payment_specific_currency = CountryHelper::force_payment_specific_currency();
				if ( ! $is_dis_checkout_diff_currency && ( isset( $force_payment_specific_currency['force_notice'] ) && 1 === intval( $force_payment_specific_currency['force_notice'] ) ) ) {
					$current_theme                   = Helper::get_current_theme();
					$html                            = CountryHelper::get_force_payment_notice_html();
					$hide_class                      = empty( $html ) ? 'yay-currency-force-payment-hide' : '';
					$force_payment_class             = 'yay-currency-checkout-force-payment-notice yay-currency-checkout-notice user yay-currency-with-' . $current_theme . ' ' . $hide_class;
					$results['force_payment_notice'] = "<div class='" . esc_attr( $force_payment_class ) . "'>" . wp_kses_post( $html ) . '</div>';
				}

				$results = apply_filters( 'yay_currency_ajax_handle_response_force_payment_results', $results, $_POST, $old_apply_currency, $new_apply_currency );
				wp_send_json_success( $results );
			}
		}

		wp_send_json_error();
	}
}
