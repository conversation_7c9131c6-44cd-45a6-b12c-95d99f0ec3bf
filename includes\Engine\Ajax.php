<?php
namespace Yay_Currency\Engine;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\RateHelper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;

use Automattic\WooCommerce\Admin\API\Reports\Orders\Stats\DataStore as OrdersStatsDataStore;

defined( 'ABSPATH' ) || exit;

class Ajax {
	use SingletonTrait;

	protected function __construct() {

		add_action( 'rest_api_init', array( $this, 'yay_currency_endpoints' ) );

		add_action( 'wp_ajax_yayCurrency_get_all_data', array( $this, 'get_all_data' ) );
		add_action( 'wp_ajax_yayCurrency_update_exchange_rate', array( $this, 'update_exchange_rate' ) );
		add_action( 'wp_ajax_yayCurrency_delete_currency', array( $this, 'delete_currency' ) );

		// Force Payment by Country
		add_action( 'wp_ajax_yayCurrency_handle_force_payment_response', array( $this, 'ajax_handle_force_payment_response' ) );
		add_action( 'wp_ajax_nopriv_yayCurrency_handle_force_payment_response', array( $this, 'ajax_handle_force_payment_response' ) );

		// Force Currency by Payment Method Selected
		add_action( 'wp_ajax_yayCurrency_handle_force_currency_by_payment_selected_response', array( $this, 'ajax_handle_force_currency_by_payment_selected_response' ) );
		add_action( 'wp_ajax_nopriv_yayCurrency_handle_force_currency_by_payment_selected_response', array( $this, 'ajax_handle_force_currency_by_payment_selected_response' ) );
		// Fetch Analytics
		add_action( 'wp_ajax_yayCurrency_sync_orders_revert_to_base', array( $this, 'ajax_handle_sync_orders_revert_to_base' ) );

	}

	/**
	 * Add YayCurrency Endpoints
	 */
	public function yay_currency_endpoints() {
		register_rest_route(
			'yaycurrency/v1',
			'/settings',
			array(
				array(
					'methods'             => 'POST',
					'callback'            => array( $this, 'exec_patch_settings' ),
					'permission_callback' => '__return_true',
				),
			)
		);
	}

	public function get_all_data() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		self::send_data_settings();
	}

	private function send_data_settings() {
		$currency_manage_tab_data                       = self::get_currency_manage_tab_data();
		$data_settings_args                             = apply_filters(
			'yay_currency_send_data_settings_args',
			array(
				'list_countries'           => WC()->countries->countries,
				'woo_current_settings'     => Helper::get_woo_current_settings(),
				'currency_manage_tab_data' => $currency_manage_tab_data,
			)
		);
		$data_settings_args['currency_manage_tab_data'] = wp_json_encode( $data_settings_args['currency_manage_tab_data'] );
		wp_send_json( $data_settings_args );
	}

	private function set_currency_manage_settings( $list_currencies ) {
		foreach ( $list_currencies as $key => $currency ) {
			if ( isset( $currency['ID'] ) ) {
				$update_currency = array(
					'ID'         => $currency['ID'],
					'post_title' => $currency['currency'],
					'menu_order' => $key,
				);
				wp_update_post( $update_currency );
				Helper::update_post_meta_currency( $currency['ID'], $currency );
			} else {
				$new_currency    = array(
					'post_title'  => $currency['currency'],
					'post_type'   => Helper::get_post_type(),
					'post_status' => 'publish',
					'menu_order'  => $key,
				);
				$new_currency_ID = wp_insert_post( $new_currency );
				if ( ! is_wp_error( $new_currency_ID ) ) {
					Helper::update_post_meta_currency( $new_currency_ID, $currency );
				}
			}
		}
	}

	private function set_checkout_options_settings( $params ) {

		$is_checkout_different_currency = sanitize_text_field( $params['isCheckoutDifferentCurrency'] ) === '1' ? 1 : 0;
		$checkout_fallback_currency     = sanitize_text_field( $params['checkoutFallbackCurrency'] );
		update_option( 'yay_currency_checkout_different_currency', $is_checkout_different_currency );
		update_option( 'yay_currency_checkout_fallback_currency', $checkout_fallback_currency );
		if ( isset( $params['forcePaymentCurrency'] ) ) {
			update_option( 'yay_currency_force_payment_specific_currency', $params['forcePaymentCurrency'] );
		}

		if ( isset( $params['forceCurrencyPaymentMethod'] ) ) {
			if ( isset( $params['forceCurrencyByPaymentMethodsOptions'] ) ) {
				$params['forceCurrencyPaymentMethod']['force_currency_payment_options'] = $params['forceCurrencyByPaymentMethodsOptions'];
				update_option( 'yay_currency_force_currency_specific_payment_option', $params['forceCurrencyPaymentMethod'] );
			} else {
				delete_option( 'yay_currency_force_currency_specific_payment_option' );
			}
		}

		foreach ( $params['currencies'] as $currency ) {
			if ( isset( $currency['ID'] ) ) {
				update_post_meta( $currency['ID'], 'status', '1' === $currency['status'] ? 1 : 0 );
				update_post_meta( $currency['ID'], 'payment_methods', $currency['paymentMethods'] );
			}
		}
	}

	private function set_display_options_settings( $params ) {
		$is_show_on_single_product_page           = sanitize_text_field( $params['isShowOnSingleProductPage'] ) === '1' ? 1 : 0;
		$switcher_position_on_single_product_page = sanitize_text_field( $params['switcherPositionOnSingleProductPage'] );
		$is_show_flag_in_switcher                 = sanitize_text_field( $params['isShowFlagInSwitcher'] ) === '1' ? 1 : 0;
		$is_show_currency_name_in_switcher        = sanitize_text_field( $params['isShowCurrencyNameInSwitcher'] ) === '1' ? 1 : 0;
		$is_show_currency_symbol_in_switcher      = sanitize_text_field( $params['isShowCurrencySymbolInSwitcher'] ) === '1' ? 1 : 0;
		$is_show_currency_code_in_switcher        = sanitize_text_field( $params['isShowCurrencyCodeInSwitcher'] ) === '1' ? 1 : 0;
		$switcher_size                            = sanitize_text_field( $params['switcherSize'] );
		$currency_unit_type                       = sanitize_text_field( $params['currencyUnitType'] );

		update_option( 'yay_currency_show_single_product_page', $is_show_on_single_product_page );
		update_option( 'yay_currency_switcher_position_on_single_product_page', $switcher_position_on_single_product_page );
		update_option( 'yay_currency_show_flag_in_switcher', $is_show_flag_in_switcher );
		update_option( 'yay_currency_show_currency_name_in_switcher', $is_show_currency_name_in_switcher );
		update_option( 'yay_currency_show_currency_symbol_in_switcher', $is_show_currency_symbol_in_switcher );
		update_option( 'yay_currency_show_currency_code_in_switcher', $is_show_currency_code_in_switcher );
		update_option( 'yay_currency_switcher_size', $switcher_size );
		update_option( 'yay_currency_currency_unit_type', $currency_unit_type );
	}

	private function set_settings( $params ) {

		$is_auto_select_currency_by_countries = sanitize_text_field( $params['isAutoSelectCurrencyByCountries'] );

		$is_update_exchange_rate_auto   = sanitize_text_field( $params['isUpdateExchangeRateAuto'] );
		$finance_api                    = sanitize_text_field( $params['financeApi'] );
		$is_set_fixed_price             = sanitize_text_field( $params['isSetFixedPrice'] );
		$is_wpml_compatible             = sanitize_text_field( $params['isWPMLCompatible'] );
		$is_polylang_compatible         = sanitize_text_field( $params['isPolylangCompatible'] );
		$time_update_exchange_rate_auto = maybe_serialize( $params['timeUpdateExchangeRateAuto'] );
		$is_show_notice                 = sanitize_text_field( $params['showNotice'] );
		$notice_text                    = wp_kses_post( $params['noticeText'] );

		update_option( 'yay_currency_auto_select_currency_by_countries', $is_auto_select_currency_by_countries );

		update_option( 'yay_currency_set_fixed_price', $is_set_fixed_price );
		update_option( 'yay_currency_update_exchange_rate_auto', $is_update_exchange_rate_auto );
		update_option( 'yay_currency_finance_api', $finance_api );
		update_option( 'yay_currency_time_update_exchange_rate_auto', $time_update_exchange_rate_auto );
		update_option( 'yay_currency_setting_show_notice', $is_show_notice );
		update_option( 'yay_currency_setting_notice_text', $notice_text );
		update_option( 'yay_currency_wpml_compatible', $is_wpml_compatible );
		update_option( 'yay_currency_polylang_compatible', $is_polylang_compatible );

		if ( isset( $params['isGoogleCrawlersOrBotsEnable'] ) ) {
			$is_google_crawler = sanitize_text_field( $params['isGoogleCrawlersOrBotsEnable'] );
			update_option( 'yay_currency_google_crawlers_or_bots_enable', $is_google_crawler );
		}

		if ( isset( $params['approximatePrice'] ) ) {
			update_option( 'yay_currency_approximate_price_info', $params['approximatePrice'] );
		}

		if ( isset( $params['forceCacheCompatible'] ) ) {
			update_option( 'yay_currency_cache_compatible_args', $params['forceCacheCompatible'] );
		}

		if ( isset( $params['listCurrentPolylangLanguages'] ) ) {
			update_option( 'yay_currency_currencies_by_languages_polylang', $params['listCurrentPolylangLanguages'] );
		}

		if ( isset( $params['listCurrentWpmlLanguages'] ) ) {
			update_option( 'yay_currency_currencies_by_languages_wpml', $params['listCurrentWpmlLanguages'] );
		}

		foreach ( $params['currencies'] as $currency ) {
			if ( isset( $currency['ID'] ) ) {
				update_post_meta( $currency['ID'], 'countries', $currency['countries'] );
			}
		}

	}

	private function converted_currencies( $currencies = array() ) {
		$converted_currencies = array();
		foreach ( $currencies as $currency ) {
			$currency_meta = get_post_meta( $currency->ID, '', false );
			if ( ! $currency_meta ) {
				continue;
			}
			$currency_code      = $currency->post_title;
			$symbol_currency    = get_woocommerce_currency_symbol( $currency_code );
			$converted_currency = array(
				'ID'                   => $currency->ID,
				'currency'             => $currency_code,
				'currencySymbol'       => html_entity_decode( $symbol_currency ),
				'currencyPosition'     => $currency_meta['currency_position'][0],
				'currencyCodePosition' => isset( $currency_meta['currency_code_position'] ) ? $currency_meta['currency_code_position'][0] : 'not_display',
				'thousandSeparator'    => $currency_meta['thousand_separator'][0],
				'decimalSeparator'     => isset( $currency_meta['decimal_separator'] ) ? $currency_meta['decimal_separator'][0] : '.',
				'numberDecimal'        => isset( $currency_meta['number_decimal'] ) ? $currency_meta['number_decimal'][0] : '0',
				'rate'                 =>
					array(
						'type'  => isset( $currency_meta['rate_type'] ) ? $currency_meta['rate_type'][0] : 'auto',
						'value' => isset( $currency_meta['rate'] ) ? $currency_meta['rate'][0] : '1',
					),
				'fee'                  => isset( $currency_meta['fee'] ) ? maybe_unserialize( $currency_meta['fee'][0] ) : array(
					'value' => '0',
					'type'  => 'fixed',
				),
				'status'               => isset( $currency_meta['status'] ) ? $currency_meta['status'][0] : '1',
				'paymentMethods'       => isset( $currency_meta['payment_methods'] ) ? maybe_unserialize( $currency_meta['payment_methods'][0] ) : array( 'all' ),
				'countries'            => isset( $currency_meta['countries'] ) ? maybe_unserialize( $currency_meta['countries'][0] ) : array( 'default' ),
				'default'              => Helper::default_currency_code() === $currency_code ? true : false,
				'isLoading'            => false,
				'roundingType'         => isset( $currency_meta['rounding_type'] ) ? $currency_meta['rounding_type'][0] : 'disabled',
				'roundingValue'        => isset( $currency_meta['rounding_value'] ) ? $currency_meta['rounding_value'][0] : 1,
				'subtractAmount'       => isset( $currency_meta['subtract_amount'] ) ? $currency_meta['subtract_amount'][0] : 0,
			);
			array_push( $converted_currencies, $converted_currency );
		}
		return $converted_currencies;
	}

	private function get_data_payment_methods() {
		$paymentMethodsOptions                = array();
		$forceCurrencyByPaymentMethodsOptions = array();
		$available_payment_methods            = WC()->payment_gateways->get_available_payment_gateways();
		$index_key                            = 0;
		$force_currency_by_payment_method     = get_option( 'yay_currency_force_currency_specific_payment_option' );
		foreach ( $available_payment_methods as $key => $value ) {
			$paymentMethodsOptions[ $key ] = $value->title;

			$currency_by_method_id = isset( $force_currency_by_payment_method['force_currency_payment_options'] ) ? Helper::get_currency_payment_method_id( $force_currency_by_payment_method['force_currency_payment_options'], $key ) : false;

			$forceCurrencyByPaymentMethodsOptions[ $index_key ] = array(
				'id'       => $key,
				'title'    => $value->title,
				'currency' => [ 'default' ],
			);
			if ( $force_currency_by_payment_method && $currency_by_method_id ) {
				$forceCurrencyByPaymentMethodsOptions[ $index_key ]['currency'] = $currency_by_method_id;
			}

			++$index_key;
		}

		$data_payment_methods = array(
			'paymentMethodsOptions'                => $paymentMethodsOptions,
			'forceCurrencyByPaymentMethodsOptions' => $forceCurrencyByPaymentMethodsOptions,
		);

		return apply_filters( 'yay_currency_get_data_payment_methods', $data_payment_methods, $available_payment_methods );
	}

	private function check_currency_code_exists( $currencies, $currency_code ) {
		$currency_codes = array_column( $currencies, 'post_title' );
		return in_array( $currency_code, $currency_codes, true );
	}

	private function get_currency_manage_tab_data() {
		$converted_currencies = array();
		$currencies           = Helper::get_currencies_post_type();
		$default_currency     = Helper::get_default_currency();

		if ( $currencies ) {
			$converted_currencies = self::converted_currencies( $currencies );
			// Add default currency if it doesn't exist in the list
			if ( ! self::check_currency_code_exists( $currencies, Helper::default_currency_code() ) ) {
				array_push( $converted_currencies, $default_currency );
			}
		} else {
			array_push( $converted_currencies, $default_currency );
		}

		$default_fallback_currency      = isset( $converted_currencies[0]['default'] ) ? ( isset( $converted_currencies[1]['currency'] ) ? $converted_currencies[1]['currency'] : $converted_currencies[0]['currency'] ) : $converted_currencies[0]['currency'];
		$time_update_exchange_rate_auto = get_option( 'yay_currency_time_update_exchange_rate_auto', 0 );

		$available_payments = self::get_data_payment_methods();

		$show_notice = get_option( 'yay_currency_setting_show_notice', 0 );

		$data = array(
			'isCheckoutDifferentCurrency'         => get_option( 'yay_currency_checkout_different_currency', 0 ),
			'forcePaymentCurrency'                => CountryHelper::force_payment_specific_currency(),
			'forceCurrencyPaymentMethod'          => Helper::force_currency_specific_payment_option(),
			'checkoutFallbackCurrency'            => get_option( 'yay_currency_checkout_fallback_currency', $default_fallback_currency ),
			'isSetFixedPrice'                     => get_option( 'yay_currency_set_fixed_price', 0 ),
			'isUpdateExchangeRateAuto'            => get_option( 'yay_currency_update_exchange_rate_auto', 0 ),
			'financeApi'                          => get_option( 'yay_currency_finance_api', 'default' ),
			'isShowOnSingleProductPage'           => get_option( 'yay_currency_show_single_product_page', 1 ),
			'switcherPositionOnSingleProductPage' => get_option( 'yay_currency_switcher_position_on_single_product_page', 'before_description' ),
			'isShowFlagInSwitcher'                => get_option( 'yay_currency_show_flag_in_switcher', 1 ),
			'isShowCurrencyNameInSwitcher'        => get_option( 'yay_currency_show_currency_name_in_switcher', 1 ),
			'isShowCurrencySymbolInSwitcher'      => get_option( 'yay_currency_show_currency_symbol_in_switcher', 1 ),
			'isShowCurrencyCodeInSwitcher'        => get_option( 'yay_currency_show_currency_code_in_switcher', 1 ),
			'switcherSize'                        => get_option( 'yay_currency_switcher_size', 'medium' ),
			'currencyUnitType'                    => get_option( 'yay_currency_currency_unit_type', 'symbol' ),
			'isAutoSelectCurrencyByCountries'     => get_option( 'yay_currency_auto_select_currency_by_countries', 0 ),
			'isGoogleCrawlersOrBotsEnable'        => get_option( 'yay_currency_google_crawlers_or_bots_enable', 1 ),
			'approximatePrice'                    => Helper::approximate_price_info(),
			'isWPMLCompatible'                    => get_option( 'yay_currency_wpml_compatible', 0 ),
			'isPolylangCompatible'                => get_option( 'yay_currency_polylang_compatible', 0 ),
			'timeUpdateExchangeRateAuto'          => $time_update_exchange_rate_auto ? maybe_unserialize( $time_update_exchange_rate_auto ) : array(
				'value' => '30',
				'type'  => 'mins',
			),
			'showNotice'                          => 'true' === $show_notice || 0 !== intval( $show_notice ) ? 1 : 0,
			'noticeText'                          => get_option(
				'yay_currency_setting_notice_text',
				'You are from %current-country%, price will be in %current-currency% (%current-currency-symbol%).'
			),
			'forceCacheCompatible'                => Helper::force_cache_compatible(),
			'currencies'                          => $converted_currencies,
			'paymentMethods'                      => $available_payments['paymentMethodsOptions'],
		);

		if ( $available_payments['forceCurrencyByPaymentMethodsOptions'] ) {
			$data['forceCurrencyByPaymentMethodsOptions'] = $available_payments['forceCurrencyByPaymentMethodsOptions'];
		}

		return $data;
	}

	public function exec_patch_settings( $request ) {
		$params = $request->get_params();
		if ( $params && isset( $params['currencies'] ) ) {
			// delete cache yay currencies list
			Helper::delete_yay_currencies_transient();

			$list_currencies = isset( $params['currencies'] ) ? $params['currencies'] : array();
			self::set_currency_manage_settings( $list_currencies );
			self::set_checkout_options_settings( $params );
			self::set_display_options_settings( $params );
			self::set_settings( $params );

			if ( class_exists( 'WC_Cache_Helper' ) ) {
				\WC_Cache_Helper::get_transient_version( 'product', true ); // Update product price (currency) after change value.
			}
		}
		self::send_data_settings();
	}

	protected function get_exchange_rates_from_finance_api( $currency_object ) {
		$currencies       = $currency_object['currencies'];
		$default_currency = Helper::default_currency_code();
		$finance_api      = isset( $currency_object['financeApi'] ) ? $currency_object['financeApi'] : get_option( 'yay_currency_finance_api', 'default' );

		return array_map(
			function ( $currency ) use ( $default_currency, $finance_api ) {
				// Return 1 for default currency
				if ( $default_currency === $currency ) {
					return 1;
				}

				// Return N/A for empty currency
				if ( empty( $currency ) ) {
					return 'N/A';
				}

				// Get exchange rate
				$exchange_rate = RateHelper::get_exchange_rate_from_finance_api(
					array(
						'srcCurrency'  => $default_currency,
						'destCurrency' => $currency,
						'financeApi'   => $finance_api,
					)
				);

				return $exchange_rate ? $exchange_rate : 'N/A';
			},
			$currencies
		);
	}

	public function update_exchange_rate() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );

		if ( ! isset( $_POST['data'] ) ) {
			return;
		}

		try {
			$currency_object = Helper::sanitize( $_POST );

			if ( 'all' === $currency_object['type'] ) {
				$exchange_rates = self::get_exchange_rates_from_finance_api( $currency_object );
				wp_send_json_success(
					[
						'success'      => true,
						'exchangeRate' => $exchange_rates,
					]
				);
			}

			$exchange_rate = RateHelper::get_exchange_rate_from_finance_api( $currency_object );

			if ( ! $exchange_rate ) {
				wp_send_json_error();
			}

			wp_send_json_success(
				[
					'exchangeRate' => $exchange_rate,
				]
			);

		} catch ( \Exception $e ) {
			wp_send_json_error( $e );
		}
	}

	public function delete_currency() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		if ( isset( $_POST['data'] ) ) {
			$currency_ID = isset( $_POST['data']['ID'] ) ? sanitize_text_field( $_POST['data']['ID'] ) : false;
			if ( ! $currency_ID ) {
				$currency_code = isset( $_POST['data']['currency'] ) ? sanitize_text_field( $_POST['data']['currency'] ) : false;
				$currency_data = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );
				$currency_ID   = $currency_data ? $currency_data['ID'] : false;
			}

			$is_deleted = $currency_ID ? wp_delete_post( $currency_ID ) : false;

			// delete cache yay currencies list
			Helper::delete_yay_currencies_transient();

			wp_send_json(
				array(
					'status' => $is_deleted,
				)
			);
		}
	}

	public function ajax_handle_force_payment_response() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		$results             = array();
		$old_currency_ID     = isset( $_POST['yay_currency_old'] ) ? sanitize_key( $_POST['yay_currency_old'] ) : false;
		$new_apply_currency  = array();
		$detect_force_blocks = false;
		$currency_id_blocks  = YayCurrencyHelper::get_currency_id_blocks_name();
		if ( $old_currency_ID ) {
			$old_apply_currency = YayCurrencyHelper::get_currency_by_ID( $old_currency_ID );
			// cart - checkout use shortcode
			if ( isset( $_POST['yay_currency_new'] ) ) {
				$currency_ID        = sanitize_key( $_POST['yay_currency_new'] );
				$new_apply_currency = YayCurrencyHelper::get_currency_by_ID( $currency_ID );
			}
			// cart - checkout use blocks
			if ( isset( $_POST['woocommerce_blocks'] ) ) {
				$currency_ID = get_option( $currency_id_blocks, false );
				if ( $currency_ID ) {
					$detect_force_blocks = true;
					$new_apply_currency  = YayCurrencyHelper::get_currency_by_ID( $currency_ID );
					YayCurrencyHelper::set_cookie( YayCurrencyHelper::get_cookie_name(), $currency_ID );
					YayCurrencyHelper::set_cookie( YayCurrencyHelper::get_cookie_name( 'switcher' ), $currency_ID );
					WC()->cart->calculate_totals();
				}
			}

			if ( $new_apply_currency ) {
				$results = apply_filters( 'yay_currency_ajax_handle_response_force_payment_results', $results, $_POST, $old_apply_currency, $new_apply_currency );

				if ( $detect_force_blocks ) {
					delete_option( $currency_id_blocks );
				}

				wp_send_json_success( $results );
			}
		}

		wp_send_json_error();
	}

	public function ajax_handle_force_currency_by_payment_selected_response() {
		check_ajax_referer( 'yay-currency-nonce', 'nonce', true );
		$results            = array();
		$old_currency_ID    = isset( $_POST['yay_currency_old'] ) ? sanitize_key( $_POST['yay_currency_old'] ) : false;
		$new_apply_currency = array();

		if ( $old_currency_ID ) {
			$old_apply_currency = YayCurrencyHelper::get_currency_by_ID( $old_currency_ID );
			if ( isset( $_POST['yay_currency_new'] ) ) {
				$currency_ID        = sanitize_key( $_POST['yay_currency_new'] );
				$new_apply_currency = YayCurrencyHelper::get_currency_by_ID( $currency_ID );
			}

			if ( $new_apply_currency ) {
				$results = apply_filters( 'yay_currency_ajax_handle_response_force_currency_results', $results, $_POST, $old_apply_currency, $new_apply_currency );
				wp_send_json_success( $results );
			}
		}

		wp_send_json_error();

	}

	// Update Order Product Loop
	protected function update_wc_order_product_loop( $order_id ) {
		global $wpdb;
		$product_item = $wpdb->get_results(
			$wpdb->prepare(
				"SELECT * FROM {$wpdb->prefix}wc_order_product_lookup WHERE order_id = %d",
				$order_id
			)
		);
		if ( $product_item ) {
			foreach ( $product_item as $item ) {
				do_action( 'woocommerce_analytics_update_product', $item->order_item_id, $item->order_id );
			}
		}

	}

	// Update Order Coupon Loop
	protected function update_wc_order_coupon_loop( $order_id ) {
		global $wpdb;
		$coupon_item = $wpdb->get_results(
			$wpdb->prepare(
				"SELECT * FROM {$wpdb->prefix}wc_order_coupon_lookup WHERE order_id = %d",
				$order_id
			)
		);
		if ( $coupon_item ) {
			foreach ( $coupon_item as $item ) {
				do_action( 'woocommerce_analytics_update_coupon', $item->coupon_id, $item->order_id );
			}
		}

	}

	// Update Order Tax Loop
	protected function update_wc_order_tax_loop( $order_id ) {
		global $wpdb;
		$tax_item = $wpdb->get_results(
			$wpdb->prepare(
				"SELECT * FROM {$wpdb->prefix}wc_order_tax_lookup WHERE order_id = %d",
				$order_id
			)
		);
		if ( $tax_item ) {
			foreach ( $tax_item as $item ) {
				do_action( 'woocommerce_analytics_update_tax', $item->tax_rate_id, $item->order_id );
			}
		}

	}

	public function ajax_handle_sync_orders_revert_to_base() {

		$nonce = isset( $_POST['_nonce'] ) ? sanitize_text_field( $_POST['_nonce'] ) : false;

		if ( ! $nonce || ! wp_verify_nonce( sanitize_key( $nonce ), 'yay-currency-admin-nonce' ) ) {
			wp_send_json_error( array( 'message' => __( 'Nonce invalid', 'yay-currency' ) ) );
		}

		if ( isset( $_POST['_yay_sync'] ) ) {
			$paged           = isset( $_POST['_paged'] ) && ! empty( $_POST['_paged'] ) ? intval( sanitize_text_field( $_POST['_paged'] ) ) : 1;
			$sync_currencies = isset( $_POST['_sync_currencies'] ) && ! empty( $_POST['_sync_currencies'] ) ? map_deep( wp_unslash( $_POST['_sync_currencies'] ), 'sanitize_text_field' ) : array();
			$data            = Helper::get_list_orders_not_revert_to_base( $sync_currencies, $paged );
			if ( isset( $data['results'] ) && $data['results'] ) {
				foreach ( $data['results'] as $order_id ) {
					$order = wc_get_order( $order_id );
					if ( ! $order ) {
						continue;
					}
					Helper::order_match_reverted( $order_id, $order );
					self::update_wc_order_product_loop( $order_id );
					self::update_wc_order_coupon_loop( $order_id );
					self::update_wc_order_tax_loop( $order_id );
					OrdersStatsDataStore::update( $order );
				}
			}

			$args = array(
				'orders' => $data['results'],
			);

			if ( isset( $data['orders'] ) && $data['orders'] ) {
				$args['paged'] = $paged + 1;
			} else {
				update_option( 'yay_currency_orders_synced_to_base', 'yes' );
			}

			wp_send_json_success( $args );
		}

		wp_send_json_error();
	}
}
