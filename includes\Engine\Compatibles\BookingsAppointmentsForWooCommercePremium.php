<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;
class BookingsAppointmentsForWooCommercePremium {
	use SingletonTrait;

	private $apply_currency = array();
	protected $offer_controller;

	public function __construct() {

		if ( defined( 'PH_BOOKINGS_PLUGIN_VERSION' ) || class_exists( 'Woocommerce_Booking' ) ) {
			$this->apply_currency = YayCurrencyHelper::detect_current_currency();
			// Link plugin: https://www.pluginhive.com/product/woocommerce-booking-and-appointments/
			if ( defined( 'PH_BOOKINGS_PLUGIN_VERSION' ) ) {
				add_filter( 'ph_bookings_get_client_currency', array( $this, 'ph_bookings_get_client_currency' ), 99, 1 );
				add_filter( 'phive_booking_cost', array( $this, 'phive_booking_cost' ), 99, 4 );
				add_filter( 'woocommerce_add_cart_item_data', array( $this, 'add_cart_item_data_booking_infos_with_cart_item' ), 10, 3 );
				add_action( 'woocommerce_get_cart_item_from_session', array( $this, 'woocommerce_get_cart_item_from_session' ), 20, 3 );
			}
			// Link plugin: https://www.tychesoftwares.com/products/woocommerce-booking-and-appointment-plugin/
			if ( class_exists( 'Woocommerce_Booking' ) ) {
				add_filter( 'bkap_final_price_json_data', array( $this, 'bkap_final_price_json_data' ), 20, 2 );
				if ( FixedPriceHelper::is_set_fixed_price() ) {
					add_action( 'yay_currency_set_cart_contents', array( $this, 'product_addons_set_cart_contents' ), 10, 4 );
					add_filter( 'yay_currency_get_price_default_in_checkout_page', array( $this, 'get_price_default_in_checkout_page' ), 20, 2 );
					add_filter( 'yay_currency_product_price_3rd_with_condition', array( $this, 'get_price_with_options' ), 20, 2 );
				}
			}
		}

	}

	public function ph_bookings_get_client_currency( $currency ) {
		if ( ! $this->apply_currency || ! isset( $this->apply_currency['currency'] ) ) {
			return $currency;
		}
		return $this->apply_currency['currency'];
	}

	public function phive_booking_cost( $asset_applied_price, $product, $customer_choosen_values, $booking_data ) {
		if ( ! $this->apply_currency || ! isset( $this->apply_currency['currency'] ) ) {
			return $asset_applied_price;
		}
		return YayCurrencyHelper::calculate_price_by_currency( $asset_applied_price, false, $this->apply_currency );
	}

	public function add_cart_item_data_booking_infos_with_cart_item( $cart_item_data, $product_id, $variation_id ) {
		if ( isset( $cart_item_data['phive_booked_price'] ) ) {
			$cart_item_data['phive_booked_price_yay_currency_added'] = $this->apply_currency;
		}
		return $cart_item_data;
	}

	public function woocommerce_get_cart_item_from_session( $cart_item ) {
		if ( isset( $cart_item['phive_booked_price'] ) ) {
			$booked_price                          = isset( $cart_item['phive_booked_price'] ) && ! empty( $cart_item['phive_booked_price'] ) ? $cart_item['phive_booked_price'] : false;
			$phive_booked_price_yay_currency_added = isset( $cart_item['phive_booked_price_yay_currency_added'] ) && ! empty( $cart_item['phive_booked_price_yay_currency_added'] ) ? $cart_item['phive_booked_price_yay_currency_added'] : false;
			if ( $booked_price && $phive_booked_price_yay_currency_added ) {
				$booked_price_default = $booked_price / YayCurrencyHelper::get_rate_fee( $phive_booked_price_yay_currency_added );
				if ( $this->apply_currency['currency'] !== $phive_booked_price_yay_currency_added['currency'] ) {
					$booked_price = YayCurrencyHelper::calculate_price_by_currency( $booked_price_default, false, $this->apply_currency );
				}
			}
			$cart_item['data']->set_price( $booked_price );
		}
		return $cart_item;
	}

	public function bkap_final_price_json_data( $wp_send_json, $product_id ) {
		if ( isset( $wp_send_json['bkap_price'] ) && isset( $wp_send_json['total_price_calculated'] ) ) {
			$total_price_calculated = $wp_send_json['total_price_calculated'];
			$formatted_price        = YayCurrencyHelper::calculate_price_by_currency_html( $this->apply_currency, $total_price_calculated );
			if ( FixedPriceHelper::is_set_fixed_price() ) {
				$product             = wc_get_product( $product_id );
				$fixed_product_price = FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $this->apply_currency );
				if ( $fixed_product_price ) {
					$formatted_price = YayCurrencyHelper::format_price( $fixed_product_price );
				}
			}
			$wp_send_json['bkap_price'] = get_option( 'book_price-label' ) . ' ' . $formatted_price;
		}
		return $wp_send_json;
	}

	public function product_addons_set_cart_contents( $cart_contents, $cart_item_key, $cart_item, $apply_currency ) {
		$cart_item_booking     = isset( $cart_item['bkap_booking'] ) && ! empty( $cart_item['bkap_booking'] ) ? $cart_item['bkap_booking'] : false;
		$booking_price_default = isset( $cart_item_booking['0']['price'] ) && ! empty( $cart_item_booking['0']['price'] ) ? $cart_item_booking['0']['price'] : false;
		if ( $cart_item_booking && $booking_price_default ) {
			$booking_price_current = apply_filters( 'yay_currency_convert_price', $booking_price_default, $this->apply_currency );
			$fixed_product_price   = FixedPriceHelper::product_is_set_fixed_price_by_currency( $cart_item['data'], $this->apply_currency );
			if ( $fixed_product_price ) {
				$booking_price_current = $fixed_product_price;
				SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_bkap_booking_price', $booking_price_default );
				SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_bkap_booking_price_current', $booking_price_current );
			}
		}
	}

	public function get_price_default_in_checkout_page( $price, $product ) {
		$booking_price = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_bkap_booking_price' );
		if ( $booking_price ) {
			return $booking_price;
		}
		return $price;

	}

	public function get_price_with_options( $price, $product ) {
		$booking_price = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_bkap_booking_price_current' );
		if ( $booking_price ) {
			return $booking_price;
		}
		return $price;
	}
}
