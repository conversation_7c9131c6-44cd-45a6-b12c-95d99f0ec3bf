<?php
namespace Yay_Currency\Engine\BEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\YayCurrencyHelper;

use Automattic\WooCommerce\Admin\API\Reports\Categories\DataStore as CategoriesDataStore;
use Automattic\WooCommerce\Admin\API\Reports\Coupons\DataStore as CouponsDataStore;
use Automattic\WooCommerce\Admin\API\Reports\Customers\DataStore as CustomersDataStore;
use Automattic\WooCommerce\Admin\API\Reports\Products\DataStore as ProductsDataStore;

defined( 'ABSPATH' ) || exit;

class WooCommerceFilterAnalytics {

	use SingletonTrait;

	public $default_currency;
	private $converted_currency;
	public function __construct() {

		// convert coupons to default currency
		add_action( 'woocommerce_analytics_update_coupon', array( $this, 'convert_coupons' ), 20, 2 );

		// convert products to default currency
		add_action( 'woocommerce_analytics_update_product', array( $this, 'convert_products' ), 20, 2 );

		// convert tax to default currency
		add_action( 'woocommerce_analytics_update_tax', array( $this, 'convert_tax' ), 20, 2 );

		// convert order stats to default currency
		add_filter( 'woocommerce_analytics_update_order_stats_data', array( $this, 'convert_order_stats_data' ), 20, 2 );

		add_action( 'init', array( $this, 'add_currencies_dropdown_filter' ) );

	}

	public function convert_coupons( $coupon_id, $order_id ) {
		Helper::order_match_reverted( $order_id );
		Helper::revert_coupon_loop_to_default( $coupon_id, $order_id );
	}

	public function convert_products( $order_item_id, $order_id ) {
		Helper::order_match_reverted( $order_id );
		Helper::revert_product_loop_to_default( $order_item_id, $order_id );
	}

	public function convert_tax( $tax_rate_id, $order_id ) {
		Helper::order_match_reverted( $order_id );
		Helper::revert_tax_loop_to_default( $tax_rate_id, $order_id );
	}

	public function convert_order_stats_data( $order_data, $order ) {
		$order_id = $order->get_id();
		Helper::order_match_reverted( $order_id, $order );
		$rate = Helper::calculate_order_rate( $order_id );
		if ( $rate ) {
			$order_data['total_sales']    = $order_data['total_sales'] / $rate;
			$order_data['tax_total']      = $order_data['tax_total'] / $rate;
			$order_data['shipping_total'] = $order_data['shipping_total'] / $rate;
			$order_data['net_total']      = $order_data['net_total'] / $rate;
		}
		return $order_data;

	}

	protected function analytics_query_args() {
		return array(
			'products',
			'products_stats',
			'revenue',
			'orders',
			'orders_stats',
			'variations',
			'variations_stats',
			'categories',
			'categories_stats',
			'coupons',
			'coupons_stats',
			'taxes',
			'taxes_stats',
		);
	}

	protected function analytics_subquery_args() {
		$args = array(
			'products',
			'orders',
			'variations',
			'categories',
			'coupons',
			'taxes',
		);
		return $args;
	}

	public function add_currencies_dropdown_filter() {

		if ( apply_filters( 'yay_currency_hide_dropdown_filter_analytics', false ) ) {
			return;
		}

		$this->default_currency = Helper::default_currency_code();

		add_filter( 'woocommerce_analytics_report_should_use_cache', array( $this, 'woocommerce_analytics_report_should_use_cache' ), 20, 2 );

		$query_args = self::analytics_query_args();

		foreach ( $query_args as $field ) {
			add_filter( 'woocommerce_analytics_' . $field . '_query_args', array( $this, 'filter_stats_by_currency' ) );
		}

		$subquery_args = self::analytics_subquery_args();

		foreach ( $subquery_args as  $field ) {
			// join
			add_filter( 'woocommerce_analytics_clauses_join_' . $field . '_subquery', array( $this, 'concat_join_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_join_' . $field . '_stats_total', array( $this, 'concat_join_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_join_' . $field . '_stats_interval', array( $this, 'concat_join_subquery' ) );
			// where
			add_filter( 'woocommerce_analytics_clauses_where_' . $field . '_subquery', array( $this, 'concat_where_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_where_' . $field . '_stats_total', array( $this, 'concat_where_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_where_' . $field . '_stats_interval', array( $this, 'concat_where_subquery' ) );
			// select
			add_filter( 'woocommerce_analytics_clauses_select_' . $field . '_subquery', array( $this, 'concat_select_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_select_' . $field . '_stats_total', array( $this, 'concat_select_subquery' ) );
			add_filter( 'woocommerce_analytics_clauses_select_' . $field . '_stats_interval', array( $this, 'concat_select_subquery' ) );
		}

		// OVERVIEW ANALYTICS
		add_filter( 'woocommerce_leaderboards', array( $this, 'custom_leaderboards_analytics' ), 20, 5 );

		// PRODUCTS ANALYTICS
		add_filter( 'woocommerce_analytics_products_select_query', array( $this, 'woocommerce_analytics_products_select_query' ), 20, 2 );
		add_filter( 'woocommerce_analytics_products_stats_select_query', array( $this, 'woocommerce_analytics_products_stats_select_query' ), 20, 2 );

		// REVENUE ANALYTICS
		add_filter( 'woocommerce_analytics_revenue_select_query', array( $this, 'woocommerce_analytics_revenue_select_query' ), 20, 2 );

		// ORDERS ANALYTICS
		add_filter( 'woocommerce_analytics_orders_select_query', array( $this, 'woocommerce_analytics_orders_select_query' ), 20, 2 );
		add_filter( 'woocommerce_analytics_orders_stats_select_query', array( $this, 'woocommerce_analytics_orders_stats_select_query' ), 20, 2 );

		// VARIATIONS ANALYTICS
		add_filter( 'woocommerce_analytics_variations_stats_select_query', array( $this, 'woocommerce_analytics_variations_stats_select_query' ), 20, 2 );
		add_filter( 'woocommerce_analytics_variations_select_query', array( $this, 'woocommerce_analytics_variations_select_query' ), 20, 2 );

		// CATEGORIES ANALYTICS
		add_filter( 'woocommerce_analytics_categories_select_query', array( $this, 'woocommerce_analytics_categories_select_query' ), 20, 2 );

		// COUPONS ANALYTICS
		add_filter( 'woocommerce_analytics_coupons_select_query', array( $this, 'woocommerce_analytics_coupons_select_query' ), 20, 2 );

		// TAXES ANALYTICS
		add_filter( 'woocommerce_analytics_taxes_stats_select_query', array( $this, 'woocommerce_analytics_taxes_stats_select_query' ), 20, 2 );
		add_filter( 'woocommerce_analytics_taxes_select_query', array( $this, 'woocommerce_analytics_taxes_select_query' ), 20, 2 );

		if ( is_admin() ) {
			$converted_currencies = self::analytics_currencies();

			wp_enqueue_script( 'yay-currency-analytics', YAY_CURRENCY_PLUGIN_URL . 'src/analytics.js', array(), YAY_CURRENCY_VERSION, true );
			wp_localize_script(
				'yay-currency-analytics',
				'yayCurrencyAnalytics',
				array(
					'defaultCurrency' => 'all_currency',
					'currencies'      => $converted_currencies,
				)
			);
			$data_registry = \Automattic\WooCommerce\Blocks\Package::container()->get( \Automattic\WooCommerce\Blocks\Assets\AssetDataRegistry::class );
			$data_registry->add( 'multiCurrency', self::analytics_dropdown_currencies( $converted_currencies ) );
		}
	}

	protected function analytics_currencies() {
		$currencies           = Helper::get_currencies_post_type();
		$converted_currencies = array();

		foreach ( $currencies as $currency ) {
			$currency_meta = get_post_meta( $currency->ID, '', false );
			if ( ! $currency_meta ) {
				continue;
			}
			$currency_code = $currency->post_title;
			array_push(
				$converted_currencies,
				array(
					$currency_code => array(
						'code'              => $currency_code,
						'symbol'            => html_entity_decode( YayCurrencyHelper::get_symbol_by_currency_code( $currency_code ) ),
						'symbolPosition'    => $currency_meta['currency_position'][0],
						'thousandSeparator' => $currency_meta['thousand_separator'][0],
						'decimalSeparator'  => $currency_meta['decimal_separator'][0],
						'precision'         => $currency_meta['number_decimal'][0],
					),
				)
			);

		}
		return $converted_currencies;
	}

	protected function analytics_dropdown_currencies( $converted_currencies = array() ) {
		$dropdown_currencies = array(
			array(
				'label' => __( 'All currencies', 'yay-currency' ),
				'value' => 'all_currency',
			),
		);
		$list_currencies     = Helper::woo_list_currencies();

		foreach ( $converted_currencies as $key => $value ) {
			if ( ! isset( $list_currencies[ reset( $value )['code'] ] ) ) {
				continue;
			}
			$decoded_currency_name       = wp_kses_post( html_entity_decode( $list_currencies[ reset( $value )['code'] ] ) );
			$currency_symbol             = wp_kses_post( html_entity_decode( reset( $value )['symbol'] ) );
			$currency_code               = wp_kses_post( html_entity_decode( reset( $value )['code'] ) );
			$dropdown_converted_currency = array(
				'label' => __( $decoded_currency_name . '(' . $currency_symbol . ') - ' . $currency_code, 'yay-currency' ),
				'value' => $currency_code,
			);
			array_push( $dropdown_currencies, $dropdown_converted_currency );
		}

		return $dropdown_currencies;
	}

	public function woocommerce_analytics_report_should_use_cache( $flag, $cache_key ) {
		$flag = false;
		return $flag;
	}

	public function filter_stats_by_currency( $args ) {
		$args['currency'] = isset( $_GET['currency'] ) && 'all_currency' !== $_GET['currency'] ? sanitize_text_field( wp_unslash( $_GET['currency'] ) ) : $this->default_currency;
		return $args;
	}

	public function concat_join_subquery( $clauses ) {
		if ( isset( $_GET['currency'] ) && 'all_currency' !== $_GET['currency'] ) {
			global $wpdb;
			if ( Helper::check_custom_orders_table_usage_enabled() ) {
				$clauses[] = "JOIN {$wpdb->prefix}wc_orders currency_postmeta ON {$wpdb->prefix}wc_order_stats.order_id = currency_postmeta.id";
			} else {
				$clauses[] = "JOIN {$wpdb->postmeta} currency_postmeta ON {$wpdb->prefix}wc_order_stats.order_id = currency_postmeta.post_id";
			}
		}
		return $clauses;
	}

	public function concat_where_subquery( $clauses ) {
		if ( isset( $_GET['currency'] ) && 'all_currency' !== $_GET['currency'] ) {
			global $wpdb;
			$currency = $this->default_currency;
			$pattern  = '/^[a-zA-Z]{3}+$/';
			if ( ! empty( $_GET['currency'] ) ) {
				$currency = sanitize_text_field( wp_unslash( $_GET['currency'] ) );
			}
			if ( preg_match( $pattern, $currency ) ) {

				if ( Helper::check_custom_orders_table_usage_enabled() ) {
					$clauses[] = $wpdb->prepare( 'AND currency_postmeta.currency = %s', $currency );
				} else {
					$clauses[] = $wpdb->prepare( "AND currency_postmeta.meta_key = '_order_currency' AND currency_postmeta.meta_value = %s", $currency );
				}
			}
		}
		return $clauses;
	}

	public function concat_select_subquery( $clauses ) {
		if ( isset( $_GET['currency'] ) && 'all_currency' !== $_GET['currency'] ) {
			$currency = $this->default_currency;
			if ( isset( $_GET['currency'] ) ) {
				$currency = sanitize_text_field( wp_unslash( $_GET['currency'] ) );
			}

			if ( Helper::check_custom_orders_table_usage_enabled() ) {
				$clauses[] = ', currency_postmeta.currency AS currency';
			} else {
				$clauses[] = ', currency_postmeta.meta_value AS currency';
			}
		}
		return $clauses;
	}

	protected function get_apply_currency_by_analytics() {
		if ( isset( $_GET['currency'] ) && 'all_currency' !== $_GET['currency'] ) {
			$currency = sanitize_text_field( wp_unslash( $_GET['currency'] ) );
			return YayCurrencyHelper::get_currency_by_currency_code( $currency );
		}
		return false;
	}

	public function woocommerce_analytics_products_stats_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			$results->totals->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->totals->net_revenue, false, $apply_currency );
		}

		return $results;
	}

	public function woocommerce_analytics_products_select_query( $results, $args ) {

		if ( ! isset( $results->data ) ) {
			return $results;
		}

		$apply_currency = self::get_apply_currency_by_analytics();

		if ( $apply_currency ) {
			foreach ( $results->data as $key => $value ) {
				$results->data[ $key ]['net_revenue'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $key ]['net_revenue'], false, $apply_currency );
			}
		}

		return $results;
	}

	public function woocommerce_analytics_revenue_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			if ( isset( $results->totals->gross_sales ) ) {
				$results->totals->gross_sales = YayCurrencyHelper::calculate_price_by_currency( $results->totals->gross_sales, false, $apply_currency );
			}
			if ( isset( $results->totals->refunds ) ) {
				$results->totals->refunds = YayCurrencyHelper::calculate_price_by_currency( $results->totals->refunds, false, $apply_currency );
			}
			if ( isset( $results->totals->net_revenue ) ) {
				$results->totals->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->totals->net_revenue, false, $apply_currency );
			}
			if ( isset( $results->totals->total_sales ) ) {
				$results->totals->total_sales = YayCurrencyHelper::calculate_price_by_currency( $results->totals->total_sales, false, $apply_currency );
			}
			if ( isset( $results->totals->shipping ) ) {
				$results->totals->shipping = YayCurrencyHelper::calculate_price_by_currency( $results->totals->shipping, false, $apply_currency );
			}
			if ( isset( $results->totals->taxes ) ) {
				$results->totals->taxes = YayCurrencyHelper::calculate_price_by_currency( $results->totals->taxes, false, $apply_currency );
			}
			if ( isset( $results->totals->avg_order_value ) ) {
				$results->totals->avg_order_value = YayCurrencyHelper::calculate_price_by_currency( $results->totals->avg_order_value, false, $apply_currency );
			}
			if ( isset( $results->totals->coupons ) ) {
				$results->totals->coupons = YayCurrencyHelper::calculate_price_by_currency( $results->totals->coupons, false, $apply_currency );
			}

			if ( isset( $results->intervals ) && ! empty( $results->intervals ) ) {
				foreach ( $results->intervals as $interval_key => $interval ) {
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->gross_sales ) ) {
						$results->intervals[ $interval_key ]['subtotals']->gross_sales = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->gross_sales, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->refunds ) ) {
						$results->intervals[ $interval_key ]['subtotals']->refunds = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->refunds, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->net_revenue ) ) {
						$results->intervals[ $interval_key ]['subtotals']->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->net_revenue, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->taxes ) ) {
						$results->intervals[ $interval_key ]['subtotals']->taxes = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->taxes, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->shipping ) ) {
						$results->intervals[ $interval_key ]['subtotals']->shipping = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->shipping, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->avg_order_value ) ) {
						$results->intervals[ $interval_key ]['subtotals']->avg_order_value = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->avg_order_value, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->total_sales ) ) {
						$results->intervals[ $interval_key ]['subtotals']->total_sales = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->total_sales, false, $apply_currency );
					}
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->coupons ) ) {
						$results->intervals[ $interval_key ]['subtotals']->coupons = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->coupons, false, $apply_currency );
					}
				}
			}
		}

		return $results;
	}

	public function custom_leaderboards_analytics( $leaderboards, $per_page, $after, $before, $persisted_query ) {
		$leaderboards = array(
			$this->get_categories_leaderboard( $per_page, $after, $before, $persisted_query ),
			$this->get_products_leaderboard( $per_page, $after, $before, $persisted_query ),
			$this->get_coupons_leaderboard( $per_page, $after, $before, $persisted_query ),
			$this->get_customers_leaderboard( $per_page, $after, $before, $persisted_query ),
		);
		return $leaderboards;
	}

	public function get_categories_leaderboard( $per_page, $after, $before, $persisted_query ) {
		$categories_data_store = new CategoriesDataStore();
		$categories_data       = $per_page > 0 ? $categories_data_store->get_data(
			apply_filters(
				'woocommerce_analytics_categories_query_args',
				array(
					'orderby'       => 'items_sold',
					'order'         => 'desc',
					'after'         => $after,
					'before'        => $before,
					'per_page'      => $per_page,
					'extended_info' => true,
				)
			)
		)->data : array();

		$rows = array();

		$apply_currency  = self::get_apply_currency_by_analytics();
		$currency_symbol = $apply_currency ? $apply_currency['symbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $this->default_currency );

		foreach ( $categories_data as $category ) {
			$url_query     = wp_parse_args(
				array(
					'filter'     => 'single_category',
					'categories' => $category['category_id'],
				),
				$persisted_query
			);
			$category_url  = wc_admin_url( '/analytics/categories', $url_query );
			$category_name = isset( $category['extended_info'] ) && isset( $category['extended_info']['name'] ) ? $category['extended_info']['name'] : '';

			if ( $apply_currency ) {
				if ( isset( $category['net_revenue'] ) ) {
					$category['net_revenue'] = YayCurrencyHelper::calculate_price_by_currency( $category['net_revenue'], false, $apply_currency );
				}
			}

			$rows[] = array(
				array(
					'display' => '<a href="' . esc_attr( $category_url ) . '">' . esc_html( $category_name ) . '</a>',
					'value'   => $category_name,
				),
				array(
					'display' => wc_admin_number_format( $category['items_sold'] ),
					'value'   => $category['items_sold'],
				),
				array(
					'display' => wp_kses_post( html_entity_decode( '~' . $currency_symbol . number_format( $category['net_revenue'], 2, '.', ',' ) ) ),
					'value'   => $category['net_revenue'],
				),
			);
		}

		return array(
			'id'      => 'categories',
			'label'   => __( 'Top Categories - Items Sold', 'woocommerce' ),
			'headers' => array(
				array(
					'label' => __( 'Category', 'woocommerce' ),
				),
				array(
					'label' => __( 'Items Sold', 'woocommerce' ),
				),
				array(
					'label' => __( 'Net Sales', 'woocommerce' ),
				),
			),
			'rows'    => $rows,
		);
	}

	public function get_products_leaderboard( $per_page, $after, $before, $persisted_query ) {
		$products_data_store = new ProductsDataStore();
		$products_data       = $per_page > 0 ? $products_data_store->get_data(
			apply_filters(
				'woocommerce_analytics_products_query_args',
				array(
					'orderby'       => 'items_sold',
					'order'         => 'desc',
					'after'         => $after,
					'before'        => $before,
					'per_page'      => $per_page,
					'extended_info' => true,
				)
			)
		)->data : array();

		$rows            = array();
		$apply_currency  = self::get_apply_currency_by_analytics();
		$currency_symbol = $apply_currency ? $apply_currency['symbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $this->default_currency );

		foreach ( $products_data as $product ) {
			$url_query    = wp_parse_args(
				array(
					'filter'   => 'single_product',
					'products' => $product['product_id'],
				),
				$persisted_query
			);
			$product_url  = wc_admin_url( '/analytics/products', $url_query );
			$product_name = isset( $product['extended_info'] ) && isset( $product['extended_info']['name'] ) ? $product['extended_info']['name'] : '';

			if ( $apply_currency ) {
				$product['net_revenue'] = YayCurrencyHelper::calculate_price_by_currency( $product['net_revenue'], false, $apply_currency );
			}

			$rows[] = array(
				array(
					'display' => '<a href="' . esc_attr( $product_url ) . '">' . esc_html( $product_name ) . '</a>',
					'value'   => $product_name,
				),
				array(
					'display' => wc_admin_number_format( $product['items_sold'] ),
					'value'   => $product['items_sold'],
				),
				array(
					'display' => wp_kses_post( html_entity_decode( '~' . $currency_symbol . number_format( $product['net_revenue'], 2, '.', ',' ) ) ),
					'value'   => $product['net_revenue'],
				),
			);
		}

		return array(
			'id'      => 'products',
			'label'   => __( 'Top Products - Items Sold', 'woocommerce' ),
			'headers' => array(
				array(
					'label' => __( 'Product', 'woocommerce' ),
				),
				array(
					'label' => __( 'Items Sold', 'woocommerce' ),
				),
				array(
					'label' => __( 'Net Sales', 'woocommerce' ),
				),
			),
			'rows'    => $rows,
		);
	}

	public function get_coupons_leaderboard( $per_page, $after, $before, $persisted_query ) {
		$coupons_data_store = new CouponsDataStore();
		$coupons_data       = $per_page > 0 ? $coupons_data_store->get_data(
			apply_filters(
				'woocommerce_analytics_coupons_query_args',
				array(
					'orderby'       => 'orders_count',
					'order'         => 'desc',
					'after'         => $after,
					'before'        => $before,
					'per_page'      => $per_page,
					'extended_info' => true,
				)
			)
		)->data : array();

		$rows            = array();
		$apply_currency  = self::get_apply_currency_by_analytics();
		$currency_symbol = $apply_currency ? $apply_currency['symbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $this->default_currency );

		foreach ( $coupons_data as $coupon ) {
			$url_query   = wp_parse_args(
				array(
					'filter'  => 'single_coupon',
					'coupons' => $coupon['coupon_id'],
				),
				$persisted_query
			);
			$coupon_url  = wc_admin_url( '/analytics/coupons', $url_query );
			$coupon_code = isset( $coupon['extended_info'] ) && isset( $coupon['extended_info']['code'] ) ? $coupon['extended_info']['code'] : '';

			if ( $apply_currency ) {
				$coupon['amount'] = YayCurrencyHelper::calculate_price_by_currency( $coupon['amount'], false, $apply_currency );
			}

			$rows[] = array(
				array(
					'display' => '<a href="' . esc_attr( $coupon_url ) . '">' . esc_html( $coupon_code ) . '</a>',
					'value'   => $coupon_code,
				),
				array(
					'display' => wc_admin_number_format( $coupon['orders_count'] ),
					'value'   => $coupon['orders_count'],
				),
				array(
					'display' => wp_kses_post( html_entity_decode( '~' . $currency_symbol . number_format( $coupon['amount'], 2, '.', ',' ) ) ),
					'value'   => $coupon['amount'],
				),
			);
		}

		return array(
			'id'      => 'coupons',
			'label'   => __( 'Top Coupons - Number of Orders', 'woocommerce' ),
			'headers' => array(
				array(
					'label' => __( 'Coupon Code', 'woocommerce' ),
				),
				array(
					'label' => __( 'Orders', 'woocommerce' ),
				),
				array(
					'label' => __( 'Amount Discounted', 'woocommerce' ),
				),
			),
			'rows'    => $rows,
		);
	}

	public function get_customers_leaderboard( $per_page, $after, $before, $persisted_query ) {
		$customers_data_store = new CustomersDataStore();
		$customers_data       = $per_page > 0 ? $customers_data_store->get_data(
			apply_filters(
				'woocommerce_analytics_customers_query_args',
				array(
					'orderby'      => 'total_spend',
					'order'        => 'desc',
					'order_after'  => $after,
					'order_before' => $before,
					'per_page'     => $per_page,
				)
			)
		)->data : array();

		$rows            = array();
		$apply_currency  = self::get_apply_currency_by_analytics();
		$currency_symbol = $apply_currency ? $apply_currency['symbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $this->default_currency );

		foreach ( $customers_data as $customer ) {
			$url_query    = wp_parse_args(
				array(
					'filter'    => 'single_customer',
					'customers' => $customer['id'],
				),
				$persisted_query
			);
			$customer_url = wc_admin_url( '/analytics/customers', $url_query );
			if ( $apply_currency ) {
				$customer['total_spend'] = YayCurrencyHelper::calculate_price_by_currency( $customer['total_spend'], false, $apply_currency );
			}
			$rows[] = array(
				array(
					'display' => '<a href="' . esc_attr( $customer_url ) . '">' . esc_html( $customer['name'] ) . '</a>',
					'value'   => $customer['name'],
				),
				array(
					'display' => $customer['orders_count'],
					'value'   => $customer['orders_count'],
				),
				array(
					'display' => wp_kses_post( html_entity_decode( '~' . $currency_symbol . number_format( $customer['total_spend'], 2, '.', ',' ) ) ),
					'value'   => $customer['total_spend'],
				),
			);
		}

		return array(
			'id'      => 'customers',
			'label'   => __( 'Top Customers - Total Spend', 'woocommerce' ),
			'headers' => array(
				array(
					'label' => __( 'Customer Name', 'woocommerce' ),
				),
				array(
					'label' => __( 'Orders', 'woocommerce' ),
				),
				array(
					'label' => __( 'Total Spend', 'woocommerce' ),
				),
			),
			'rows'    => $rows,
		);
	}

	public function woocommerce_analytics_orders_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			foreach ( $results->data as $key => $value ) {
				if ( isset( $results->data[ $key ]['net_total'] ) ) {
					$results->data[ $key ]['net_total'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $key ]['net_total'], false, $apply_currency );
				}
				if ( isset( $results->data[ $key ]['total_sales'] ) ) {
					$results->data[ $key ]['total_sales'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $key ]['total_sales'], false, $apply_currency );
				}
			}
		}

		return $results;
	}

	public function woocommerce_analytics_orders_stats_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			if ( isset( $results->totals->gross_sales ) ) {
				$results->totals->gross_sales = YayCurrencyHelper::calculate_price_by_currency( $results->totals->gross_sales, false, $apply_currency );
			}
			if ( isset( $results->totals->net_revenue ) ) {
				$results->totals->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->totals->net_revenue, false, $apply_currency );
			}
			if ( isset( $results->totals->total_sales ) ) {
				$results->totals->total_sales = YayCurrencyHelper::calculate_price_by_currency( $results->totals->total_sales, false, $apply_currency );
			}
			if ( isset( $results->totals->shipping ) ) {
				$results->totals->shipping = YayCurrencyHelper::calculate_price_by_currency( $results->totals->shipping, false, $apply_currency );
			}
			if ( isset( $results->totals->taxes ) ) {
				$results->totals->taxes = YayCurrencyHelper::calculate_price_by_currency( $results->totals->taxes, false, $apply_currency );
			}
			if ( isset( $results->totals->avg_order_value ) ) {
				$results->totals->avg_order_value = YayCurrencyHelper::calculate_price_by_currency( $results->totals->avg_order_value, false, $apply_currency );
			}
			if ( isset( $results->totals->coupons ) ) {
				$results->totals->coupons = YayCurrencyHelper::calculate_price_by_currency( $results->totals->coupons, false, $apply_currency );
			}
			if ( isset( $results->totals->refunds ) ) {
				$results->totals->refunds = YayCurrencyHelper::calculate_price_by_currency( $results->totals->refunds, false, $apply_currency );
			}
		}
		return $results;
	}

	public function woocommerce_analytics_variations_stats_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {

			if ( isset( $results->totals->net_revenue ) ) {
				$results->totals->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->totals->net_revenue, false, $apply_currency );
			}

			if ( isset( $results->intervals ) && ! empty( $results->intervals ) ) {
				foreach ( $results->intervals as $interval_key => $interval ) {
					if ( isset( $results->intervals[ $interval_key ]['subtotals']->net_revenue ) ) {
						$results->intervals[ $interval_key ]['subtotals']->net_revenue = YayCurrencyHelper::calculate_price_by_currency( $results->intervals[ $interval_key ]['subtotals']->net_revenue, false, $apply_currency );
					}
				}
			}
		}

		return $results;
	}

	public function woocommerce_analytics_variations_select_query( $results, $args ) {
		if ( ! isset( $results->data ) ) {
			return $results;
		}

		$apply_currency = self::get_apply_currency_by_analytics();

		if ( $apply_currency ) {
			foreach ( $results->data as $key => $value ) {
				if ( isset( $value['variation_id'] ) && ! empty( $value['variation_id'] ) && isset( $results->data[ $key ]['net_revenue'] ) ) {
					$results->data[ $key ]['net_revenue'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $key ]['net_revenue'], false, $apply_currency );
				}
			}
		}

		return $results;
	}

	public function woocommerce_analytics_categories_select_query( $results, $args ) {
		if ( ! isset( $results->data ) ) {
			return $results;
		}
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			foreach ( $results->data as $category_key => $category ) {
				if ( isset( $results->data[ $category_key ]['net_revenue'] ) ) {
					$results->data[ $category_key ]['net_revenue'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $category_key ]['net_revenue'], false, $apply_currency );
				}
			}
		}

		return $results;
	}

	public function woocommerce_analytics_coupons_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			if ( isset( $results->totals ) && isset( $results->totals->amount ) ) {
				$results->totals->amount = YayCurrencyHelper::calculate_price_by_currency( $results->totals->amount, false, $apply_currency );
			} else {
				if ( ! isset( $results->data ) ) {
					return $results;
				}
				foreach ( $results->data as $coupon_key => $coupon ) {
					if ( isset( $results->data[ $coupon_key ]['amount'] ) ) {
						$results->data[ $coupon_key ]['amount'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $coupon_key ]['amount'], false, $apply_currency );
					}
				}
			}
		}

		return $results;
	}

	public function woocommerce_analytics_taxes_stats_select_query( $results, $args ) {
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			if ( isset( $results->totals->order_tax ) ) {
				$results->totals->order_tax = YayCurrencyHelper::calculate_price_by_currency( $results->totals->order_tax, false, $apply_currency );
			}
			if ( isset( $results->totals->shipping_tax ) ) {
				$results->totals->shipping_tax = YayCurrencyHelper::calculate_price_by_currency( $results->totals->shipping_tax, false, $apply_currency );
			}
			if ( isset( $results->totals->total_tax ) ) {
				$results->totals->total_tax = YayCurrencyHelper::calculate_price_by_currency( $results->totals->total_tax, false, $apply_currency );
			}
		}

		return $results;
	}

	public function woocommerce_analytics_taxes_select_query( $results, $args ) {
		if ( ! isset( $results->data ) ) {
			return $results;
		}
		$apply_currency = self::get_apply_currency_by_analytics();
		if ( $apply_currency ) {
			foreach ( $results->data as $tax_key => $tax ) {
				if ( isset( $results->data[ $tax_key ]['shipping_tax'] ) ) {
					$results->data[ $tax_key ]['shipping_tax'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $tax_key ]['shipping_tax'], false, $apply_currency );
				}
				if ( isset( $results->data[ $tax_key ]['order_tax'] ) ) {
					$results->data[ $tax_key ]['order_tax'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $tax_key ]['order_tax'], false, $apply_currency );
				}
				if ( isset( $results->data[ $tax_key ]['total_tax'] ) ) {
					$results->data[ $tax_key ]['total_tax'] = YayCurrencyHelper::calculate_price_by_currency( $results->data[ $tax_key ]['total_tax'], false, $apply_currency );
				}
			}
		}

		return $results;
	}
}
