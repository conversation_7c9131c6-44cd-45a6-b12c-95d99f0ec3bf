<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class RateHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function get_exchange_rates( $currency_params_template ) {
		$url_template = 'https://query1.finance.yahoo.com/v8/finance/chart/$src$dest=X?interval=1m';
		$url          = strtr( $url_template, $currency_params_template );
		$json_data    = wp_remote_get( $url );
		return $json_data;
	}

	public static function get_rate_fee_from_currency_not_exists_in_list( $currency_code ) {
		$exchange_data = self::get_exchange_rates(
			array(
				'$src'  => Helper::default_currency_code(),
				'$dest' => $currency_code,
			)
		);

		// Return 1 on error or invalid response
		if ( is_wp_error( $exchange_data ) || ! isset( $exchange_data['response']['code'] ) || 200 !== $exchange_data['response']['code'] ) {
			return 1;
		}

		$data   = json_decode( wp_remote_retrieve_body( $exchange_data ) );
		$result = isset( $data->chart->result[0] ) ? $data->chart->result[0] : null;

		// Return 1 if no result is found
		if ( ! $result ) {
			return 1;
		}

		// Return close value or previous close, defaulting to 1
		return isset( $result->indicators->quote[0]->close[0] )
			? $result->indicators->quote[0]->close[0]
			: ( isset( $result->meta->previousClose ) ? $result->meta->previousClose : 1 );
	}


	public static function update_exchange_rate_currency( $list_currencies = array(), $woocommerce_currency = '', $is_auto_update = false ) {
		if ( ! empty( $woocommerce_currency ) && $list_currencies ) {
			foreach ( $list_currencies as $currency ) {
				if ( $currency->post_title !== $woocommerce_currency ) {
					if ( ! isset( $currency->ID ) ) {
						continue;
					}
					$rate_type = get_post_meta( $currency->ID, 'rate_type', true );
					if ( 'auto' === $rate_type || empty( $rate_type ) ) {
						$currency_object = array(
							'srcCurrency'  => $woocommerce_currency,
							'destCurrency' => $currency->post_title,
						);
						$exchange_rate   = self::get_exchange_rate_from_finance_api( $currency_object );
						if ( ! $exchange_rate ) {
							if ( ! $is_auto_update ) {
								update_post_meta( $currency->ID, 'rate', 'N/A' );
							}
							continue;
						}
						update_post_meta( $currency->ID, 'rate', $exchange_rate );
					}
				} else {
					update_post_meta( $currency->ID, 'rate', 1 );
				}
			}
		}
	}

	// Get exchange rate from finance api
	public static function get_exchange_rate_from_finance_api( $currency_object ) {
		$finance_api = isset( $currency_object['financeApi'] ) ? $currency_object['financeApi'] : get_option( 'yay_currency_finance_api', 'default' );

		switch ( $finance_api ) {
			case 'google':
				$exchange_rate = self::get_exchange_rate_from_google_api( $currency_object );
				break;
			case 'cuex':
				$exchange_rate = self::get_exchange_rate_from_cuex_api( $currency_object );
				break;
			case 'xe':
				$exchange_rate = self::get_exchange_rate_from_crawl_xe_api( $currency_object );
				break;
			case 'fawazcurrency':
				$exchange_rate = self::get_exchange_rate_from_fawazcurrency_api( $currency_object );
				break;
			default:
				$exchange_rate = self::get_exchange_rate_from_yahoo_api( $currency_object );
				break;

		}

		return $exchange_rate;
	}

	// YAHOO API - GET EXCHANGE RATE

	public static function get_exchange_rate_from_yahoo_api( $currency_object ) {
		$currency_params_template = array(
			'$src'  => $currency_object['srcCurrency'],
			'$dest' => $currency_object['destCurrency'],
		);

		$json_data = self::get_exchange_rates( $currency_params_template );

		if ( is_wp_error( $json_data ) || ! isset( $json_data['response']['code'] ) || 200 !== $json_data['response']['code'] ) {
			return false;
		}

		$decoded_json_data = json_decode( $json_data['body'] );

		if ( ! isset( $decoded_json_data->chart->result[0] ) ) {
			return false;
		}

		$exchange_rate = 1;

		if ( isset( $decoded_json_data->chart->result[0]->meta->regularMarketPrice ) ) {
			$exchange_rate = $decoded_json_data->chart->result[0]->meta->regularMarketPrice;
		} elseif ( isset( $decoded_json_data->chart->result[0]->indicators->quote[0]->close ) ) {
			$exchange_rate = $decoded_json_data->chart->result[0]->indicators->quote[0]->close[0];
		} else {
			$exchange_rate = $decoded_json_data->chart->result[0]->meta->previousClose;
		}

		return $exchange_rate;

	}

	// GOOGLE API - GET EXCHANGE RATE

	public static function get_exchange_rate_from_google_api( $currency_object ) {
		// Build URL for Google Finance currency pair
		$src_currency  = $currency_object['srcCurrency'];
		$dest_currency = $currency_object['destCurrency'];
		$url           = "https://www.google.com/finance/quote/{$src_currency}-{$dest_currency}";

		// Make request with browser user agent
		$request_args = array(
			'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36',
			'timeout'    => 10,
		);

		$response = wp_remote_get( $url, $request_args );

		// Check for valid response
		if ( is_wp_error( $response ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
			return false;

		}

		// Extract exchange rate from response HTML

		$html = wp_remote_retrieve_body( $response );

		if ( ! preg_match( '/<div class="YMlKec fxKbKc">([\d,]+\.?\d*)/', $html, $matches ) ) {
			return false;
		}

		if ( ! isset( $matches[1] ) ) {
			return false;
		}

		// Clean and return rate
		$exchange_rate = (float) str_replace( ',', '', $matches[1] );
		return $exchange_rate;
	}


	// CUEX API - GET EXCHANGE RATE

	public static function get_exchange_rate_from_cuex_api( $currency_object ) {
		try {
			// Prepare request parameters
			$src_currency  = strtolower( $currency_object['srcCurrency'] );
			$dest_currency = strtolower( $currency_object['destCurrency'] );
			$date          = gmdate( 'Y-m-d', current_time( 'timestamp' ) );

			// Build API URL
			$url = sprintf(
				'https://api.cuex.com/v1/exchanges/%s?to_currency=%s&from_date=%s&l=en',
				$src_currency,
				$dest_currency,
				$date
			);

			// Make API request
			$response = wp_remote_get(
				$url,
				array(
					'user-agent' => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/61.0.3163.100 Safari/537.36',
					'timeout'    => 10,
					'headers'    => array(
						'Authorization' => '7b3196edb611907ac9564c1f79356d5d',
					),
				)
			);

			// Validate response
			if ( is_wp_error( $response ) || wp_remote_retrieve_response_code( $response ) !== 200 ) {
				return false;
			}

			// Parse response data
			$body = json_decode( wp_remote_retrieve_body( $response ) );

			return isset( $body->data[0]->rate ) ? $body->data[0]->rate : false;

		} catch ( \Exception $e ) {
			return false;
		}
	}

	// FAWAZCURRENCY API - GET EXCHANGE RATE

	public static function get_exchange_rate_from_fawazcurrency_api( $currency_object ) {
		$src_currency  = strtolower( $currency_object['srcCurrency'] );
		$dest_currency = strtolower( $currency_object['destCurrency'] );
		$api_url       = "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies/{$src_currency}.json";

		try {
			$response = wp_remote_get(
				$api_url,
				array(
					'sslverify' => true,
					'timeout'   => 30,
					'headers'   => array(
						'Cache-Control' => 'no-cache',
					),
				)
			);

			if ( is_wp_error( $response ) || empty( wp_remote_retrieve_body( $response ) ) || 200 !== wp_remote_retrieve_response_code( $response ) ) {
				return false;
			}

			$data = json_decode( wp_remote_retrieve_body( $response ), true );

			if ( ! isset( $data[ $src_currency ] ) || ! isset( $data[ $src_currency ][ $dest_currency ] ) ) {
				return false;
			}

			return $data[ $src_currency ][ $dest_currency ];

		} catch ( \Exception $e ) {
			return false;
		}
	}

	// CRAWL XE API - GET EXCHANGE RATE [crawl]

	public static function get_exchange_rate_from_crawl_xe_api( $currency_object ) {

		$src_currency  = sanitize_text_field( $currency_object['srcCurrency'] );
		$dest_currency = sanitize_text_field( $currency_object['destCurrency'] );

		// Build URL with cache-busting parameter
		$url = sprintf(
			'https://www.xe.com/currencyconverter/convert/?Amount=1&From=%s&To=%s&t=%s',
			$src_currency,
			$dest_currency,
			time() // Avoid server cache
		);

		// Fetch HTML with enhanced headers to mimic browser
		$response = wp_remote_get(
			$url,
			[
				'user-agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
				'timeout'    => 30, // Increase timeout for potential JavaScript delay
				'headers'    => [
					'Accept'          => 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
					'Accept-Language' => 'en-US,en;q=0.5',
					'Cache-Control'   => 'no-cache',
					'Pragma'          => 'no-cache',
					'Connection'      => 'keep-alive',
				],
			]
		);

		// Check response
		if ( is_wp_error( $response ) || empty( wp_remote_retrieve_body( $response ) ) || wp_remote_retrieve_response_code( $response ) !== 200 ) {
			return false;
		}

		$html = wp_remote_retrieve_body( $response );
		return self::extract_exchange_rate_from_crawl_xe_api( $html, $dest_currency );

	}

	private static function extract_exchange_rate_from_crawl_xe_api( $html ) {
		libxml_use_internal_errors( true );
		$doc = new \DOMDocument();
		$doc->loadHTML( $html );
		$xpath = new \DOMXPath( $doc );

		// Query for primary rate (e.g., "1.00 USD = 1.3158... SGD")
		$nodes = $xpath->query( "//div[@data-testid='conversion']//p[contains(text(), '1.00') and contains(text(), '=')]/following-sibling::p" );

		if ( 0 === $nodes->length ) {
			return false;
		}

		// Combine text from <p> and <span class="faded-digits">
		$text = '';
		foreach ( $nodes->item( 0 )->childNodes as $child ) {
			$text .= $child->textContent;
		}

		// Extract rate (e.g., "1.3158...")
		if ( preg_match( '/(\d{1,3}(?:,\d{3})*(?:\.\d+)?)/', $text, $matches ) ) {
			$rate = floatval( str_replace( ',', '', $matches[1] ) );
			return $rate;
		}

		return false;
	}
}
