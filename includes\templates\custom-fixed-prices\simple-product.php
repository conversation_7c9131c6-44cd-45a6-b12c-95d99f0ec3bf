<?php
defined( 'ABSPATH' ) || exit;
use Yay_Currency\Helpers\Helper;
$yay_currencies      = Helper::get_currencies_post_type();
$product_id          = get_the_ID();
$custom_fixed_prices = get_post_meta( $product_id, 'yay_currency_custom_fixed_prices', true );
$fixed_prices_nonce  = wp_create_nonce( 'yay-custom-fixed-prices-nonce' );
?>
<div class="yay-currency-product-custom-fixed-prices-simple">
	<div class="yay-currency-fixed-price-checkbox-wrapper">
		<h3><?php esc_html_e( 'Fixed price for each currency', 'yay-currency' ); ?></h3>
	</div>
	<div class="yay-currency-fixed-prices-input-wrapper">
		<i class="checkbox-sub-text"><?php esc_html_e( 'You can manually set fixed price for each currency. Leave blank to get the rate automatically.', 'yay-currency' ); ?></i>
		<?php
		foreach ( $yay_currencies as $currency ) {
			if ( Helper::default_currency_code() === $currency->post_title ) {
				continue;
			}
			echo '<div class="yay-currency-fixed-prices-input">';
			woocommerce_wp_text_input(
				array(
					'id'          => "regular_price_{$currency->post_title}",
					'placeholder' => 'Auto',
					'label'       => 'Regular Price (' . $currency->post_title . ')',
					'desc_tip'    => 'true',
					'value'       => ! empty( $custom_fixed_prices[ $currency->post_title ] ) ? $custom_fixed_prices[ $currency->post_title ]['regular_price'] : '',
				)
			);
			woocommerce_wp_text_input(
				array(
					'id'          => "sale_price_{$currency->post_title}",
					'placeholder' => 'Auto',
					'label'       => 'Sale Price (' . $currency->post_title . ')',
					'desc_tip'    => 'true',
					'value'       => ! empty( $custom_fixed_prices[ $currency->post_title ] ) ? $custom_fixed_prices[ $currency->post_title ]['sale_price'] : '',
				)
			);
			echo '</div>';
		}
		?>
	</div>
	<input type="hidden" name="yay-custom-fixed-prices-nonce" value="<?php echo esc_attr( $fixed_prices_nonce ); ?>" />
</div>

<?php do_action( 'yay_currency_add_fixed_prices_simple_product', $yay_currencies, $product_id, $product_object ); ?>
