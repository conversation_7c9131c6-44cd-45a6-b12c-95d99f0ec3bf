#wpcontent {
  padding: 0 !important;
}

#wpbody-content {
  padding: 0;
  overflow-x: hidden !important;
  min-height: calc(100vh - 32px);
}
@media (min-width: 783px) {
  #wpbody-content {
    padding-left: 0;
  }
}
:root {
  --large-gap: 40px;
  --main-gap: 24px;
}
@media (max-width: 960px) {
  :root {
    --large-gap: 24px;
  }
}
@media (max-width: 782px) {
  :root {
    --large-gap: 16px;
    --main-gap: 16px;
  }
}
@keyframes loading-fade {
  0% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
  to {
    opacity: 0.7;
  }
}

.yaycommerce-license-page h2 {
  font-weight: 700;
  font-size: 18px;
  line-height: 20px;
}

.yaycommerce-license-page h3 {
  font-size: 14px;
  font-weight: 600;
  margin: 8px 0px;
}

.yaycommerce-license-page label {
  margin-bottom: 5px;
  display: inline-block;
}

.yaycommerce-license__top-bar {
  display: flex;
  align-items: center;
  background: #fff;
  gap: 1rem;
}

.yaycommerce-license__top-bar__icon {
  display: flex;
}

.yaycommerce-license-page main {
  margin-top: 1rem;
}

.yaycommerce-license-body {
  margin: 0 0 100px 0;
  padding: 0 20px 20px;
}

.yaycommerce-license-layout * {
  box-sizing: border-box;
}

.yaycommerce-license-layout {
  display: flex;
  gap: 1rem;
}

.yaycommerce-license-layout .yaycommerce-license-layout-main {
  max-width: 100%;
  flex: 1;
  margin-top: 10px;
}
.yaycommerce-license-settings {
  display: grid;
  gap: 1rem;
  grid-template-columns: 1fr 1fr 1fr;
  justify-content: center;
}

.yaycommerce-license-sidebar {
  width: 400px;
}
.yaycommerce-license-sidebar-sticky {
  margin-top: 10px;
  width: 400px;
  position: fixed;
  right: 20px;
  border-radius: 3px;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media screen and (max-width: 1800px) {
  .yaycommerce-license-settings {
    grid-template-columns: 1fr 1fr;
  }
  .yaycommerce-license-settings .yaycommerce-license-no-license:last-child {
    grid-column: 1 / span 2 !important;
  }
}
@media screen and (max-width: 1500px) {
  .yaycommerce-license-settings {
    grid-template-columns: 100%;
    max-width: 600px;
    margin: auto;
  }
  .yaycommerce-license-settings .yaycommerce-license-no-license:last-child {
    grid-column: auto !important;
  }
}
@media screen and (max-width: 1100px) {
  .yaycommerce-license-layout {
    flex-direction: column;
    align-items: center;
    width: 70%;
    margin: auto;
  }
  .yaycommerce-license-sidebar {
    width: 100%;
  }
  .yaycommerce-license-sidebar-sticky {
    position: relative;
    top: 0;
    right: 0;
    width: 100%;
  }
  .yaycommerce-license-layout-main {
    order: 2;
    width: 100%;
    margin-top: 0 !important;
  }
  .yaycommerce-license-settings {
    grid-template-columns: 100%;
    max-width: none;
    width: 100%;
  }
}
@media screen and (max-width: 700px) {
  .yaycommerce-license-layout {
    width: 100%;
  }
}
.yaycommerce-license-card {
  background: #ffffff;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.yaycommerce-license-card-header {
  padding: 1rem;
  border-bottom: 1px solid #e2e4e7;
  background: #fafafa;
  display: flex;
  align-items: center;
}
.yaycommerce-license-sidebar-sticky .yaycommerce-license-card-header {
  background: #fff;
}
.yaycommerce-license-card-header h3 {
  margin: 0;
  line-height: 1.6;
}
.yaycommerce-license-card-description {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  margin-top: 4px;
}
.yaycommerce-license-card .yaycommerce-license-card-body {
  padding: 8px 24px 12px;
}

.yaycommerce-license-settings .yaycommerce-license-card-body {
  padding: 1rem;
}

.yaycommerce-license-input-row {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 10px;
}

.yaycommerce-license-input-row input {
  flex: 1;
}

.yaycommerce-license-input-row input:disabled {
  background: #fbfbfb;
}

@media screen and (max-width: 782px) {
  .yaycommerce-license-input-row input {
    min-height: 30px;
    padding: 0 8px;
    font-size: 14px;
  }
}

.yaycommerce-license-card-footer {
  padding: 10px 1rem;
  border-top: 1px solid #d9d9d9;
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: auto;
}

.yaycommerce-license-card-footer:not(:has(div)) {
  display: none;
}

/* CSS Notification */
.yaycommerce-license-notification {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen-Sans,
    Ubuntu, Cantarell, Helvetica Neue, sans-serif;
  font-size: 13px;
  background-color: #32373c;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  color: #fff;
  padding: 16px 24px;
  width: fit-content;
  max-width: 600px;
  box-sizing: border-box;
  cursor: pointer;
  animation: NslideUp 0.5s both linear;
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 20;
}

.yaycommerce-license-buy-now {
  font-weight: 600;
}

.yaycommerce-license-expired-text,
.yaycommerce-license-expired-text:hover {
  color: rgb(247, 40, 40);
}

p.yaycommerce_expired_text::before {
  content: "\f158";
}

.yaycommerce-activate-license-button {
  display: flex !important;
  align-items: center;
}

.activate-loading {
  display: none;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  align-items: center;
  cursor: pointer;
  font-size: 21px;
  justify-content: center;
  padding: 2px;
}

.activate-loading.sync-loading {
  -webkit-animation: loading-rotate 1.5s linear infinite forwards;
  animation: loading-rotate 1.5s linear infinite forwards;
}

@keyframes loading-rotate {
  50% {
    rotate: 180deg;
  }
  100% {
    rotate: 360deg;
  }
}

.activate-loading svg {
  fill: currentColor;
  height: 1rem;
  width: 1rem;
}

.yaycommerce-license-message {
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease-in-out;
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ff4d4f;
}

.yaycommerce-license-message a {
  color: #ff4d4f;
}

.yaycommerce-license-message__close {
  display: flex;
  cursor: pointer;
  margin-left: auto;
}

.yaycommerce-license-message.show {
  opacity: 1;
  visibility: visible;
}

.yaycommerce-license-badge {
  padding: 3px 8px;
  border-radius: 30px;
  background: rgb(176, 176, 176);
  font-size: 12px;
  color: #fff;
}
.yaycommerce-license-badge.error {
  background: #d63638;
}
.yaycommerce-license-badge.success {
  background: #52c41a;
}
.yaycommerce-license-button {
  height: 30px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 15px;
  gap: 8px;
  background: #ffffff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  color: #414141;
}
.yaycommerce-license-button:hover {
  box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.063);
}
.yaycommerce-remove-license:hover {
  box-shadow: 0px 2px 0px rgba(177, 0, 0, 0.223);
}
.yaycommerce-remove-license {
  border-color: #d63638 !important;
  color: #d63638 !important;
}
.yaycommerce-license-settings .yaycommerce-license-no-license {
  display: none;
}
.yaycommerce-license-settings .yaycommerce-license-no-license:last-child {
  display: block !important;
  grid-column: 1 / span 3;
}

.yaycommerce-license__task-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 10px;
}
.yaycommerce-license__task-list li > span:first-child {
  display: flex;
}
.yaycommerce-license__task-list li > span:first-child svg {
  width: 20px;
  height: 20px;
}

.yaycommerce-license__important-notice {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border: 1px solid #c2ddff;
  background-color: #e8f2ff;
  color: #295b99;
  border-radius: 6px;
}

.yaycommerce-license__important-notice span:first-child {
  display: flex;
}

.yaycommerce-license__important-notice svg path:nth-child(odd) {
  fill: #295b99 !important;
}

.message {
  position: fixed;
  top: 0;
  left: 50%;
  z-index: 50;
  display: flex;
  align-items: center;
  gap: 8px;
  overflow: hidden;
  padding: 9px 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: all;
  transform: translate(-50%, -100%);
  transition: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.35);
}

.message.active {
  transform: translate(-50%, 35px);
}

.message svg {
  max-width: 20px;
}

.message--success {
  color: #52c41a;
}
.message--error {
  color: #ff4d4f;
}

.message__text {
  margin: 0;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
}
