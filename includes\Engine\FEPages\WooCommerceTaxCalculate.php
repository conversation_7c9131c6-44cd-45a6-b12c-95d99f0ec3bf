<?php

namespace Yay_Currency\Engine\FEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\TaxHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

class WooCommerceTaxCalculate {
	use SingletonTrait;

	private $apply_currency = array();

	private $currencies_data = array();
	private $tax_display_shop;
	private $tax_display_cart;

	public function __construct() {

		if ( ! wc_tax_enabled() ) {
			return;
		}

		add_action( 'init', array( $this, 'recalculate_all_with_taxes' ) );

	}

	public function recalculate_all_with_taxes() {
		if ( ! is_admin() ) {

			$this->tax_display_shop = get_option( 'woocommerce_tax_display_shop' );
			$this->tax_display_cart = get_option( 'woocommerce_tax_display_cart' );

			if ( ! wc_prices_include_tax() && 'excl' === $this->tax_display_shop && 'excl' === $this->tax_display_cart ) {
				return;
			}

			$this->apply_currency          = YayCurrencyHelper::detect_current_currency();
			$is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $this->apply_currency );

			if ( $is_dis_checkout_diff_currency ) {

				add_filter( 'yay_currency_get_rate_percent_in_cart', array( $this, 'get_tax_rate_percent_in_cart' ), 10, 1 );
				add_filter( 'yay_currency_is_more_one_tax_apply_in_cart', array( $this, 'is_more_one_tax_apply_in_cart' ), 10, 1 );

				if ( 'incl' === $this->tax_display_cart ) {
					add_filter( 'yay_currency_enable_label_incl_tax', array( $this, 'enable_label_incl_tax' ), 10, 1 );
					add_filter( 'yay_currency_incl_tax_enable', '__return_true' );
					add_filter( 'yay_currency_shipping_total_incl_tax', array( $this, 'recalculate_shipping_total_incl_tax' ), 10, 2 );
					add_filter( 'yay_currency_recalculate_shipping_fee_incl_tax', array( $this, 'recalculate_shipping_fee_incl_tax' ), 10, 3 );
					add_filter( 'yay_currency_recalculate_fee_incl_tax', array( $this, 'recalculate_fee_incl_tax' ), 10, 2 );
					add_filter( 'yay_currency_recalculate_total_tax', array( $this, 'recalculate_total_tax_incl' ), 10, 2 );
					add_filter( 'yay_currency_recalculate_total_tax_by_rate_id', array( $this, 'recalculate_total_tax_incl_by_rate_id' ), 10, 3 );

					if ( ! wc_prices_include_tax() ) {
						add_filter( 'yay_currency_get_product_price_default_currency', array( $this, 'get_product_price_incl_tax' ), 10, 3 );
						add_filter( 'yay_currency_get_product_price_apply_currency', array( $this, 'get_product_price_incl_tax' ), 10, 3 );
					}
				} elseif ( wc_prices_include_tax() ) {
						add_filter( 'yay_currency_get_product_price_default_currency', array( $this, 'get_product_price_excl_tax' ), 10, 3 );
						add_filter( 'yay_currency_get_product_price_apply_currency', array( $this, 'get_product_price_excl_tax' ), 10, 3 );
						add_filter( 'yay_currency_recalculate_total_tax', array( $this, 'recalculate_total_tax_excl' ), 10, 2 );
						add_filter( 'yay_currency_recalculate_total_tax_by_rate_id', array( $this, 'recalculate_total_tax_excl_by_rate_id' ), 10, 3 );
						add_filter( 'yay_currency_recalculate_excl_total_taxes_in_cart', array( $this, 'recalculate_excl_total_taxes_in_cart' ), 10, 2 );

						add_filter( 'yay_currency_excl_tax_enable', '__return_true' );
						add_filter( 'yay_currency_enable_label_excl_tax', array( $this, 'enable_label_excl_tax' ), 10, 1 );
				}
			}
		}

	}

	public function recalculate_shipping_fee_incl_tax( $shipping_cost, $method, $apply_currency ) {
		if ( count( $method->get_taxes() ) > 0 ) {
			$calculate_tax = 0;
			foreach ( $method->get_taxes() as $tax ) {
				$tax_rate       = \WC_Tax::calc_shipping_tax( $shipping_cost, \WC_Tax::get_shipping_tax_rates() );
				$tax_currency   = $apply_currency ? YayCurrencyHelper::calculate_price_by_currency( $tax, true, $apply_currency ) : $tax;
				$tax_rate       = is_array( $tax_rate ) ? array_sum( $tax_rate ) : $tax_currency;
				$calculate_tax += $tax_rate;

			}
			$shipping_cost = $shipping_cost + $calculate_tax;
		}
		return $shipping_cost;
	}

	public function recalculate_fee_incl_tax( $fee_cost, $fee ) {
		if ( $fee->taxable ) {
			$fee_tax  = \WC_Tax::calc_tax( $fee_cost, \WC_Tax::get_rates( $fee->tax_class, WC()->cart->get_customer() ), false );
			$fee_cost = $fee_cost + array_sum( $fee_tax );
		}
		return $fee_cost;
	}

	public function get_tax_rate_percent_in_cart( $tax_rate_percent ) {
		$cart_taxes = array_keys( WC()->cart->get_taxes() );
		if ( ! $cart_taxes ) {
			return $tax_rate_percent;
		}
		$rateId           = array_shift( $cart_taxes );
		$tax_rate_percent = \WC_Tax::get_rate_percent_value( $rateId );
		$tax_rate_percent = \WC_Tax::get_rate_percent_value( $rateId ) / 100;
		return $tax_rate_percent;
	}

	public function is_more_one_tax_apply_in_cart( $flag = false ) {
		$flag = TaxHelper::is_apply_multiple_taxes( $flag );
		return $flag;
	}

	public function recalculate_total_tax_incl( $tax_total, $apply_currency ) {

		$tax_percent = apply_filters( 'yay_currency_get_rate_percent_in_cart', false );

		if ( $tax_percent ) {
			$total_coupon_applies = apply_filters( 'yay_currency_get_discount_total', 0, $apply_currency );
			$shipping_total_tax   = apply_filters( 'yay_currency_get_shipping_total', 0, $apply_currency, false ) / ( 1 + $tax_percent );
			$cart_subtotal        = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency ) - $total_coupon_applies;
			$cart_subtotal_tax    = $cart_subtotal / ( 1 + $tax_percent );
			$total_fees_tax       = ( SupportHelper::get_total_fees( $apply_currency, false ) ) / ( 1 + $tax_percent );
			$tax_total            = ( $cart_subtotal_tax + $shipping_total_tax + $total_fees_tax ) * $tax_percent;
		}

		return $tax_total;
	}

	public function recalculate_total_tax_incl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency ) {
		$tax_percent = \WC_Tax::get_rate_percent_value( $tax_rate_id );
		if ( $tax_percent ) {
			$tax_percent = $tax_percent / 100;
			$taxes_info  = TaxHelper::get_taxes_in_cart( $apply_currency, WC()->cart->get_cart_contents() );

			if ( isset( $taxes_info['taxes'] ) && isset( $taxes_info['taxes'][ $tax_rate_id ] ) ) {
				$shipping_total_tax = 0;
				if ( WC()->cart->get_shipping_taxes() ) {
					$shipping_taxes = array_keys( WC()->cart->get_shipping_taxes() );
					if ( in_array( $tax_rate_id, $shipping_taxes, true ) ) {
						$shipping_total_tax = apply_filters( 'yay_currency_get_shipping_total', 0, $apply_currency, false ) / ( 1 + $tax_percent );
					}
				}
				$total_fees_tax = 0;
				if ( WC()->cart->get_fee_taxes() ) {
					$shipping_taxes = array_keys( WC()->cart->get_fee_taxes() );
					if ( in_array( $tax_rate_id, $shipping_taxes, true ) ) {
						$total_fees_tax = ( SupportHelper::get_total_fees( $apply_currency, false ) ) / ( 1 + $tax_percent );
					}
				}

				$total_coupon_applies = SupportHelper::get_total_coupons( $taxes_info['taxes'][ $tax_rate_id ]['subtotal'], $apply_currency );

				$cart_subtotal     = $taxes_info['taxes'][ $tax_rate_id ]['subtotal'] - $total_coupon_applies;
				$cart_subtotal_tax = $cart_subtotal / ( 1 + $tax_percent );

				$tax_total = ( $cart_subtotal_tax + $shipping_total_tax + $total_fees_tax ) * $tax_percent;
			}
		}
		return $tax_total;
	}

	public function recalculate_total_tax_excl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency ) {
		$tax_total = TaxHelper::recalculate_total_tax_excl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency );
		return $tax_total;
	}

	public function recalculate_excl_total_taxes_in_cart( $tax_total, $apply_currency ) {
		$cart_taxes = array_keys( WC()->cart->get_taxes() );
		if ( $cart_taxes && count( $cart_taxes ) > 1 ) {
			$tax_total = 0;
			foreach ( $cart_taxes as $tax_rate_id ) {
				$tax_total += $this->recalculate_total_tax_excl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency );
			}
		}
		return $tax_total;
	}

	public function recalculate_total_tax_excl( $tax_total, $apply_currency ) {

		$tax_percent = apply_filters( 'yay_currency_get_rate_percent_in_cart', false );

		if ( $tax_percent ) {
			$total_coupon_applies = apply_filters( 'yay_currency_get_discount_total', 0, $apply_currency );
			$shipping_total_tax   = apply_filters( 'yay_currency_get_shipping_total', 0, $apply_currency, false );
			$cart_subtotal_tax    = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency ) - $total_coupon_applies;
			$total_fees_tax       = SupportHelper::get_total_fees( $apply_currency, false );
			$tax_total            = ( $cart_subtotal_tax + $shipping_total_tax + $total_fees_tax ) * $tax_percent;
		}

		return $tax_total;
	}

	public function get_product_price_incl_tax( $product_price, $product, $qty = 1 ) {

		$product_price = wc_get_price_including_tax(
			$product,
			array(
				'qty'   => $qty,
				'price' => $product_price,
			)
		);
		return $product_price;
	}


	public function get_product_price_excl_tax( $product_price, $product, $qty = 1 ) {

		$product_price = wc_get_price_excluding_tax(
			$product,
			array(
				'qty'   => $qty,
				'price' => $product_price,
			)
		);
		return $product_price;
	}

	public function enable_label_incl_tax( $flag ) {

		if ( ! wc_prices_include_tax() ) {
			$flag = true;
		}
		return $flag;
	}

	public function enable_label_excl_tax( $flag ) {

		if ( wc_prices_include_tax() ) {
			$flag = true;
		}

		return $flag;
	}

	public function recalculate_shipping_total_incl_tax( $shipping_total, $apply_currency ) {
		$shipping_tax_rates = \WC_Tax::get_shipping_tax_rates();
		if ( ! $shipping_tax_rates ) {
			return $shipping_total;
		}
		$tax_rate        = \WC_Tax::calc_shipping_tax( $shipping_total, $shipping_tax_rates );
		$shipping_tax    = is_array( $tax_rate ) ? array_sum( $tax_rate ) : 0;
		$shipping_total += $shipping_tax;
		return $shipping_total;
	}
}
