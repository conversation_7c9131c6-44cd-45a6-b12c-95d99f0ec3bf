<?php
namespace Yay_Currency\Engine\BEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\FixedPriceHelper;

defined( 'ABSPATH' ) || exit;

class FixedPricesPerProduct {
	use SingletonTrait;

	private $currencies = null;

	protected function __construct() {

		if ( FixedPriceHelper::is_set_fixed_price() ) {

			add_action( 'woocommerce_product_options_general_product_data', array( $this, 'custom_fixed_prices_input_single_product' ) );
			add_action( 'woocommerce_process_product_meta', array( $this, 'save_custom_fixed_prices_input_single_product' ) );

			add_action( 'woocommerce_variation_options_pricing', array( $this, 'custom_fixed_prices_input_variable_product' ), 10, 3 );
			add_action( 'woocommerce_save_product_variation', array( $this, 'save_custom_fixed_prices_input_variable_product' ), 10, 2 );

		}

	}

	public function fixed_prices_display_on_product_type() {
		$product_types = array( 'simple', 'subscription', 'subscription_variation', 'external', 'bundle' );
		// LearnDash LMS plugin
		if ( defined( 'LEARNDASH_VERSION' ) ) {
			array_push( $product_types, 'course' );
		}
		// WPC Product Bundles for WooCommerce (Premium) plugin
		if ( defined( 'WOOSB_VERSION' ) ) {
			array_push( $product_types, 'woosb' );
		}
		// WPC Composite Products for WooCommerce plugin
		if ( defined( 'WOOCO_VERSION' ) ) {
			array_push( $product_types, 'composite' );
		}
		return apply_filters( 'yay_currency_fixed_prices_on_product_type', $product_types );
	}

	public function custom_fixed_prices_input_single_product() {
		global $product_object;
		$product_types = $this->fixed_prices_display_on_product_type();
		if ( in_array( $product_object->get_type(), $product_types ) ) {
			require YAY_CURRENCY_PLUGIN_DIR . 'includes/templates/custom-fixed-prices/simple-product.php';
		}
	}

	public function save_custom_fixed_prices_input_single_product( $post_id ) {
		$yay_currencies      = Helper::get_currencies_post_type();
		$custom_fixed_prices = $this->save_custom_fixed_prices_product( $yay_currencies );

		if ( ! $custom_fixed_prices || ! ( isset( $_POST['woocommerce_meta_nonce'], $_POST['acme_text_id'] ) || wp_verify_nonce( sanitize_key( $_POST['woocommerce_meta_nonce'] ), 'woocommerce_save_data' ) ) ) {
			return false;
		}

		update_post_meta( $post_id, 'yay_currency_custom_fixed_prices', $custom_fixed_prices );

		$product_type = empty( $_POST['product-type'] ) ? \WC_Product_Factory::get_product_type( $post_id ) : sanitize_title( wp_unslash( $_POST['product-type'] ) );
		do_action( 'yay_currency_custom_save_fixed_prices_single_product', $yay_currencies, $post_id, $product_type );

	}

	public function custom_fixed_prices_input_variable_product( $index, $variation_data, $variation ) {
		require YAY_CURRENCY_PLUGIN_DIR . 'includes/templates/custom-fixed-prices/variable-product.php';
	}

	public function save_custom_fixed_prices_input_variable_product( $variation_id, $index ) {
		check_ajax_referer( 'save-variations', 'security' );
		$yay_currencies      = Helper::get_currencies_post_type();
		$custom_fixed_prices = $this->save_custom_fixed_prices_product( $yay_currencies, $index );
		if ( ! $custom_fixed_prices ) {
			return false;
		}
		update_post_meta( $variation_id, 'yay_currency_custom_fixed_prices', $custom_fixed_prices );

		do_action( 'yay_currency_custom_save_fixed_prices_variable_product', $yay_currencies, $variation_id, $index );
	}

	public function save_custom_fixed_prices_product( $currencies = array(), $variation_idx = null ) {
		$nonce = isset( $_POST['yay-custom-fixed-prices-nonce'] ) ? sanitize_text_field( $_POST['yay-custom-fixed-prices-nonce'] ) : false;
		if ( ! $nonce && ! wp_verify_nonce( $nonce, 'yay-custom-fixed-prices-nonce' ) ) {
			return false;
		}
		$custom_fixed_prices = array();

		foreach ( $currencies as $currency ) {

			if ( Helper::default_currency_code() === $currency->post_title ) {
				continue;
			}

			$fixed_prices_meta = ! is_null( $variation_idx ) ? $currency->post_title . '_' . $variation_idx : $currency->post_title;

			if ( isset( $_POST[ 'regular_price_' . $fixed_prices_meta ] ) ) {
				$regular_price = sanitize_text_field( $_POST[ 'regular_price_' . $fixed_prices_meta ] );
			}

			if ( isset( $_POST[ 'sale_price_' . $fixed_prices_meta ] ) ) {
				$sale_price = sanitize_text_field( $_POST[ 'sale_price_' . $fixed_prices_meta ] );
			}

			$custom_fixed_prices[ $currency->post_title ] = array(
				'regular_price' => $regular_price,
				'sale_price'    => $sale_price,
				'price'         => $sale_price ? $sale_price : $regular_price,
			);
		}

		return $custom_fixed_prices;

	}
}
