<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class TranslateHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function get_translations() {
		return array(
			'Manager Currency'                           => __( 'Manager Currency', 'yay-currency' ),
			'Checkout in fallback currency'              => __( 'Checkout in fallback currency', 'yay-currency' ),
			'Type to search currency...'                 => __( 'Type to search currency...', 'yay-currency' ),
			'This currency is used as a fallback where the default currency can’t be displayed on checkout page.' => __( 'This currency is used as a fallback where the default currency can’t be displayed on checkout page.', 'yay-currency' ),
			'The fallback currency can not be disabled.' => __( 'The fallback currency can not be disabled.', 'yay-currency' ),
			'Checkout in different currency'             => __( 'Checkout in different currency', 'yay-currency' ),
			'This sets the ability to checkout in a variety of currencies' => __( 'This sets the ability to checkout in a variety of currencies', 'yay-currency' ),
			'Status'                                     => __( 'Status', 'yay-currency' ),
			'This sets the currency is applied in checkout' => __( 'This sets the currency is applied in checkout', 'yay-currency' ),
			'Currency'                                   => __( 'Currency', 'yay-currency' ),
			'Payment Methods'                            => __( 'Payment Methods', 'yay-currency' ),
			'Are you sure to disable the default currency at checkout?' => __( 'Are you sure to disable the default currency at checkout?', 'yay-currency' ),
			'You would need to select a fallback currency.' => __( 'You would need to select a fallback currency.', 'yay-currency' ),
			'All payment methods'                        => __( 'All payment methods', 'yay-currency' ),
			'Currency symbol position'                   => __( 'Currency symbol position', 'yay-currency' ),
			'Currency code position'                     => __( 'Currency code position', 'yay-currency' ),
			'Thousand separator'                         => __( 'Thousand separator', 'yay-currency' ),
			'Decimal separator'                          => __( 'Decimal separator', 'yay-currency' ),
			'Number of decimals'                         => __( 'Number of decimals', 'yay-currency' ),
			'Rounding'                                   => __( 'Rounding', 'yay-currency' ),
			'Custom rounding of converted prices'        => __( 'Custom rounding of converted prices', 'yay-currency' ),
			'To'                                         => __( 'To', 'yay-currency' ),
			'Round the converted price to achieve the target ending. Eg: 365.36 becomes 370 when rounding up to the nearest 10' => __( 'Round the converted price to achieve the target ending. Eg: 365.36 becomes 370 when rounding up to the nearest 10', 'yay-currency' ),
			'Minus'                                      => __( 'Minus', 'yay-currency' ),
			'Deduct this amount from the above rounded price. Eg: 370 becomes 369.99 after deducting 0.01' => __( 'Deduct this amount from the above rounded price. Eg: 370 becomes 369.99 after deducting 0.01', 'yay-currency' ),
			'Enter test amount'                          => __( 'Enter test amount', 'yay-currency' ),
			'Result'                                     => __( 'Result', 'yay-currency' ),
			'Left'                                       => __( 'Left', 'yay-currency' ),
			'Right'                                      => __( 'Right', 'yay-currency' ),
			'Left with space'                            => __( 'Left with space', 'yay-currency' ),
			'Right with space'                           => __( 'Right with space', 'yay-currency' ),
			'Not display'                                => __( 'Not display', 'yay-currency' ),
			'Disabled'                                   => __( 'Disabled', 'yay-currency' ),
			'Up'                                         => __( 'Up', 'yay-currency' ),
			'Down'                                       => __( 'Down', 'yay-currency' ),
			'Nearest'                                    => __( 'Nearest', 'yay-currency' ),
			'Your default currency is '                  => __( 'Your default currency is ', 'yay-currency' ),
			'Change'                                     => __( 'Change', 'yay-currency' ),
			'Add New Currency'                           => __( 'Add New Currency', 'yay-currency' ),
			'Live exchange rate unavailable. Please update manually for this currency.' => __( 'Live exchange rate unavailable. Please update manually for this currency.', 'yay-currency' ),
			'Troubleshoot'                               => __( 'Troubleshoot', 'yay-currency' ),
			'Config currency\'s format'                  => __( 'Config currency\'s format', 'yay-currency' ),
			'Update currency\'s rate'                    => __( 'Update currency\'s rate', 'yay-currency' ),
			'Delete currency'                            => __( 'Delete currency', 'yay-currency' ),
			'Fixed'                                      => __( 'Fixed', 'yay-currency' ),
			'Auto'                                       => __( 'Auto', 'yay-currency' ),
			'Preview'                                    => __( 'Preview', 'yay-currency' ),
			'Rate'                                       => __( 'Rate', 'yay-currency' ),
			'Fee'                                        => __( 'Fee', 'yay-currency' ),
			'Action'                                     => __( 'Action', 'yay-currency' ),
			'This controls what currency prices are listed at in the catalog and which currency gateways will take payments in.' => __( 'This controls what currency prices are listed at in the catalog and which currency gateways will take payments in.', 'yay-currency' ),
			'This show sample amount'                    => __( 'This show sample amount', 'yay-currency' ),
			'This sets the exchange rate of currency based on default currency' => __( 'This sets the exchange rate of currency based on default currency', 'yay-currency' ),
			'This sets the extra money to compensate the difference of currency' => __( 'This sets the extra money to compensate the difference of currency', 'yay-currency' ),
			'Are you sure you want to delete this currency?' => __( 'Are you sure you want to delete this currency?', 'yay-currency' ),
			'Configure'                                  => __( 'Configure', 'yay-currency' ),
			'currency'                                   => __( 'currency', 'yay-currency' ),
			'Switcher Location'                          => __( 'Switcher Location', 'yay-currency' ),
			'Choose where to display the currency switcher.' => __( 'Choose where to display the currency switcher.', 'yay-currency' ),
			'Before short description'                   => __( 'Before short description', 'yay-currency' ),
			'After short description'                    => __( 'After short description', 'yay-currency' ),
			'Shortcode'                                  => __( 'Shortcode', 'yay-currency' ),
			'Copy this shortcode to display currency switcher anywhere you want.' => __( 'Copy this shortcode to display currency switcher anywhere you want.', 'yay-currency' ),
			'Switcher Customizer'                        => __( 'Switcher Customizer', 'yay-currency' ),
			'Customize the currency switcher used at the above positions.' => __( 'Customize the currency switcher used at the above positions.', 'yay-currency' ),
			'Switcher size'                              => __( 'Switcher size', 'yay-currency' ),
			'Small'                                      => __( 'Small', 'yay-currency' ),
			'Medium'                                     => __( 'Medium', 'yay-currency' ),
			'Symbol'                                     => __( 'Symbol', 'yay-currency' ),
			'Code'                                       => __( 'Code', 'yay-currency' ),
			'Show on Single Product Page'                => __( 'Show on Single Product Page', 'yay-currency' ),
			'Show flag'                                  => __( 'Show flag', 'yay-currency' ),
			'Show currency name'                         => __( 'Show currency name', 'yay-currency' ),
			'Show currency symbol'                       => __( 'Show currency symbol', 'yay-currency' ),
			'Show currency code'                         => __( 'Show currency code', 'yay-currency' ),
			'Manage Currency'                            => __( 'Manage Currency', 'yay-currency' ),
			'Checkout Options'                           => __( 'Checkout Options', 'yay-currency' ),
			'Display Options'                            => __( 'Display Options', 'yay-currency' ),
			'Advanced Settings'                          => __( 'Advanced Settings', 'yay-currency' ),
			'Save Changes'                               => __( 'Save Changes', 'yay-currency' ),
			'Exchange rate is required!'                 => __( 'Exchange rate is required!', 'yay-currency' ),
			'Fix it now'                                 => __( 'Fix it now', 'yay-currency' ),
			'Settings saved!'                            => __( 'Settings saved!', 'yay-currency' ),
			'Exchange rate updated!'                     => __( 'Exchange rate updated!', 'yay-currency' ),
			'Successfully deleted!'                      => __( 'Successfully deleted!', 'yay-currency' ),
			'Unable to update exchange rate.'            => __( 'Unable to update exchange rate.', 'yay-currency' ),
			'Oops! Something went wrong!'                => __( 'Oops! Something went wrong!', 'yay-currency' ),
			'Contact us'                                 => __( 'Contact us', 'yay-currency' ),
			'Display current currency notice on product page' => __( 'Display current currency notice on product page', 'yay-currency' ),
			'You can include these shortcodes'           => __( 'You can include these shortcodes', 'yay-currency' ),
			'Fixed product price for each currency'      => __( 'Fixed product price for each currency', 'yay-currency' ),
			'By enabling this option, you can go to <b>Products</b> settings to set up fixed product prices based on currency.' => __( 'By enabling this option, you can go to <b>Products</b> settings to set up fixed product prices based on currency.', 'yay-currency' ),
			'Update exchange rate automatically'         => __( 'Update exchange rate automatically', 'yay-currency' ),
			'This sets the interval of update exchange rate automation.' => __( 'This sets the interval of update exchange rate automation.', 'yay-currency' ),
			'Auto select currency by countries'          => __( 'Auto select currency by countries', 'yay-currency' ),
			'This sets the display currency depends on the customer\'s country.' => __( 'This sets the display currency depends on the customer\'s country.', 'yay-currency' ),
			'WPML Compatible'                            => __( 'WPML Compatible', 'yay-currency' ),
			'Polylang Compatible'                        => __( 'Polylang Compatible', 'yay-currency' ),
			'Minute(s)'                                  => __( 'Minute(s)', 'yay-currency' ),
			'Hour(s)'                                    => __( 'Hour(s)', 'yay-currency' ),
			'Day(s)'                                     => __( 'Day(s)', 'yay-currency' ),
			'Countries'                                  => __( 'Countries', 'yay-currency' ),
			'Language'                                   => __( 'Language', 'yay-currency' ),
			'Default (Auto detect)'                      => __( 'Default (Auto detect)', 'yay-currency' ),
			'Update all currencies\'s rate'              => __( 'Update all currencies\'s rate', 'yay-currency' ),
			'Copied!'                                    => __( 'Copied!', 'yay-currency' ),
			'Click to copy'                              => __( 'Click to copy', 'yay-currency' ),
		);
	}
}
