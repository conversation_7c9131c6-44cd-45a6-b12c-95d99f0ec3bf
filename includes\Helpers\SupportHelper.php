<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class SupportHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function detect_php_version() {
		$version = phpversion();
		return $version;
	}

	public static function cart_item_maybe_prefix_key( $key, $prefix = '_' ) {
		return ( substr( $key, 0, strlen( $prefix ) ) !== $prefix ) ? $prefix . $key : $key;
	}

	public static function set_cart_item_objects_property( &$data, $key, $value ) {
		if ( self::detect_php_version() < 8.2 ) {
			$data->$key = $value;
		} else {
			$meta_key = self::cart_item_maybe_prefix_key( $key );
			$data->update_meta_data( $meta_key, $value, '' );
		}

	}

	public static function get_cart_item_objects_property( $data, $property ) {

		if ( ! is_object( $data ) ) {
			return false;
		}

		if ( self::detect_php_version() < 8.2 ) {
			return isset( $data->$property ) ? $data->$property : false;
		} else {
			$prefixed_key = self::cart_item_maybe_prefix_key( $property );
			$value        = $data->get_meta( $prefixed_key, true );
			return ! empty( $value ) ? $value : false;
		}

	}

	public static function get_price_options_by_3rd_plugin( $product ) {
		$price_options = apply_filters( 'yay_currency_price_options', 0, $product );
		return $price_options;
	}

	public static function get_price_options_default_by_3rd_plugin( $product ) {
		$price_options = apply_filters( 'yay_currency_price_options_default', 0, $product );
		return $price_options;
	}

	public static function get_product_subtotal_fixed_by_3rd_plugin( $fixed_product_price, $product, $quantity, $apply_currency ) {
		$fixed_product_price          = apply_filters( 'yay_currency_get_fixed_product_price_3rd_plugin', $fixed_product_price, $product, $apply_currency );
		$fixed_product_subtotal_price = $fixed_product_price * $quantity;
		return apply_filters( 'yay_currency_get_fixed_product_subtotal_price_3rd_plugin', $fixed_product_subtotal_price, $product, $apply_currency );
	}

	public static function get_product_price( $product_id, $apply_currency = array() ) {
		$product_obj   = wc_get_product( $product_id );
		$product_price = $product_obj->get_price( 'edit' );
		if ( $apply_currency ) {
			$product_price = YayCurrencyHelper::calculate_price_by_currency( $product_price, false, $apply_currency );
			$product_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $product_obj, $product_price, $apply_currency );
		}
		return $product_price;
	}

	public static function woo_discount_rules_active() {
		return apply_filters( 'yay_currency_active_woo_discount_rules', false );
	}

	// Get product price by Woo Discount Rules plugin
	public static function get_product_fixed_price_by_discount_type( $price, $product, $apply_currency ) {
		//Support a few plugin like Extra, Add-ons
		$discount_type = isset( $product->awdr_product_get_discount_type ) ? $product->awdr_product_get_discount_type : false;
		if ( $discount_type ) {
			$discount_value = $product->awdr_product_get_discount_value;
			$quantity       = $product->awdr_product_get_quantity;
			switch ( $discount_type ) {
				case 'fixed_price':
					$price = YayCurrencyHelper::calculate_price_by_currency( $discount_value, true, $apply_currency ) * $quantity;
					break;
				case 'flat':
					$price = ( $price - YayCurrencyHelper::calculate_price_by_currency( $discount_value, true, $apply_currency ) ) * $quantity;
					break;
				default:
					$price = ( $price - ( $discount_value / 100 ) * floatval( $price ) ) * $quantity;
					break;
			}
		}

		return $price;
	}

	// SUPPORT FOR Woo Discount Rules PRO
	public static function calculate_discount_from() {
		$calculate_discount_from = 'sale_price';
		if ( self::woo_discount_rules_active() && class_exists( '\Wdr\App\Controllers\DiscountCalculator' ) ) {
			$calculate_discount_from = \Wdr\App\Controllers\DiscountCalculator::$config->getConfig( 'calculate_discount_from', 'sale_price' );
		}
		return $calculate_discount_from;
	}

	public static function get_original_price_apply_discount_pro( $product_id ) {
		$calculate_discount_from = self::calculate_discount_from();
		if ( 'sale_price' === $calculate_discount_from ) {
			$original_price = (float) get_post_meta( $product_id, '_sale_price', true );
		} else {
			$original_price = (float) get_post_meta( $product_id, '_regular_price', true );
		}
		return (float) $original_price;
	}

	public static function product_fixed_price_apply_discount_pro_by_currency( $price_fixed, $product, $apply_currency ) {
		if ( FixedPriceHelper::is_set_fixed_price() ) {
			$custom_fixed_prices = get_post_meta( $product->get_id(), 'yay_currency_custom_fixed_prices', true );
			$currency_code       = isset( $apply_currency['currency'] ) ? $apply_currency['currency'] : '';
			if ( ! empty( $custom_fixed_prices ) && isset( $custom_fixed_prices[ $currency_code ] ) && $custom_fixed_prices[ $currency_code ] ) {
				$calculate_discount_from = self::calculate_discount_from();
				$regular_price           = Helper::get_value_variable( $custom_fixed_prices[ $currency_code ]['regular_price'], $price_fixed );
				if ( 'sale_price' === $calculate_discount_from ) {
					$price_fixed = Helper::get_value_variable( $custom_fixed_prices[ $currency_code ]['sale_price'], $regular_price );
				} else {
					$price_fixed = $regular_price;
				}
			}
		}
		return (float) $price_fixed;
	}

	public static function calculate_subtotal_apply_discount( $price, $discount_amount, $products_ids, $apply_currency ) {
		$subtotal = apply_filters( 'yay_currency_discount_rules_get_cart_subtotal_apply_coupon_as_cart_rule', $price, $discount_amount, $products_ids, $apply_currency );
		return $subtotal;
	}

	// Get Original Price by Cart Item
	public static function get_product_price_by_cart_item( $cart_item, $apply_currency ) {
		$product_id    = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
		$product_price = self::get_product_price( $product_id, $apply_currency );
		$product_price = apply_filters( 'yay_currency_get_product_price_by_cart_item', $product_price, $cart_item, $apply_currency );
		$price_options = apply_filters( 'yay_currency_get_price_options_by_cart_item', 0, $cart_item, $product_id, $product_price, $apply_currency );
		return $price_options ? $product_price + $price_options : $product_price;
	}

	public static function get_cart_subtotal( $apply_currency = false ) {
		$subtotal      = 0;
		$cart_contents = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $cart_item ) {
			$product_price    = self::get_product_price_by_cart_item( $cart_item, $apply_currency );
			$product_price    = apply_filters( 'yay_currency_get_product_price_apply_currency', $product_price, $cart_item['data'], 1 );
			$product_subtotal = $product_price * $cart_item['quantity'];
			$subtotal         = $subtotal + $product_subtotal;
		}

		return $subtotal;
	}

	public static function get_cart_subtotal_shipping_fee( $apply_currency = array() ) {
		$subtotal      = 0;
		$cart_contents = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $cart_item ) {
			if ( $cart_item['quantity'] > 0 && $cart_item['data']->needs_shipping() ) {
				if ( ! $apply_currency ) {
					$product_price = self::get_product_price_default_by_cart_item( $cart_item );
				} else {
					$product_price = self::get_product_price_by_cart_item( $cart_item, $apply_currency );
					$product_price = apply_filters( 'yay_currency_get_product_price_apply_currency', $product_price, $cart_item['data'], 1 );
				}

				$product_subtotal = $product_price * $cart_item['quantity'];
				$subtotal         = $subtotal + $product_subtotal;
			}
		}

		return $subtotal;
	}

	// DISABLE : Checkout in different currency option

	public static function get_product_price_default_by_cart_item( $cart_item ) {
		$product_id    = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
		$product_price = self::get_product_price( $product_id );
		$product_price = apply_filters( 'yay_currency_get_product_price_default_by_cart_item', $product_price, $cart_item );
		$price_options = apply_filters( 'yay_currency_get_price_options_default_by_cart_item', 0, $cart_item, $product_id, $product_price );
		return $price_options ? $product_price + $price_options : $product_price;
	}

	public static function get_cart_subtotal_default() {
		$subtotal      = 0;
		$cart_contents = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $cart_item ) {
			$product_price = self::get_product_price_default_by_cart_item( $cart_item );
			$product_price = floatval( $product_price );
			if ( $product_price ) {
				$product_subtotal = $product_price * $cart_item['quantity'];
				$subtotal         = $subtotal + $product_subtotal;
			}
		}

		return $subtotal;
	}

	public static function get_total_coupons( $cart_subtotal = 0, $apply_currency = false ) {
		$total_coupon_applies = 0;
		$applied_coupons      = WC()->cart->applied_coupons;
		if ( $applied_coupons ) {
			foreach ( $applied_coupons  as $coupon_code ) {
				$coupon          = new \WC_Coupon( $coupon_code );
				$discount_type   = $coupon->get_discount_type();
				$coupon_data     = $coupon->get_data();
				$discount_amount = (float) $coupon_data['amount'];

				if ( 'percent' !== $discount_type ) {

					if ( 'fixed_product' === $discount_type ) {
						$discount_amount *= self::get_product_quantity_item_qty( true );
					}

					if ( apply_filters( 'yay_currency_incl_tax_enable', false ) ) {
						$discount_totals     = WC()->cart->get_coupon_discount_totals();
						$discount_tax_totals = WC()->cart->get_coupon_discount_tax_totals();
						$discount_totals     = wc_array_merge_recursive_numeric( $discount_totals, $discount_tax_totals );
						$discount_amount     = $discount_totals[ $coupon->get_code() ];
					}

					if ( apply_filters( 'yay_currency_excl_tax_enable', false ) ) {
						$tax_rate_percent = apply_filters( 'yay_currency_get_rate_percent_in_cart', false );
						if ( $tax_rate_percent ) {
							$discount_amount = $discount_amount / ( 1 + $tax_rate_percent );
						}
					}

					if ( $apply_currency ) {
						$discount_amount = YayCurrencyHelper::calculate_price_by_currency( $discount_amount, true, $apply_currency );
					}

					$total_coupon_applies += $discount_amount;
				} else {
					$total_coupon_applies += ( $cart_subtotal * $discount_amount ) / 100;
				}
			}
		}
		return $total_coupon_applies;
	}

	public static function get_total_fees( $apply_currency, $calculate_include_total = false, $calculate_default = false ) {
		$total_fees = 0;
		foreach ( WC()->cart->get_fees() as $fee ) {
			if ( $fee->taxable || $calculate_include_total ) {
				$fee_amount = $fee->amount;
				if ( ! $calculate_default ) {
					$fee_amount = YayCurrencyHelper::calculate_price_by_currency( $fee_amount, true, $apply_currency );
				} else {
					$apply_currency = $apply_currency ? $apply_currency : YayCurrencyHelper::detect_current_currency();
					if ( Helper::default_currency_code() !== $apply_currency['currency'] ) {
						$fee_amount = $fee_amount / YayCurrencyHelper::get_rate_fee( $apply_currency );
					}
				}
				$fee_amount  = apply_filters( 'yay_currency_recalculate_fee_incl_tax', $fee_amount, $fee );
				$total_fees += $fee_amount;
			}
		}
		return $total_fees;
	}

	public static function get_total_tax( $apply_currency ) {
		$total_tax = 0;
		if ( 'yes' === get_option( 'woocommerce_calc_taxes' ) ) {
			$total_tax = self::get_cart_subtotal_by_fixed_include_tax( $apply_currency );
		}
		return $total_tax;
	}

	public static function get_total_tax_is_fallback( $apply_currency ) {
		$total_tax = 0;
		if ( 'yes' === get_option( 'woocommerce_calc_taxes' ) ) {
			$shipping_total = self::get_shipping_total_selected( $apply_currency, false, true, true );
			$taxes_in_cart  = TaxHelper::get_info_taxes_include_in_cart( $apply_currency, $shipping_total, false );
			$total_tax      = $taxes_in_cart ? $taxes_in_cart['total_tax'] : 0;
			if ( TaxHelper::is_apply_multiple_taxes() ) {
				$total_tax = TaxHelper::recalculate_excl_total_taxes_in_cart( $total_tax, $apply_currency );
			}
		}
		return $total_tax;
	}

	public static function get_cart_subtotal_by_fixed_include_tax( $apply_currency ) {
		$calculate_default = $apply_currency ? false : true;
		$shipping_total    = self::get_shipping_total_selected( $apply_currency, $calculate_default, true );
		$taxes_in_cart     = TaxHelper::get_info_taxes_include_in_cart( $apply_currency, $shipping_total, $calculate_default );
		$subtotal          = $taxes_in_cart ? $taxes_in_cart['total_tax'] : 0;
		if ( TaxHelper::is_apply_multiple_taxes() ) {
			$subtotal = TaxHelper::recalculate_excl_total_taxes_in_cart( $subtotal, $apply_currency );
		}
		return $subtotal;
	}

	public static function get_product_quantity_item_qty( $include_all_products = false ) {

		$total_quantity = 0;

		$cart_contents = WC()->cart->get_cart_contents();

		if ( $cart_contents ) {
			foreach ( $cart_contents as $cart_item ) {
				if ( $cart_item['quantity'] > 0 ) {
					$quantity        = ! $include_all_products ? ( $cart_item['data']->needs_shipping() ? $cart_item['quantity'] : 0 ) : $cart_item['quantity'];
					$total_quantity += $quantity;
				}
			}
		}

		return $total_quantity;
	}

	public static function get_shipping_flat_rate_fee_total_selected( $apply_currency = array(), $calculate_default = false, $calculate_tax = false, $is_fallback = false ) {
		$shipping = WC()->session->get( 'shipping_for_package_0' );
		if ( ! $shipping || ! isset( $shipping['rates'] ) ) {
			return false;
		}
		$shipping_fee = false;
		foreach ( $shipping['rates'] as $method_id => $method ) {
			if ( WC()->session->get( 'chosen_shipping_methods' )[0] === $method_id ) {
				if ( 'local_pickup' === $method->method_id ) {
					$shipping = new \WC_Shipping_Local_Pickup( $method->instance_id );
					if ( $calculate_tax && 'taxable' !== $shipping->tax_status ) {
						$shipping_fee = -1;
						break;
					}
				}

				if ( 'flat_rate' === $method->method_id ) {

					$shipping = new \WC_Shipping_Flat_Rate( $method->instance_id );

					if ( $calculate_tax && 'taxable' !== $shipping->tax_status ) {
						$shipping_fee = -1;
						break;
					}
					$data         = array(
						'apply_currency' => $apply_currency,
						'is_fallback'    => $is_fallback,
					);
					$shipping_fee = self::get_total_shipping_fee_flat_rate_method( 0, $method, $data );
				}
			}
		}

		return $shipping_fee;
	}

	public static function get_shipping_total_selected( $apply_currency, $calculate_default = false, $calculate_tax = false, $is_fallback = false ) {
		$shipping_total = WC()->cart->shipping_total;

		if ( ! $shipping_total ) {
			return 0;
		}

		if ( ! $calculate_default ) {
			$shipping_total = YayCurrencyHelper::calculate_price_by_currency( WC()->cart->shipping_total, true, $apply_currency );
		}

		$shipping_total = apply_filters( 'yay_currency_shipping_total_incl_tax', $shipping_total, $apply_currency );

		$shipping_flat_fee_total = self::get_shipping_flat_rate_fee_total_selected( $apply_currency, $calculate_default, $calculate_tax, $is_fallback );

		if ( $shipping_flat_fee_total ) {
			$shipping_total = -1 === $shipping_flat_fee_total ? 0 : $shipping_flat_fee_total;
		}

		return $shipping_total;

	}

	public static function evaluate_cost( $sum, $args = array(), $calculate_default = false, $is_fallback = false ) {
		// Add warning for subclasses.
		if ( ! is_array( $args ) || ! array_key_exists( 'qty', $args ) || ! array_key_exists( 'cost', $args ) ) {
			wc_doing_it_wrong( __FUNCTION__, '$args must contain `cost` and `qty` keys.', '4.0.1' );
		}

		include_once WC()->plugin_path() . '/includes/libraries/class-wc-eval-math.php';

		$locale   = localeconv();
		$decimals = array( wc_get_price_decimal_separator(), $locale['decimal_point'], $locale['mon_decimal_point'], ',' );
		if ( $calculate_default ) {
			$sum = str_replace( '[yaycurrency-fee', '[yaycurrency-fee-default', $sum );
			$sum = str_replace( '[fee', '[yaycurrency-fee-default', $sum );
		} else {
			$sum         = str_replace( '[yaycurrency-fee-default', '[yaycurrency-fee', $sum );
			$str_replace = $is_fallback ? '[yaycurrency-fee-fallback' : '[yaycurrency-fee';
			$sum         = str_replace( '[fee', $str_replace, $sum );
		}

		$sum = do_shortcode(
			str_replace(
				array(
					'[qty]',
					'[cost]',
				),
				array(
					$args['qty'],
					$args['cost'],
				),
				$sum
			)
		);

		// Remove whitespace from string.
		$sum = preg_replace( '/\s+/', '', $sum );

		// Remove locale from string.
		$sum = str_replace( $decimals, '.', $sum );

		// Trim invalid start/end characters.
		$sum = rtrim( ltrim( $sum, "\t\n\r\0\x0B+*/" ), "\t\n\r\0\x0B+-*/" );

		// Do the math.
		return $sum ? \WC_Eval_Math::evaluate( $sum ) : 0;
	}

	public static function get_total_shipping_fee_flat_rate_method( $shipping_fee, $method, $data ) {
		$apply_currency = isset( $data['apply_currency'] ) ? $data['apply_currency'] : false;
		if ( ! $apply_currency ) {
			return $shipping_fee;
		}
		$is_fallback = isset( $data['is_fallback'] ) ? $data['is_fallback'] : false;
		$shipping    = new \WC_Shipping_Flat_Rate( $method->instance_id );
		$cost        = $shipping->get_option( 'cost' );
		if ( ! empty( $cost ) ) {
			if ( ! is_numeric( $cost ) ) {
				$args         = array(
					'qty'  => self::get_product_quantity_item_qty(),
					'cost' => apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency ),
				);
				$shipping_fee = self::evaluate_cost( $cost, $args, false, $is_fallback );
				if ( is_numeric( $shipping_fee ) && ! strpos( $cost, 'fee' ) ) {
					$shipping_fee = YayCurrencyHelper::calculate_price_by_currency( $shipping_fee, true, $apply_currency );
				}
				$shipping_fee = apply_filters( 'yay_currency_recalculate_shipping_fee_incl_tax', $shipping_fee, $method, $apply_currency );
			} else {
				$shipping_fee = YayCurrencyHelper::calculate_price_by_currency( $cost, true, $apply_currency );
			}
		} else {
			$shipping_fee = 0;
		}

		$shipping_classes = WC()->shipping->get_shipping_classes();
		if ( empty( $shipping_classes ) ) {
			return $shipping_fee;
		}
		$has_costs              = false;
		$package                = array();
		$cart_shipping_packages = WC()->cart->get_shipping_packages();
		if ( $cart_shipping_packages && is_array( $cart_shipping_packages ) ) {
			$package = array_shift( $cart_shipping_packages );
		}
		$product_shipping_classes = $shipping->find_shipping_classes( $package );

		foreach ( $product_shipping_classes as $shipping_class => $products ) {
			$shipping_class_term = get_term_by( 'slug', $shipping_class, 'product_shipping_class' );
			$class_cost_string   = $shipping_class_term && $shipping_class_term->term_id ? $shipping->get_option( 'class_cost_' . $shipping_class_term->term_id, $shipping->get_option( 'class_cost_' . $shipping_class, '' ) ) : $shipping->get_option( 'no_class_cost', '' );
			if ( '' === $class_cost_string ) {
				continue;
			}
			if ( ! empty( $class_cost_string ) && ! is_numeric( $class_cost_string ) ) {
				$has_costs  = true;
				$class_cost = self::evaluate_cost(
					$class_cost_string,
					array(
						'qty'  => array_sum( wp_list_pluck( $products, 'quantity' ) ),
						'cost' => array_sum( wp_list_pluck( $products, 'line_total' ) ),
					)
				);
				if ( ! empty( $cost ) && is_numeric( $cost ) ) {
					$class_cost += $cost;
				}
			} else {
				$class_cost = $class_cost_string;
			}

			if ( is_numeric( $class_cost ) && ! strpos( $class_cost_string, 'fee' ) ) {
				$class_cost = YayCurrencyHelper::calculate_price_by_currency( $class_cost, true, $apply_currency );
			}
			// calculate tax
			if ( $has_costs ) {
				if ( wc_tax_enabled() && 'incl' === get_option( 'woocommerce_tax_display_cart' ) ) {
					$tax_class_cost = \WC_Tax::calc_shipping_tax( $class_cost, \WC_Tax::get_shipping_tax_rates() );
					if ( $tax_class_cost && is_array( $tax_class_cost ) ) {
						$class_cost += array_shift( $tax_class_cost );
					}
				}
			}
			if ( 'class' === $shipping->type ) {
				$shipping_fee += $class_cost;
			}
		}
		return $shipping_fee;

	}

	public static function detect_shipping_methods_ignore() {
		$shipping_methods_args = array( 'alids', 'betrs_shipping', 'printful_shipping', 'easyship', 'printful_shipping_STANDARD', 'BookVAULT Shipping' );
		$special_methods_args  = array( 'per_product', 'tree_table_rate' );

		return array(
			'shipping_methods' => apply_filters( 'yay_currency_detect_shipping_methods_ignore', $shipping_methods_args ),
			'special_methods'  => apply_filters( 'yay_currency_detect_special_methods_ignore', $special_methods_args ),
		);
	}

	public static function get_cart_total( $apply_currency, $is_fallback = false ) {
		if ( $apply_currency ) {
			$calculate_default = false;
			$cart_subtotal     = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
		} else {
			$calculate_default = true;
			$cart_subtotal     = apply_filters( 'yay_currency_get_cart_subtotal_default', 0 );
		}
		$shipping_total       = self::get_shipping_total_selected( $apply_currency, $calculate_default, false, $is_fallback );
		$total_coupon_applies = self::get_total_coupons( $cart_subtotal, $apply_currency );
		$total_fees           = self::get_total_fees( $apply_currency, true, $calculate_default );
		$total_tax            = 0;

		if ( self::detect_recalculate_tax() ) {
			$total_tax = ! $is_fallback ? self::get_total_tax( $apply_currency ) : self::get_total_tax_is_fallback( $apply_currency );
		}

		$total = ( $cart_subtotal - $total_coupon_applies ) + $total_tax + $shipping_total + $total_fees;
		return $total;
	}

	// CHECKOUT CART TOTAL RECALCULATE
	public static function recalculate_shipping_total( $apply_currency, $shipping_total ) {
		if ( WC()->cart->get_taxes() ) {
			$rateId        = key( WC()->cart->get_taxes() );
			$taxes_in_cart = TaxHelper::get_info_taxes_include_in_cart( $apply_currency, $shipping_total );
			if ( in_array( $rateId, array_keys( $taxes_in_cart['taxes'] ), true ) ) {
				$is_virtual = isset( $taxes_in_cart['taxes'][ $rateId ]['is_virtual'] ) ? $taxes_in_cart['taxes'][ $rateId ]['is_virtual'] : false;
				if ( $is_virtual ) {
					$shipping_total = 0;
				}
			}
		}
		return $shipping_total;
	}

	public static function recalculate_checkout_cart_total( $apply_currency ) {
		$cart_subtotal        = apply_filters( 'yay_currency_get_cart_subtotal', 0, $apply_currency );
		$shipping_total       = apply_filters( 'yay_currency_get_shipping_total', 0, $apply_currency, false );
		$total_tax_fees       = self::get_total_fees( $apply_currency, true );
		$total_coupon_applies = apply_filters( 'yay_currency_get_discount_total', 0, $apply_currency );
		$total_tax            = 0;

		if ( self::detect_recalculate_tax() ) {
			$total_tax = TaxHelper::get_cart_subtotal_by_fixed_include_tax( $apply_currency );
			if ( apply_filters( 'yay_currency_excl_tax_enable', false ) ) {
				if ( apply_filters( 'yay_currency_is_more_one_tax_apply_in_cart', false ) ) {
					$total_tax = apply_filters( 'yay_currency_recalculate_excl_total_taxes_in_cart', $total_tax, $apply_currency );
				} else {
					$total_tax = apply_filters( 'yay_currency_recalculate_total_tax', $total_tax, $apply_currency );
				}
			}
			$shipping_total = self::recalculate_shipping_total( $apply_currency, $shipping_total );
		}

		$cart_total = ( $cart_subtotal - $total_coupon_applies ) + $total_tax + $shipping_total + $total_tax_fees;
		return $cart_total;
	}

	// CALCULATE PRODUCT INFO
	public static function get_product_subtotal_info( $product, $quantity = 1, $apply_currency = array() ) {
		$product_price    = self::get_product_price( $product->get_id(), $apply_currency );
		$product_subtotal = $product_price * $quantity;
		$data             = array(
			'subtotal'     => $product_subtotal,
			'subtotal_tax' => 0,
		);

		if ( 'yes' === get_option( 'woocommerce_calc_taxes' ) ) {
			if ( 'taxable' === $product->get_tax_status() ) {
				$rateId               = key( \WC_Tax::get_rates( $product->get_tax_class() ) );
				$tax_rate_percent     = \WC_Tax::get_rate_percent_value( $rateId );
				$data['subtotal_tax'] = $product_subtotal * $tax_rate_percent / 100;
			}
		}
		return $data;
	}

	// 3rd MULTIPLE LANGUAGE PLUGIN

	public static function get_filtered_currency_by_language( $currencies_by_languages = array(), $current_language = '' ) {
		$filtered_currency_by_language = array_filter(
			$currencies_by_languages,
			function ( $currency ) use ( $current_language ) {
				if ( $currency['code'] === $current_language ) {
					return true;
				}
				return false;
			}
		);
		return $filtered_currency_by_language;
	}

	public static function get_apply_currency_by_polylang_or_wpml( $apply_currency = array(), $currencies_by_languages = array(), $selected_language = '', $converted_currency = array() ) {
		$filtered_currency_by_language = self::get_filtered_currency_by_language( $currencies_by_languages, $selected_language );
		if ( ! $filtered_currency_by_language ) {
			return $apply_currency;
		}
		$filtered_currency_by_language = array_shift( $filtered_currency_by_language );
		if ( ! $filtered_currency_by_language ) {
			return $apply_currency;
		}

		if ( wp_doing_ajax() || isset( $_REQUEST['wc-ajax'] ) || apply_filters( 'yay_currency_is_not_detect_language', false ) || is_admin() ) {
			return $apply_currency;
		}

		$cookie_language_name = YayCurrencyHelper::get_cookie_name( 'language' );
		$cookie_switcher_name = YayCurrencyHelper::get_cookie_name( 'switcher' );
		if ( isset( $_COOKIE[ $cookie_language_name ] ) ) {
			$cookie_language = sanitize_text_field( $_COOKIE[ $cookie_language_name ] );
			if ( $cookie_language === $selected_language || wp_doing_ajax() ) {
				if ( $apply_currency['currency'] === $filtered_currency_by_language['currency'] ) {
					YayCurrencyHelper::set_cookie( $cookie_language_name, $selected_language );
					return $apply_currency;
				}
			}
		}
		if ( isset( $cookie_language ) && $cookie_language === $selected_language ) {
			if ( isset( $_COOKIE[ $cookie_switcher_name ] ) ) {
				$cookie_switcher = intval( sanitize_text_field( $_COOKIE[ $cookie_switcher_name ] ) );
				if ( $cookie_switcher === $apply_currency['ID'] ) {
					return $apply_currency;
				}
			}
		}

		YayCurrencyHelper::set_cookie( $cookie_language_name, $selected_language );
		YayCurrencyHelper::delete_cookie( $cookie_switcher_name );
		$currency        = isset( $filtered_currency_by_language['currency'] ) ? $filtered_currency_by_language['currency'] : reset( $filtered_currency_by_language )['currency'];
		$currency_object = Helper::get_yay_currency_by_currency_code( $currency );
		if ( $currency_object ) {
			$currency_ID    = $currency_object->ID;
			$apply_currency = YayCurrencyHelper::get_currency_by_ID( $currency_ID ) ? YayCurrencyHelper::get_currency_by_ID( $currency_ID ) : reset( $converted_currency );
		}
		return $apply_currency;
	}

	// DETECT WITH CONDITION

	public static function get_product_prices_filters_priority( $priority = 10 ) {
		// Compatible with B2B Wholesale Suite, Price by Country, B2BKing
		if ( class_exists( 'B2bwhs' ) || class_exists( 'CBP_Country_Based_Price' ) || class_exists( 'B2bkingcore' ) ) {
			$priority = 100000;
		}

		return apply_filters( 'yay_currency_product_prices_filters_priority', $priority );
	}

	public static function get_filter_priority( $priority = 10, $hook_type = 'woocommerce_checkout_create_order' ) {
		if ( 'woocommerce_checkout_create_order' === $hook_type ) {
			$priority = PHP_INT_MAX;
		}
		return apply_filters( 'yay_currency_orders_filter_priority', $priority );
	}

	public static function get_format_filters_priority( $priority = 10 ) {

		if ( class_exists( 'AG_Tyl_init' ) || class_exists( 'WC_Product_Price_Based_Country' ) ) {
			$priority = 9999;
		}

		return apply_filters( 'yay_currency_format_filters_priority', $priority );
	}

	public static function get_approximate_price_filters_priority( $priority = 9999 ) {
		return apply_filters( 'yay_currency_approximate_price_priority', $priority );
	}

	public static function get_fee_priority( $priority = 10 ) {
		// Payment Gateway Based Fees and Discounts for WooCommerce
		if ( class_exists( 'Alg_Woocommerce_Checkout_Fees' ) ) {
			$priority = PHP_INT_MAX;
		}

		if ( class_exists( '\Packetery\Module\Plugin' ) ) {
			$priority = PHP_INT_MAX;
		}

		return apply_filters( 'yay_currency_fee_priority', $priority );
	}

	public static function detect_keep_old_currency_symbol( $flag, $is_dis_checkout_diff_currency, $apply_currency ) {

		if ( YayCurrencyHelper::detect_allow_hide_dropdown_currencies() || self::detect_is_origin_default_currency( $apply_currency ) ) {
			return true;
		}

		return apply_filters( 'yay_currency_use_default_default_currency_symbol', $flag, $is_dis_checkout_diff_currency, $apply_currency );

	}

	public static function detect_is_origin_default_currency( $apply_currency ) {

		$flag = false;

		if ( YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' ) ) {
			$flag = true;
		}

		return apply_filters( 'yay_currency_is_original_default_currency', $flag, $apply_currency );
	}

	public static function detect_ignore_price_conversion( $flag, $price, $product ) {
		// Role Based Pricing for WooCommerce plugin & WooCommerce Bulk Discount plugin
		if ( class_exists( 'AF_C_S_P_Price' ) || class_exists( 'Woo_Bulk_Discount_Plugin_t4m' ) || class_exists( 'FP_Lottery' ) ) {
			$flag = true;
		}

		if ( defined( 'SUBSCRIPTIONS_FOR_WOOCOMMERCE_VERSION' ) ) {
			$flag = true;
		}

		// Custom Product Boxes: https://wisdmlabs.com/assorted-bundles-woocommerce-custom-product-boxes-plugin/
		if ( class_exists( 'Custom_Product_Boxes' ) ) {
			$flag = true;
		}

		return apply_filters( 'yay_currency_before_calculate_totals_ignore_price_conversion', $flag, $price, $product );
	}

	public static function detect_original_product_price( $flag, $price, $product ) {

		if ( empty( $price ) || ! is_numeric( $price ) || YayCurrencyHelper::is_wc_json_products() || class_exists( 'BM' ) ) {
			$flag = true;
		}

		// WC Fields Factory plugin
		if ( class_exists( 'wcff' ) && doing_filter( 'woocommerce_get_cart_item_from_session' ) ) {
			$flag = true;
		}

		if ( doing_filter( 'woocommerce_before_calculate_totals' ) ) {
			$flag = self::detect_ignore_price_conversion( $flag, $price, $product );
		}

		return apply_filters( 'yay_currency_is_original_product_price', $flag, $price, $product );
	}

	public static function detect_price_with_conditions( $price, $product, $apply_currency ) {
		// YayExtra , YayPricing
		$is_ydp_adjust_price = false;
		$calculate_price     = YayCurrencyHelper::calculate_price_by_currency( $price, false, $apply_currency );

		if ( class_exists( '\YayPricing\FrontEnd\ProductPricing' ) ) {
			$is_ydp_adjust_price = apply_filters( 'ydp_check_adjust_price', false );
		}

		if ( class_exists( '\YayPricing\FrontEnd\ProductPricing' ) && $is_ydp_adjust_price ) {
			return $calculate_price;
		}

		return apply_filters( 'yay_currency_product_price_3rd_with_condition', false, $product );

	}

	public static function detect_cart_fees_original( $flag = false ) {

		if ( class_exists( 'Woocommerce_Conditional_Product_Fees_For_Checkout_Pro' ) || class_exists( 'TaxamoClass' ) || class_exists( 'WooWallet' ) || function_exists( 'WholeSale_Discount_Based_on_CartTotal' ) ) {
			$flag = true;
		}

		return apply_filters( 'yay_currency_is_cart_fees_original', $flag );

	}

	public static function detect_original_format_order_item_totals( $flag, $total_rows, $order, $tax_display ) {
		if ( isset( $_GET['action'] ) && 'generate_wpo_wcpdf' === $_GET['action'] ) {
			$flag = true;
		}
		return apply_filters( 'yay_currency_is_original_format_order_item_totals', $flag, $total_rows, $order, $tax_display );
	}

	public static function detect_payment_reload_page() {
		$force_payment_currency = CountryHelper::force_payment_specific_currency();
		return isset( $force_payment_currency['reload_page'] ) && $force_payment_currency['reload_page'];
	}

	public static function detect_force_currency_reload_page() {
		$force_currency = Helper::detect_force_currency_by_payment_method();
		return isset( $force_currency['force_currency_reload_page'] ) && $force_currency['force_currency_reload_page'];
	}

	public static function detect_recalculate_tax( $flag = false ) {
		if ( wc_tax_enabled() && 'incl' !== get_option( 'woocommerce_tax_display_cart' ) ) {
			$flag = true;
		}

		return apply_filters( 'yay_currency_is_recalculate_tax', $flag );
	}

	public static function detect_used_other_currency_3rd_plugin( $order_id, $order ) {

		//FOX - Currency Switcher Professional for WooCommerce
		$order_rate = get_post_meta( $order_id, '_woocs_order_rate', true );
		//WooPayments
		$wcpay_default_currency = get_post_meta( $order_id, '_wcpay_multi_currency_order_default_currency', true );
		//CURCY - WooCommerce Multi Currency
		$wmc_order_info = get_post_meta( $order_id, 'wmc_order_info', true );

		if ( $order_rate || $wcpay_default_currency || $wmc_order_info ) {
			return true;
		}

		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			if ( $order->get_meta( '_woocs_order_rate', true ) || $order->get_meta( '_wcpay_multi_currency_order_default_currency', true ) ) {
				return true;
			}
			if ( $order->get_meta( 'wmc_order_info', true ) ) {
				return true;
			}
		}

		return false;

	}

	public static function detect_recalculate_value_with_fallback_currency() {
		$detect_recalculate = YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' );
		return apply_filters( 'yay_currency_recalculate_value_with_fallback_currency', $detect_recalculate );
	}

	public static function detect_ajax_caching_doing() {
		if ( ! wp_doing_ajax() ) {
			return false;
		}
		if ( isset( $_REQUEST['action'] ) && in_array( $_REQUEST['action'], array( 'yay_caching_get_price_html', 'yay_caching_generate_currency_switcher_html' ), true ) ) {
			return true;
		}
		return false;
	}

	public static function detect_deregister_script() {
		if ( class_exists( '\LP_Admin_Assets' ) ) {
			wp_deregister_script( 'vue-libs' );
		}
		if ( defined( 'STM_MOTORS_EXTENDS_PLUGIN_VERSION' ) ) {
			wp_deregister_script( 'vue.js' );
		}
		//EnvíaloSimple: Email Marketing y Newsletters plugin
		if ( defined( 'ES_PLUGIN_URL_BASE' ) ) {
			wp_deregister_script( 'es-vue-js' );
		}
		do_action( 'yay_currency_admin_deregister_script' );
	}

	public static function detect_google_crawler() {
		// Check if auto select currency by country is disabled or Google crawler handling is enabled
		if ( ! get_option( 'yay_currency_auto_select_currency_by_countries', 0 ) ||
			get_option( 'yay_currency_google_crawlers_or_bots_enable', 1 ) ) {
			return false;
		}

		// Check for Google-related user agents
		if ( isset( $_SERVER['HTTP_USER_AGENT'] ) ) {
			$user_agent = strtolower( sanitize_text_field( $_SERVER['HTTP_USER_AGENT'] ) );

			// Common Google bot user agent strings
			$google_bots = array(
				'googlebot',
				'google-read-aloud',
				'chrome-lighthouse',
				'adsbot-google',
				'mediapartners-google',
				'google-structured-data-testing-tool',
				'facebookexternalhit',
			);

			foreach ( $google_bots as $bot ) {
				if ( false !== strpos( $user_agent, $bot ) ) {
					return true;
				}
			}
		}

		// Check for Google IP addresses
		if ( isset( $_SERVER['REMOTE_ADDR'] ) ) {
			// Verify IP against Google's published IP ranges
			$ip = sanitize_text_field( $_SERVER['REMOTE_ADDR'] );
			if ( gethostbyaddr( $ip ) && strpos( gethostbyaddr( $ip ), 'googlebot.com' ) !== false ) {
				return true;
			}
		}

		return false;
	}

	// Approximately Price

	public static function get_params_by_country_info( $country_info, $converted_currency ) {
		$params             = false;
		$converted_currency = $converted_currency ? $converted_currency : YayCurrencyHelper::converted_currency();
		if ( ! isset( $country_info['currency_code'] ) || ! isset( $country_info['country_code'] ) || empty( $country_info['country_code'] ) || empty( $country_info['currency_code'] ) ) {
			return $params;
		}
		if ( isset( $_COOKIE['yay_currency_approximately_price_currency_params'] ) && ! empty( $_COOKIE['yay_currency_approximately_price_currency_params'] ) ) {
			$params = array_map( 'sanitize_text_field', $_COOKIE['yay_currency_approximately_price_currency_params'] );
		} else {
			$currency_by_country_code = CountryHelper::find_apply_currency_by_country_code( $country_info['country_code'], $country_info['currency_code'], $converted_currency );
			$params                   = array(
				'country_in_yay_currency' => $currency_by_country_code ? 'yes' : 'no',
				'country_code'            => isset( $country_info['country_code'] ) ? $country_info['country_code'] : 'US',
				'currency_code'           => $currency_by_country_code && isset( $currency_by_country_code['currency'] ) ? $currency_by_country_code['currency'] : $country_info['currency_code'],
				'rate'                    => $currency_by_country_code ? YayCurrencyHelper::get_rate_fee( $currency_by_country_code ) : RateHelper::get_rate_fee_from_currency_not_exists_in_list( $country_info['currency_code'] ),
				'symbol'                  => $currency_by_country_code && isset( $currency_by_country_code['symbol'] ) ? $currency_by_country_code['symbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $country_info['currency_code'] ),
				'format'                  => $currency_by_country_code ? YayCurrencyHelper::format_currency_symbol( $currency_by_country_code ) : YayCurrencyHelper::format_currency_position( get_option( 'woocommerce_currency_pos' ) ),
				'decimals'                => $currency_by_country_code && isset( $currency_by_country_code['numberDecimal'] ) ? $currency_by_country_code['numberDecimal'] : apply_filters( 'yay_currency_approximately_price_num_decimals', 2 ),
				'decimal_separator'       => $currency_by_country_code && isset( $currency_by_country_code['decimalSeparator'] ) ? $currency_by_country_code['decimalSeparator'] : apply_filters( 'yay_currency_approximately_price_decimal_sep', '.' ),
				'thousand_separator'      => $currency_by_country_code && isset( $currency_by_country_code['thousandSeparator'] ) ? $currency_by_country_code['thousandSeparator'] : apply_filters( 'yay_currency_approximately_price_thousand_sep', ',' ),
			);
			$_COOKIE['yay_currency_approximately_price_currency_params'] = $params;
		}
		return $params;
	}

	public static function format_range_price( $product, $apply_currency, $args = array() ) {
		$params = ! empty( $args['params'] ) ? $args['params'] : false;
		if ( ! $params ) {
			return false;
		}

		$rate             = isset( $params['rate'] ) && ! empty( $params['rate'] ) ? floatval( $params['rate'] ) : 1;
		$tax_display_mode = get_option( 'woocommerce_tax_display_shop' );
		$child_prices     = array();

		foreach ( array_map( 'wc_get_product', $product->get_visible_children() ) as $child ) {
			$price = $child->get_price( 'edit' );
			if ( empty( $price ) ) {
				continue;
			}

			$result_price = ( 'incl' === $tax_display_mode )
				? wc_get_price_including_tax(
					$child,
					array(
						'qty'   => 1,
						'price' => $price,
					)
				)
				: wc_get_price_excluding_tax(
					$child,
					array(
						'qty'   => 1,
						'price' => $price,
					)
				);

			// Apply currency conversion
			$result_price = $apply_currency
				? YayCurrencyHelper::calculate_price_by_currency( $result_price, false, $apply_currency )
				: $result_price * $rate;

			// Apply fixed price if necessary
			if ( ! empty( $args['check_fixed_price'] ) && $apply_currency ) {
				$result_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $child, $result_price, $apply_currency );
			}

			$child_prices[] = $result_price;
		}

		// Calculate min and max prices
		$min_price = ! empty( $child_prices ) ? min( $child_prices ) : 0;
		$max_price = ! empty( $child_prices ) ? max( $child_prices ) : 0;

		if ( ! $min_price && ! $max_price ) {
			return false;
		}

		// Format prices
		$format_price = function ( $price ) use ( $params ) {
			return self::get_formatted_price( $price, $params );
		};

		return ( $min_price === $max_price ) ? $format_price( $min_price ) : $format_price( $min_price ) . ' - ' . $format_price( $max_price );
	}

	public static function get_approximately_price_data( $country_info = array(), $apply_currency = array(), $converted_currency = array() ) {
		$params                      = self::get_params_by_country_info( $country_info, $converted_currency );
		$approximately_currency_code = isset( $params['currency_code'] ) ? $params['currency_code'] : false;
		if ( ! $params || ! $approximately_currency_code || ! isset( $apply_currency['currency'] ) || $apply_currency['currency'] === $approximately_currency_code ) {
			return false;
		}
		$approximately_apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $approximately_currency_code );
		return array(
			'approximately_apply_currency' => $approximately_apply_currency,
			'check_fixed_price'            => FixedPriceHelper::is_set_fixed_price() && $approximately_apply_currency,
			'params'                       => $params,
		);
	}

	public static function get_formatted_price_by_specific_product( $product, $approximately_price_data = array() ) {
		$params                       = $approximately_price_data['params'];
		$approximately_apply_currency = $approximately_price_data['approximately_apply_currency'];
		if ( isset( $approximately_price_data['default_price'] ) && $approximately_price_data['default_price'] ) {
			$raw_price = $approximately_price_data['default_price'];
		} else {
			$raw_price = wc_get_price_to_display(
				$product,
				array(
					'qty'   => 1,
					'price' => $product->get_price( 'edit' ),
				)
			);
		}

		$convert_price = $approximately_apply_currency ? YayCurrencyHelper::calculate_price_by_currency( $raw_price, false, $approximately_apply_currency ) : $raw_price * floatval( $params['rate'] );
		if ( isset( $approximately_price_data['check_fixed_price'] ) && $approximately_price_data['check_fixed_price'] ) {
			if ( isset( $approximately_price_data['quantity'] ) && $approximately_price_data['quantity'] > 1 ) {
				$fixed_product_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $product, false, $approximately_apply_currency );
				$convert_price       = $fixed_product_price ? $fixed_product_price * $approximately_price_data['quantity'] : $convert_price;
			} else {
				$convert_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $product, $convert_price, $approximately_apply_currency );
			}
		}

		return self::get_formatted_price( $convert_price, $params );

	}

	public static function get_formatted_sale_price_by_specific_product( $product, $approximately_price_data = array() ) {
		$params                       = $approximately_price_data['params'];
		$approximately_apply_currency = $approximately_price_data['approximately_apply_currency'];
		if ( isset( $approximately_price_data['default_price'] ) && $approximately_price_data['default_price'] ) {
			$regular_price = $approximately_price_data['default_regular_price'];
			$sale_price    = $approximately_price_data['default_sale_price'];
		} else {
			$regular_price = wc_get_price_to_display(
				$product,
				array(
					'qty'   => 1,
					'price' => $product->get_regular_price( 'edit' ),
				)
			);
			$sale_price    = wc_get_price_to_display(
				$product,
				array(
					'qty'   => 1,
					'price' => $product->get_sale_price( 'edit' ),
				)
			);

		}

		$convert_regular_price = $approximately_apply_currency ? YayCurrencyHelper::calculate_price_by_currency( $regular_price, false, $approximately_apply_currency ) : $regular_price * floatval( $params['rate'] );
		$convert_sale_price    = $approximately_apply_currency ? YayCurrencyHelper::calculate_price_by_currency( $sale_price, false, $approximately_apply_currency ) : $sale_price * floatval( $params['rate'] );
		if ( isset( $approximately_price_data['check_fixed_price'] ) && $approximately_price_data['check_fixed_price'] ) {
			if ( isset( $approximately_price_data['quantity'] ) && $approximately_price_data['quantity'] > 1 ) {
				$fixed_regular_price   = FixedPriceHelper::get_price_fixed_by_apply_currency( $product, false, 'regular', $approximately_apply_currency );
				$convert_regular_price = $fixed_regular_price ? $fixed_regular_price * $approximately_price_data['quantity'] : $convert_regular_price;
				$fixed_sale_price      = FixedPriceHelper::get_price_fixed_by_apply_currency( $product, false, 'sale', $approximately_apply_currency );
				$convert_sale_price    = $fixed_sale_price ? $fixed_sale_price * $approximately_price_data['quantity'] : $convert_sale_price;
			} else {
				$convert_regular_price = FixedPriceHelper::get_fixed_regular_sale_price_by_apply_currency( $product, $convert_regular_price, 'regular', $approximately_apply_currency );
				$convert_sale_price    = FixedPriceHelper::get_fixed_regular_sale_price_by_apply_currency( $product, $convert_sale_price, 'sale', $approximately_apply_currency );
			}
		}

		if ( $convert_regular_price !== $convert_sale_price ) {
			$formatted_regular_price = self::get_formatted_price( $convert_regular_price, $params );
			$formatted_sale_price    = self::get_formatted_price( $convert_sale_price, $params );
			return wc_format_sale_price( $formatted_regular_price, $formatted_sale_price );
		}

		return self::get_formatted_price( $convert_regular_price, $params );
	}

	public static function get_formatted_price( $price = 0, $params = array() ) {
		if ( ( ! $params || ( isset( $params['country_in_yay_currency'] ) && 'no' === $params['country_in_yay_currency'] ) ) && class_exists( 'NumberFormatter' ) ) {
			$formatted_price = CountryHelper::format_amount_by_country_code( $price, $params['country_code'], $params['currency_code'] );
		} else {
			$formatted_price = number_format( $price, (int) $params['decimals'], $params['decimal_separator'], $params['thousand_separator'] );
			$formatted_price = sprintf( $params['format'], $params['symbol'], $formatted_price );
		}

		return $formatted_price;
	}

	public static function get_formatted_product_price( $product, $country_info, $apply_currency, $converted_currency = array() ) {

		$approximately_price_data = self::get_approximately_price_data( $country_info, $apply_currency, $converted_currency );

		if ( ! $approximately_price_data ) {
			return false;
		}

		if ( 'variable' === $product->get_type() ) {
			$formatted_price = self::format_range_price( $product, $approximately_price_data['approximately_apply_currency'], $approximately_price_data );
			if ( ! $formatted_price ) {
				return false;
			}
		} elseif ( $product->is_on_sale() ) {
			$formatted_price = self::get_formatted_sale_price_by_specific_product( $product, $approximately_price_data );
		} else {
			$formatted_price = self::get_formatted_price_by_specific_product( $product, $approximately_price_data );
		}

		$formatted_price = apply_filters( 'yay_currency_get_approximately_formatted_price', $formatted_price, $product, $approximately_price_data, $apply_currency );

		return $formatted_price;
	}

	public static function get_formatted_approximately_price( $price_html, $formatted_price, $insert_condition = false ) {
		$approximate_price_info = Helper::approximate_price_info();
		if ( ! isset( $approximate_price_info['status'] ) || ! $approximate_price_info['status'] ) {
			return $price_html;
		}

		$approximately_label      = isset( $approximate_price_info['label'] ) ? wp_kses_post( $approximate_price_info['label'] ) : '';
		$approximately_price      = str_contains( $approximately_label, '%formatted-price%' ) ? str_replace( '%formatted-price%', $formatted_price, $approximately_label ) : $approximately_label . $formatted_price;
		$approximately_price_html = '<span class="yay-currency-approximately-price-by-country">' . $approximately_price . '</span>';
		if ( isset( $approximate_price_info['position'] ) && 'before' === $approximate_price_info['position'] ) {
			if ( $insert_condition ) {
				$price_html = preg_replace(
					'/(<span\s+class="woocommerce-Price-amount\s+amount[^"]*">)/',
					$approximately_price_html . '$1',
					$price_html
				);
			} else {
				$price_html = $approximately_price_html . $price_html;
			}
		} elseif ( $insert_condition ) {
			$price_html = preg_replace(
				'/(<\/span>)(?!.*<\/span>)/',
				'$1 ' . $approximately_price_html,
				$price_html
			);

		} else {
			$price_html = $price_html . $approximately_price_html;
		}

		return $price_html;
	}

	public static function display_approximately_converted_price( $apply_currency ) {
		return apply_filters( 'yay_currency_checkout_converted_approximately', true, $apply_currency );
	}

	public static function display_approximate_price_checkout_only() {
		return apply_filters( 'yay_currency_display_approximate_price_checkout_only', false );
	}

	// WooCommerce Block support

	public static function rest_api_endpoints() {
		$endpoints = array(
			'/wc/store/v1/batch', // Cart updates
			'/wc/store/v1/checkout', // Checkout submission and state updates (including payment method)
			'/wc/store/v1/cart/select-shipping-rate', // Shipping updates
			'/wc/store/v1/cart/update-customer', // Phone, email, billing updates
			'/wc/store/v1/checkout/update-order', // Order updates (may include payment method changes)
		);

		return apply_filters( 'yay_currency_block_rest_api_endpoints', $endpoints );
	}

	public static function detect_rest_api_doing() {
		if ( ! WC()->is_rest_api_request() ) {
			return false;
		}
		$rest_route = CountryHelper::get_rest_route_via_rest_api();
		if ( $rest_route && in_array( $rest_route, self::rest_api_endpoints(), true ) && isset( $_REQUEST['_locale'] ) ) {
			return true;
		}
		return false;
	}

	public static function detect_set_default_apply_currency_on_checkout_blocks() {

		if ( ! self::detect_rest_api_doing() ) {
			return false;
		}

		if ( CountryHelper::detect_force_country_by_checkout_blocks_page() ) {
			return true;
		}

		$currency_id_blocks = YayCurrencyHelper::get_currency_id_blocks_name();

		if ( ! get_option( $currency_id_blocks, false ) && CountryHelper::force_payment_country() ) {
			return true;
		}

		return false;

	}

	public static function is_checkout_blocks() {

		$flag = false;

		if ( self::detect_set_default_apply_currency_on_checkout_blocks() ) {
			$flag = true;
		}

		return apply_filters( 'yay_currency_is_checkout_blocks', $flag );

	}
}
