<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;

// Link plugin: https://woocommerce.com/products/product-add-ons/

class WooCommerceProductAddons {
	use SingletonTrait;

	private $apply_currency = array();

	public function __construct() {

		if ( ! defined( 'WC_PRODUCT_ADDONS_VERSION' ) ) {
			return;
		}

		$this->apply_currency = YayCurrencyHelper::detect_current_currency();

		add_action( 'yay_currency_set_cart_contents', array( $this, 'product_addons_set_cart_contents' ), 10, 4 );

		add_filter( 'yay_currency_price_options', array( $this, 'get_price_options' ), 10, 2 );

		add_filter( 'woocommerce_product_addons_option_price_raw', array( $this, 'custom_product_addons_option_price' ), 10, 2 );
		add_filter( 'woocommerce_product_addons_get_item_data', array( $this, 'custom_cart_item_addon_data' ), 10, 3 );
		// Place Order
		add_filter( 'woocommerce_product_addons_order_line_item_meta', array( $this, 'custom_order_line_item_meta' ), 10, 4 );

		add_filter( 'yay_currency_get_price_default_in_checkout_page', array( $this, 'get_price_default_in_checkout_page' ), 10, 2 );
		add_filter( 'yay_currency_product_price_3rd_with_condition', array( $this, 'get_price_with_options' ), 10, 2 );

		add_filter( 'yay_currency_get_price_options_by_cart_item', array( $this, 'get_price_options_by_cart_item' ), 10, 5 );
		add_filter( 'yay_currency_get_price_options_default_by_cart_item', array( $this, 'get_price_options_default_by_cart_item' ), 10, 4 );

		add_filter( 'yay_currency_get_fixed_product_price_3rd_plugin', array( $this, 'get_product_price_fixed_3rd_plugin' ), 10, 3 );

		// SUPPORT CACHING
		add_filter( 'yay_currency_caching_localize_args', array( $this, 'get_caching_localize_args' ), 10, 1 );
		add_filter( 'yay_currency_ajax_generate_results_3rd_plugins', array( $this, 'generate_results_3rd_plugins' ), 10, 3 );

	}

	public function product_addons_set_cart_contents( $cart_contents, $cart_item_key, $cart_item, $apply_currency ) {

		if ( isset( $cart_item['addons'] ) && ! empty( $cart_item['addons'] ) ) {
			$data_details                   = $this->calculate_price_options_by_cart_item( $cart_item, $apply_currency );
			$price_options_current_currency = $data_details['price_options_current_currency'];
			$price_options_default_currency = $data_details['price_options_default_currency'];

			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_original_price_options', $price_options_default_currency );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_price_options_by_currency', $price_options_current_currency );

			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_set_options_price_default', $price_options_default_currency );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_set_price_with_options_default', $data_details['product_price_with_option_default_currency'] );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_set_options_price', $price_options_current_currency );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_addon_set_price_with_options', $data_details['product_price_with_option_current_currency'] );

		}
	}

	public function get_price_options( $price_options, $product ) {
		$addon_set_options_price = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_addon_set_options_price' );
		return $addon_set_options_price ? $addon_set_options_price : $price_options;
	}

	public function custom_product_addons_option_price( $price, $option ) {
		if ( 'percentage_based' !== $option['price_type'] ) {
			$price = YayCurrencyHelper::calculate_price_by_currency( $price, false, $this->apply_currency );
		}
		return $price;
	}

	public function custom_formatted_item_fee( $args_price_option, $apply_currency, $addon ) {
		$item_fee = isset( $args_price_option['price_options_current_currency'] ) ? $args_price_option['price_options_current_currency'] : (float) $addon['price'];

		$formatted_item_fee = YayCurrencyHelper::format_price( $item_fee );

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {

			$item_fee = isset( $args_price_option['price_options_default_currency'] ) ? $args_price_option['price_options_default_currency'] : (float) $addon['price'];

			if ( YayCurrencyHelper::is_checkout_in_fallback() ) {
				$fallback_currency  = YayCurrencyHelper::get_fallback_currency();
				$formatted_item_fee = YayCurrencyHelper::calculate_price_by_currency_html( $fallback_currency, $item_fee );
			} else {
				$formatted_item_fee = wc_price( $item_fee );
			}
		}
		return $addon['value'] . ' (+ ' . $formatted_item_fee . ')';
	}

	public function custom_cart_item_addon_data( $addon_data, $addon, $cart_item ) {
		$addon_price = isset( $addon['price'] ) && ! empty( $addon['price'] ) ? $addon['price'] : false;
		if ( $addon_price ) {
			$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );
			$args           = $this->calculate_price_options_by_cart_item( $cart_item, $apply_currency, $addon['value'] );
			if ( ! $args ) {
				return $addon_data;
			}

			$cart_item_addon_data_value = $this->custom_formatted_item_fee( $args, $apply_currency, $addon );
			$addon_data['value']        = apply_filters( 'yay_currency_cart_item_addon_data', $cart_item_addon_data_value, $args, $cart_item, $addon, $apply_currency );

		}

		return $addon_data;

	}

	public function custom_order_line_item_meta( $meta_data, $addon, $item, $cart_item ) {

		$addon_price = isset( $addon['price'] ) && ! empty( $addon['price'] ) ? $addon['price'] : false;

		if ( ! $addon_price ) {
			return $meta_data;
		}

		$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );
		$args           = $this->calculate_price_options_by_cart_item( $cart_item, $apply_currency, $addon['value'] );

		if ( ! $args ) {
			return $meta_data;
		}

		$meta_data['value'] = $this->custom_formatted_item_fee( $args, $apply_currency, $addon );

		return $meta_data;

	}

	public function calculate_price_options_by_cart_item( $cart_item, $apply_currency, $addon_value = false ) {

		$price_options_default_currency = 0;
		$price_options_current_currency = 0;

		$product_id    = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
		$product_price = SupportHelper::get_product_price( $product_id );

		$product_price_by_currency = YayCurrencyHelper::calculate_price_by_currency( $product_price, false, $apply_currency );
		$product_price_by_currency = FixedPriceHelper::get_price_fixed_by_apply_currency( $cart_item['data'], $product_price_by_currency, $this->apply_currency );

		if ( ! $addon_value ) {
			foreach ( $cart_item['addons'] as $key => $addon ) {
				if ( isset( $addon['price_type'] ) ) {
					if ( 'percentage_based' !== $addon['price_type'] ) {
						$price_options_default_currency += (float) $addon['price'];
						$price_options_current_currency += YayCurrencyHelper::calculate_price_by_currency( $addon['price'], false, $apply_currency );
					} else {
						$price_options_default_currency += $product_price * ( $addon['price'] / 100 );
						$price_options_current_currency += $product_price_by_currency * ( $addon['price'] / 100 );
					}
				}
			}
		} else {
			$result = array_filter(
				$cart_item['addons'],
				function ( $option ) use ( $addon_value ) {
					if ( $option['value'] === $addon_value ) {
						return true;
					}
					return false;
				}
			);

			if ( $result ) {
				$addon = $result ? array_shift( $result ) : false;
				if ( 'percentage_based' !== $addon['price_type'] ) {
					$price_options_default_currency = (float) $addon['price'];
					$price_options_current_currency = YayCurrencyHelper::calculate_price_by_currency( $addon['price'], false, $apply_currency );
				} else {
					$price_options_default_currency = $product_price * ( $addon['price'] / 100 );
					$price_options_current_currency = $product_price_by_currency * ( $addon['price'] / 100 );
				}
			}
		}

		$data = array(
			'price_options_default_currency'             => $price_options_default_currency,
			'price_options_current_currency'             => $price_options_current_currency,
			'product_price_with_option_default_currency' => $product_price + $price_options_default_currency,
			'product_price_with_option_current_currency' => $product_price_by_currency + $price_options_current_currency,
		);
		return $data;
	}


	public function get_price_default_in_checkout_page( $price, $product ) {
		$addon_set_price_with_options_default = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_addon_set_price_with_options_default' );
		return $addon_set_price_with_options_default ? $addon_set_price_with_options_default : $price;
	}

	public function get_price_with_options( $price, $product ) {
		$addon_set_price_with_options = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_addon_set_price_with_options' );
		return $addon_set_price_with_options ? $addon_set_price_with_options : $price;
	}

	public function get_price_options_by_cart_item( $price_options, $cart_item, $product_id, $original_price, $apply_currency ) {
		$addons = isset( $cart_item['addons'] ) ? $cart_item['addons'] : false;
		if ( ! $addons ) {
			return $price_options;
		}

		$data_details = $this->calculate_price_options_by_cart_item( $cart_item, $this->apply_currency );

		return isset( $data_details['price_options_current_currency'] ) ? $data_details['price_options_current_currency'] : 0;
	}

	public function get_price_options_default_by_cart_item( $price_options, $cart_item, $product_id, $original_price ) {
		$addons = isset( $cart_item['addons'] ) ? $cart_item['addons'] : false;
		if ( ! $addons ) {
			return $price_options;
		}
		$data_details = $this->calculate_price_options_by_cart_item( $cart_item, $this->apply_currency );
		return isset( $data_details['price_options_default_currency'] ) ? $data_details['price_options_default_currency'] : 0;
	}

	public function get_product_price_fixed_3rd_plugin( $fixed_product_price, $product, $apply_currency ) {
		$addon_set_price_with_options = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_addon_set_price_with_options' );
		return $addon_set_price_with_options ? $addon_set_price_with_options : $fixed_product_price;
	}


	// CACHING INFO
	private function generate_addon_display_html( $product_id, $prefix ) {

		if ( ! class_exists( 'WC_Product_Addons_Helper' ) || ! defined( 'WC_PRODUCT_ADDONS_PLUGIN_PATH' ) || ! $product_id || ! isset( $GLOBALS['Product_Addon_Display'] ) ) {
			return false;
		}

		$html = '';
		global $product;
		$product = wc_get_product( $product_id );

		// We do not currently support grouped or external products.
		if ( ! is_object( $product ) || 'grouped' === $product->get_type() || 'external' === $product->get_type() ) {
			return $html;
		}
		$product_addon_display_class = $GLOBALS['Product_Addon_Display'];
		$product_addon_display_class->addon_scripts();
		$product_addons = \WC_Product_Addons_Helper::get_product_addons( $product_id, $prefix );
		ob_start();
		if ( is_array( $product_addons ) && count( $product_addons ) > 0 ) {
			echo '<div class="wc-pao-addons-container">';
			foreach ( $product_addons as $addon ) {
				if ( ! isset( $addon['field_name'] ) ) {
					continue;
				}

				wc_get_template(
					'addons/addon-start.php',
					array(
						'addon'               => $addon,
						'required'            => \WC_Product_Addons_Helper::is_addon_required( $addon ),
						'name'                => $addon['name'],
						'description'         => $addon['description'],
						'display_description' => \WC_Product_Addons_Helper::should_display_description( $addon ),
						'type'                => $addon['type'],
					),
					'woocommerce-product-addons',
					WC_PRODUCT_ADDONS_PLUGIN_PATH . '/templates/'
				);

				$method_name = 'get_' . $addon['type'] . '_html';
				if ( method_exists( $product_addon_display_class, $method_name ) ) {
					$product_addon_display_class->$method_name( $addon );
				}
				do_action( 'woocommerce_product_addons_get_' . $addon['type'] . '_html', $addon );

				wc_get_template(
					'addons/addon-end.php',
					array(
						'addon' => $addon,
					),
					'woocommerce-product-addons',
					WC_PRODUCT_ADDONS_PLUGIN_PATH . '/templates/'
				);
			}

			self::generate_html_totals( $product, $product_id );
			echo '<div class="validation_message woocommerce-info" id="required_addons_validation_message"></div>';
			echo '</div>';
		}

		return ob_get_clean();
	}

	private function generate_html_totals( $the_product, $post_id ) {

		if ( is_object( $the_product ) ) {
			$tax_display_mode = get_option( 'woocommerce_tax_display_shop' );
			$display_price    = 'incl' === $tax_display_mode ? wc_get_price_including_tax( $the_product ) : wc_get_price_excluding_tax( $the_product );
		} else {
			$display_price = '';
			$raw_price     = 0;
		}

		if ( 'no' === get_option( 'woocommerce_prices_include_tax' ) ) {
			$tax_mode  = 'excl';
			$raw_price = wc_get_price_excluding_tax( $the_product );
		} else {
			$tax_mode  = 'incl';
			$raw_price = wc_get_price_including_tax( $the_product );
		}

		$show_incomplete_subtotal = isset( get_option( 'product_addons_options' )['show-incomplete-subtotal'] ) ? get_option( 'product_addons_options' )['show-incomplete-subtotal'] : '';

		echo '<div id="product-addons-total" data-show-incomplete-sub-total="' . esc_attr( $show_incomplete_subtotal ) . '" data-show-sub-total="' . ( apply_filters( 'woocommerce_product_addons_show_grand_total', true, $the_product ) ? 1 : 0 ) . '" data-type="' . esc_attr( $the_product->get_type() ) . '" data-tax-mode="' . esc_attr( $tax_mode ) . '" data-tax-display-mode="' . esc_attr( $tax_display_mode ) . '" data-price="' . esc_attr( $display_price ) . '" data-raw-price="' . esc_attr( $raw_price ) . '" data-product-id="' . esc_attr( $post_id ) . '"></div>';
	}

	public function get_caching_localize_args( $localize_args ) {

		if ( is_singular( 'product' ) ) {
			global $post;
			if ( isset( $post->ID ) && $post->ID ) {
				$localize_args['product_id'] = $post->ID;
			}
		}

		$localize_args['wc_product_addons_active'] = 'yes';

		return $localize_args;
	}

	public function generate_results_3rd_plugins( $results, $data, $apply_currency ) {

		$product_id = isset( $data['product_id'] ) ? intval( sanitize_text_field( $data['product_id'] ) ) : false;
		if ( $product_id ) {
			$results['yay_currency_addon_display_html'] = self::generate_addon_display_html( $product_id, false );
		}
		return $results;
	}
}
