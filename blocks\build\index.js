(()=>{"use strict";const e=window.React,r=window.wp.i18n,c=window.wp.blocks,n=window.wp.blockEditor,t=window.wp.components;(0,c.registerBlockType)("yay-currency/currency-switcher",{title:(0,r.__)("Currency Switcher - YayCurrency","yay-currency"),icon:"index-card",category:"widgets",attributes:{currencyName:{type:"string",default:"United States dollar"},currencySymbol:{type:"string",default:"($)"},hyphen:{type:"string",default:" - "},currencyCode:{type:"string",default:"USD"},isShowFlag:{type:"boolean",default:!0},isShowCurrencyName:{type:"boolean",default:!0},isShowCurrencySymbol:{type:"boolean",default:!0},isShowCurrencyCode:{type:"boolean",default:!0},widgetSize:{type:"string",default:"small"}},edit:c=>{const{attributes:{currencyName:a,currencySymbol:l,hyphen:o,currencyCode:y,isShowFlag:s,isShowCurrencyName:u,isShowCurrencySymbol:i,isShowCurrencyCode:d,widgetSize:m},setAttributes:h}=c,{yayCurrencyPluginURL:w}=yayCurrencyGutenberg;return[(0,e.createElement)(n.InspectorControls,null,(0,e.createElement)(t.PanelBody,{title:(0,r.__)("Switcher elements","yay-currency")},(0,e.createElement)(t.CheckboxControl,{label:"Show flag",checked:s,onChange:e=>{h({isShowFlag:e})}}),(0,e.createElement)(t.CheckboxControl,{label:"Show currency name",checked:u,onChange:e=>{h({isShowCurrencyName:e})}}),(0,e.createElement)(t.CheckboxControl,{label:"Show currency symbol",checked:i,onChange:e=>{h({isShowCurrencySymbol:e})}}),(0,e.createElement)(t.CheckboxControl,{label:"Show currency code",checked:d,onChange:e=>{h({isShowCurrencyCode:e})}})),(0,e.createElement)(t.PanelBody,{title:(0,r.__)("Switcher size","yay-currency")},(0,e.createElement)(t.PanelRow,null,(0,e.createElement)(t.RadioControl,{selected:m,options:[{label:"Small",value:"small"},{label:"Medium",value:"medium"}],onChange:e=>{h({widgetSize:e})}})))),(0,e.createElement)("div",{className:`yay-currency-custom-select-wrapper ${m} ${!u&&"no-currency-name"} ${u&&!s&&!i&&!d&&"only-currency-name"}\n          ${u&&2===(()=>{const e=[];return[s,u,i,d].forEach((r=>{r&&e.push(r)})),e.length})()&&"only-currency-name-and-something"}\n          `},(0,e.createElement)("div",{className:"yay-currency-custom-select"},(0,e.createElement)("div",{className:`yay-currency-custom-select__trigger ${m}`},(0,e.createElement)("div",{className:"yay-currency-custom-selected-option"},s&&(0,e.createElement)("span",{class:`yay-currency-flag selected ${m}`,style:{backgroundImage:`url(${w}assets/dist/flags/us.svg)`,backgroundSize:"cover",backgroundRepeat:"no-repeat"}}),(0,e.createElement)("span",{className:"yay-currency-selected-option"},(h(u?{currencyName:"United States dollar"}:{currencyName:""}),h(i?u?{currencySymbol:"($)"}:{currencySymbol:"$ "}:{currencySymbol:""}),h(d?{currencyCode:"USD"}:{currencyCode:""}),h(u&&d?{hyphen:" - "}:{hyphen:""}),`${a} ${l}${o}${y}`))),(0,e.createElement)("div",{className:"yay-currency-custom-arrow"}))))]},save:()=>null})})();