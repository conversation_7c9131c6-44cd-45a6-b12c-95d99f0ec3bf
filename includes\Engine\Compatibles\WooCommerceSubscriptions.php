<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;

use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\Helper;

defined( 'ABSPATH' ) || exit;

class WooCommerceSubscriptions {
	use SingletonTrait;

	private $converted_currency;
	private $apply_currency;
	private $default_currency;
	private $is_dis_checkout_diff_currency;

	private $parent_rate_fee = 1;

	public function __construct() {

		if ( ! class_exists( 'WC_Subscriptions' ) ) {
			return;
		}

		$this->default_currency = Helper::default_currency_code();

		// Admin
		add_filter( 'yay_currency_admin_localize_args', array( $this, 'yay_currency_admin_localize_args' ), 10, 1 );
		add_action( 'wp_ajax_yay_subscription_admin_reports_data', array( $this, 'custom_subscription_admin_reports_data' ) );
		add_action( 'wp_ajax_nopriv_yay_subscription_admin_reports_data', array( $this, 'custom_subscription_admin_reports_data' ) );
		add_filter( 'wcs_reports_subscription_events_args', array( $this, 'wcs_reports_subscription_events_args' ), 10, 1 );
		add_filter( 'wcs_reports_subscription_events_sign_up_data', array( $this, 'wcs_reports_subscription_events_sign_up_data' ), 10, 2 );

		if ( isset( $_REQUEST['page'] ) && 'wc-reports' === $_REQUEST['page'] && isset( $_REQUEST['tab'] ) && 'subscriptions' === $_REQUEST['tab'] ) {
			add_filter( 'woocommerce_currency_symbol', array( $this, 'custom_admin_woocommerce_currency_symbol' ), 10, 2 );
		}

		$this->converted_currency = YayCurrencyHelper::converted_currency();
		$this->apply_currency     = YayCurrencyHelper::detect_current_currency();

		if ( ! $this->apply_currency ) {
			return;
		}

		$this->is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $this->apply_currency );

		add_action( 'yay_currency_set_cart_contents', array( $this, 'product_addons_set_cart_contents' ), 10, 4 );

		add_filter( 'woocommerce_subscriptions_product_sign_up_fee', array( $this, 'custom_subscription_sign_up_fee' ), 10, 2 );
		add_filter( 'woocommerce_subscriptions_product_price_string', array( $this, 'custom_subscription_price_string' ), 10, 3 );
		add_filter( 'woocommerce_subscriptions_price_string', array( $this, 'custom_subscription_price_string' ), 10, 3 );

		add_filter( 'yay_currency_coupon_types', array( $this, 'yay_currency_coupon_types' ), 10, 2 );

		if ( $this->is_dis_checkout_diff_currency ) {
			// Recurring cart shipping
			add_filter( 'wcs_cart_totals_shipping_method', array( $this, 'wcs_cart_totals_shipping_method' ), 10, 3 );
			add_filter( 'wcs_cart_totals_shipping_method_price_label', array( $this, 'wcs_cart_totals_shipping_price_label' ), 10, 3 );
			// Recurring cart subtotal
			add_filter( 'woocommerce_cart_subscription_string_details', array( $this, 'woocommerce_cart_subscription_string_details' ), 10, 2 );
			// Recurring cart total tax
			add_filter( 'wcs_recurring_cart_itemized_tax_totals_html', array( $this, 'wcs_recurring_cart_itemized_tax_totals_html' ), 10, 4 );
			// Recurring cart total
			add_filter( 'wcs_cart_totals_order_total_html', array( $this, 'woocommerce_cart_totals_order_total_html' ), 10, 2 );
		}

		// After the renewal order is created on checkout, set the renewal order cart item data now that we have an order. Must be hooked on before WCS_Cart_Renewal->set_order_item_id(), in order for the line item ID set by that function to be correct.
		add_filter( 'yay_currency_product_price_3rd_with_condition', array( $this, 'subscription_get_price_renew' ), 10, 2 );

		// Define filter get price default (when disable Checkout in different currency option)
		add_filter( 'yay_currency_get_price_default_in_checkout_page', array( $this, 'get_price_default_in_checkout_page' ), 10, 2 );
		add_filter( 'yay_currency_checkout_product_sign_up_fee', array( $this, 'checkout_product_sign_up_fee' ), 10, 3 );

		add_filter( 'yay_currency_get_product_price_by_cart_item', array( $this, 'get_product_price_by_cart_item' ), 10, 3 );
		add_filter( 'yay_currency_get_product_price_default_by_cart_item', array( $this, 'get_product_price_default_by_cart_item' ), 10, 2 );

		add_filter( 'yay_currency_get_cart_item_extra_price_3rd', array( $this, 'get_cart_item_extra_price_3rd' ), 10, 3 );

		add_filter( 'yay_currency_get_fixed_cart_subtotal_3rd_plugin', array( $this, 'get_fixed_cart_subtotal_3rd_plugin' ), 10, 2 );

		// Add Fixed Sing-up fee
		add_action( 'yay_currency_add_fixed_prices_simple_product', array( $this, 'add_fixed_prices_simple_product' ), 10, 3 );
		add_action( 'yay_currency_custom_save_fixed_prices_single_product', array( $this, 'custom_save_fixed_prices_single_product' ), 10, 3 );

		add_action( 'yay_currency_add_fixed_prices_variable_product', array( $this, 'add_fixed_prices_variable_product' ), 10, 3 );
		add_action( 'yay_currency_custom_save_fixed_prices_variable_product', array( $this, 'custom_save_fixed_prices_variable_product' ), 10, 3 );

		add_filter( 'yay_currency_converted_product_subtotal_sign_up_fee', array( $this, 'custom_converted_product_subtotal_sign_up_fee' ), 10, 3 );

		// Custom Renewals Order Symbols
		add_action( 'woocommerce_subscriptions_email_order_details', array( $this, 'set_email_order_id' ), PHP_INT_MAX, 4 );
		add_filter( 'woocommerce_currency', array( $this, 'renewal_email_woocommerce_currency' ), PHP_INT_MAX, 1 );
		add_filter( 'woocommerce_currency_symbol', array( $this, 'renewal_email_woocommerce_currency_symbol' ), PHP_INT_MAX, 2 );

		// Renewals Action
		add_filter( 'wcs_new_order_created', array( $this, 'wcs_new_order_created' ), 999, 3 );

		// Caching
		add_filter( 'yay_currency_caching_localize_args', array( $this, 'yay_currency_caching_localize_args' ), 10, 1 );
	}

	public function yay_currency_admin_localize_args( $localize_args ) {
		$localize_args['woo_subscriptions'] = 'yes';
		return $localize_args;
	}

	// GET FORMAT PRICE WITH DEFAULT CURRENCY  APPLY FOR FRONTEND
	protected function convert_price_to_default_currency( $price, $converted_currency ) {
		$apply_currency  = reset( $converted_currency );
		$price           = YayCurrencyHelper::format_price_currency( $price, $apply_currency );
		$currency_symbol = YayCurrencyHelper::get_symbol_by_currency_code( $this->default_currency );
		$format          = YayCurrencyHelper::format_currency_symbol( $apply_currency );
		$formatted_price = sprintf( $format, '<span class="woocommerce-Price-currencySymbol">' . $currency_symbol . '</span>', $price );
		$return          = '<span class="woocommerce-Price-amount amount"><bdi>' . $formatted_price . '</bdi></span>';
		return $return;
	}

	public function custom_subscription_admin_reports_data() {
		$nonce = isset( $_POST['_nonce'] ) ? sanitize_text_field( $_POST['_nonce'] ) : false;

		if ( ! $nonce || ! wp_verify_nonce( sanitize_key( $nonce ), 'yay-currency-admin-nonce' ) ) {
			wp_send_json_error( array( 'message' => __( 'Nonce invalid', 'yay-currency' ) ) );
		}

		$converted_currency = YayCurrencyHelper::converted_currency();

		$signup_revenue  = self::recalculate_signup_revenue( $converted_currency );
		$renewal_revenue = self::recalculate_renewal_revenue( $converted_currency );

		wp_send_json_success(
			array(
				'signup_revenue'  => self::convert_price_to_default_currency( $signup_revenue, $converted_currency ),
				'renewal_revenue' => self::convert_price_to_default_currency( $renewal_revenue, $converted_currency ),
			)
		);
	}

	public function wcs_reports_subscription_events_args( $args ) {
		if ( ! isset( $args['no_cache'] ) || ! $args['no_cache'] ) {
			$args['no_cache'] = true;
		}

		return $args;
	}

	public function wcs_reports_subscription_events_sign_up_data( $results, $args ) {
		if ( $results ) {

			$currency = isset( $_REQUEST['currency'] ) ? sanitize_text_field( $_REQUEST['currency'] ) : $this->default_currency;

			foreach ( $results as $key => $result ) {

				$subscription_ids = isset( $result->subscription_ids ) && ! empty( $result->subscription_ids ) ? explode( ',', $result->subscription_ids ) : false;

				if ( $subscription_ids && isset( $result->signup_totals ) ) {
					$signup_totals = 0;
					foreach ( $subscription_ids as $subscription_id ) {
						$order_id = wc_get_order( $subscription_id )->get_parent_id();
						$order    = wc_get_order( $order_id );
						if ( ! $order ) {
							continue;
						}
						if ( ! $currency || empty( $currency ) ) {
							$order_rate = Helper::get_yay_currency_order_rate( $order_id, $order );
							if ( ! $order_rate ) {
								continue;
							}
							$signup_totals += $order->get_total() / $order_rate;
						} else {
							if ( $currency !== $order->get_currency() ) {
								continue;
							}
							$signup_totals += $order->get_total();
						}
					}
					$results[ $key ]->signup_totals = $signup_totals;
				}
			}
		}
		return $results;
	}

	public function custom_admin_woocommerce_currency_symbol( $currency_symbol, $currency ) {
		$selected_currency = isset( $_GET['currency'] ) ? sanitize_text_field( $_GET['currency'] ) : Helper::default_currency_code();
		$currency_symbol   = YayCurrencyHelper::get_symbol_by_currency_code( $selected_currency );
		return $currency_symbol;
	}

	protected function get_subscription_renewal_resubscribe_price_info( $cart_item, $parent_order_id, $apply_currency ) {

		$parent_order_apply_currency = YayCurrencyHelper::get_order_currency_by_order_id( $parent_order_id, $this->converted_currency );

		if ( ! $parent_order_apply_currency ) {
			return false;
		}

		$order_renewal_price = $cart_item['data']->get_price( 'edit' );

		$args = array(
			'subscription_price_default'        => $order_renewal_price,
			'subscription_order_apply_currency' => $parent_order_apply_currency,
		);

		$parent_rate_fee = apply_filters( 'yay_currency_order_rate', $parent_order_id, 1, $this->converted_currency );

		if ( $this->default_currency !== $parent_order_apply_currency['currency'] ) {
			if ( $parent_rate_fee ) {
				$args['subscription_price_default'] = (float) $order_renewal_price / $parent_rate_fee;
			}
		}

		if ( apply_filters( 'yay_currency_keep_first_price_subscription', true ) && ( $parent_order_apply_currency['currency'] === $apply_currency['currency'] ) ) {
			$renewal_price_by_current_currency = $order_renewal_price;
		} else {
			$renewal_price_by_current_currency = YayCurrencyHelper::calculate_price_by_currency( $args['subscription_price_default'], false, $apply_currency );
			$renewal_price_by_current_currency = FixedPriceHelper::get_price_fixed_by_apply_currency( $cart_item['data'], $renewal_price_by_current_currency, $apply_currency );
		}

		$args['subscription_price_current_currency'] = $renewal_price_by_current_currency;

		return $args;

	}

	public function product_addons_set_cart_contents( $cart_contents, $key, $cart_item, $apply_currency ) {

		if ( isset( $cart_item['subscription_renewal'] ) && isset( $cart_item['subscription_renewal']['renewal_order_id'] ) ) {

			$subscription_renewal_args = self::get_subscription_renewal_resubscribe_price_info( $cart_item, $cart_item['subscription_renewal']['renewal_order_id'], $apply_currency );

			if ( $subscription_renewal_args ) {
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_renewal_price_original', $subscription_renewal_args['subscription_price_current_currency'] );
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_renewal_price_original_default', $subscription_renewal_args['subscription_price_default'] );
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_renewal_price_currency', $subscription_renewal_args['subscription_order_apply_currency']['currency'] );
			}
		}

		if ( isset( $cart_item['subscription_resubscribe'] ) && isset( $cart_item['subscription_resubscribe']['subscription_id'] ) ) {

			$subscription_renewal_args = self::get_subscription_renewal_resubscribe_price_info( $cart_item, $cart_item['subscription_resubscribe']['subscription_id'], $apply_currency );

			if ( $subscription_renewal_args ) {
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_resubscribe_price_original', $subscription_renewal_args['subscription_price_current_currency'] );
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_resubscribe_price_original_default', $subscription_renewal_args['subscription_price_default'] );
				SupportHelper::set_cart_item_objects_property( WC()->cart->cart_contents[ $key ]['data'], 'subscription_resubscribe_price_currency', $subscription_renewal_args['subscription_order_apply_currency']['currency'] );
			}
		}

	}

	public function subscription_get_price_renew( $price, $product ) {

		$renewal_price_currency = SupportHelper::get_cart_item_objects_property( $product, 'subscription_renewal_price_currency' );

		if ( $renewal_price_currency && ! empty( $renewal_price_currency ) ) {
			$renewal_price_original = SupportHelper::get_cart_item_objects_property( $product, 'subscription_renewal_price_original' );
			if ( $renewal_price_original ) {
				return $renewal_price_original;
			}
		}

		$resubscribe_price_currency = SupportHelper::get_cart_item_objects_property( $product, 'subscription_resubscribe_price_currency' );

		if ( $resubscribe_price_currency && ! empty( $resubscribe_price_currency ) ) {
			$resubscribe_price_original = SupportHelper::get_cart_item_objects_property( $product, 'subscription_resubscribe_price_original' );
			if ( $resubscribe_price_original ) {
				return $resubscribe_price_original;
			}
		}

		return $price;
	}

	public function get_price_default_in_checkout_page( $price, $product ) {
		$renewal_price_original = SupportHelper::get_cart_item_objects_property( $product, 'subscription_renewal_price_original_default' );
		if ( $renewal_price_original ) {
			return $renewal_price_original;
		}

		$resubscribe_price_original_default = SupportHelper::get_cart_item_objects_property( $product, 'subscription_resubscribe_price_original_default' );
		if ( $resubscribe_price_original_default ) {
			return $resubscribe_price_original_default;
		}

		return $price;
	}

	public function checkout_product_sign_up_fee( $flag, $product, $apply_currency ) {
		if ( class_exists( 'WC_Subscriptions_Product' ) && \WC_Subscriptions_Product::is_subscription( $product ) ) {
			$original_price = (float) $product->get_price( 'edit' );
			$sign_up_price  = \WC_Subscriptions_Product::get_sign_up_fee( $product );
			// Price for signup fee not apply fixed
			if ( $sign_up_price ) {
				$sign_up_price = (float) $product->get_price();
				if ( $sign_up_price && $original_price !== $sign_up_price ) {
					return true;
				}
			}
			return false;
		}

		return $flag;
	}

	public function get_period_string( $cart_item_key ) {
		if ( str_contains( $cart_item_key, 'daily' ) ) {
			return 'day';
		}
		if ( str_contains( $cart_item_key, 'weekly' ) ) {
			return 'week';
		}
		if ( str_contains( $cart_item_key, 'yearly' ) ) {
			return 'year';
		}
		if ( str_contains( $cart_item_key, 'monthly' ) ) {
			return 'month';
		}
	}

	public function custom_subscription_sign_up_fee( $sign_up_fee, $product ) {
		$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );

		$sign_up_fee_apply_currency = YayCurrencyHelper::calculate_price_by_currency( $sign_up_fee, false, $apply_currency );
		$sign_up_fee_apply_currency = self::get_fixed_signup_fee_price_by_currency( $product, $sign_up_fee_apply_currency, $apply_currency );

		if ( SupportHelper::detect_ajax_caching_doing() ) {
			return $sign_up_fee_apply_currency;
		}

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) || is_admin() || doing_filter( 'yay_currency_get_product_price_default_by_cart_item' ) ) {
			return $sign_up_fee;
		}

		return $sign_up_fee_apply_currency;
	}

	public function enable_fixed_or_rounding( $apply_currency ) {
		$fixed_price    = FixedPriceHelper::is_set_fixed_price();
		$rounding_price = 'disabled' !== $apply_currency['roundingType'];
		return $fixed_price || $rounding_price;
	}

	public function custom_subscription_price_string( $price_string, $product, $args ) {

		if ( is_checkout() ) {
			return $price_string;
		}

		$quantity = 1;

		if ( is_cart() ) {
			foreach ( WC()->cart->get_cart() as $cart_item ) {

				$item = $cart_item['data'];

				if ( ! empty( $item ) ) {
						$quantity = $cart_item['quantity'];
				}
			}
		}

		$signup_fee_original  = get_post_meta( $product->get_id(), '_subscription_sign_up_fee', true );
		$signup_fee_original  = $signup_fee_original ? $signup_fee_original : 0;
		$converted_signup_fee = YayCurrencyHelper::calculate_price_by_currency( $signup_fee_original, false, $this->apply_currency ) * $quantity;
		$formatted_signup_fee = YayCurrencyHelper::format_price( $converted_signup_fee );

		$custom_sign_up_fee = ( isset( $args['sign_up_fee'] ) && 0 !== floatval( $signup_fee_original ) ) ? __( ' and a ' . wp_kses_post( $formatted_signup_fee ) . ' sign-up fee', 'woocommerce' ) : '';

		if ( in_array( $product->get_type(), array( 'variable-subscription' ) ) ) {
			$min_price = $product->get_variation_price( 'min', true );

			if ( $this->enable_fixed_or_rounding( $this->apply_currency ) ) {
				$variation_ids = $product->get_children();
				foreach ( $variation_ids as $id ) {
					$custom_fixed_prices = get_post_meta( $id, 'yay_currency_custom_fixed_prices', true );
					if ( $custom_fixed_prices && ! empty( $custom_fixed_prices[ $this->apply_currency['currency'] ]['price'] ) ) {
						$min_price = min( $min_price, $custom_fixed_prices[ $this->apply_currency['currency'] ]['price'] );
					}
				}
			}
			$formatted_price            = YayCurrencyHelper::format_price( $min_price );
			$price_string_no_html       = strip_tags( $price_string );
			$price_string_no_fee_string = substr( $price_string_no_html, 0, strpos( $price_string_no_html, 'and' ) ); // remove default sign-up fee string
			$start_index_to_cut_string  = strpos( $price_string_no_html, ' /' ) ? strpos( $price_string_no_html, ' /' ) : ( strpos( $price_string_no_html, ' every' ) ? strpos( $price_string_no_html, ' every' ) : strpos( $price_string_no_html, ' for' ) );
			$interval_subscrition       = substr( empty( $price_string_no_fee_string ) ? $price_string_no_html : $price_string_no_fee_string, $start_index_to_cut_string ); // get default interval subscrition (ex: /month or every x days...)
			$price_string               = __( 'From: ', 'woocommerce' ) . $formatted_price . $interval_subscrition . $custom_sign_up_fee;
		}

		return $price_string;
	}

	public function yay_currency_coupon_types( $coupon_types, $coupon ) {
		if ( $coupon_types && ! in_array( 'recurring_percent', $coupon_types ) ) {
			array_push( $coupon_types, 'recurring_percent' );
		}
		return $coupon_types;
	}

	public function wcs_cart_totals_shipping_method( $label, $method, $cart ) {
		if ( is_checkout() ) {
			if ( 'Free shipping' === $label ) {
				return $label;
			}

			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $label;
			}
			$shipping_fee                             = (float) $method->cost;
			$converted_shipping_fee                   = YayCurrencyHelper::calculate_price_by_currency( $shipping_fee, true, $this->apply_currency );
			$formatted_shipping_fee                   = YayCurrencyHelper::format_price( $converted_shipping_fee );
			$shipping_method_label                    = $method->label;
			$formatted_fallback_currency_shipping_fee = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $shipping_fee );

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				$label = '' . $shipping_method_label . ': ' . $formatted_fallback_currency_shipping_fee . ' / ' . $this->get_period_string( $cart->recurring_cart_key );
			} else {
				$formatted_shipping_fee_html = YayCurrencyHelper::converted_approximately_html( $formatted_shipping_fee );
				$label                       = '' . $shipping_method_label . ': ' . $formatted_fallback_currency_shipping_fee . $formatted_shipping_fee_html . ' / ' . $this->get_period_string( $cart->recurring_cart_key );
			}
		}
		return $label;
	}

	public function wcs_cart_totals_shipping_price_label( $price_label, $method, $cart ) {
		if ( is_checkout() && 0 < $method->cost ) {
			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $price_label;
			}
			$display_prices_include_tax = wcs_is_woocommerce_pre( '3.3' ) ? ( 'incl' === WC()->cart->tax_display_cart ) : WC()->cart->display_prices_including_tax();
			if ( ! $display_prices_include_tax ) {
				$shipping_fee = (float) $method->cost;
			} else {
				$shipping_fee = (float) $method->cost + $method->get_shipping_tax();
			}
			$converted_shipping_fee = YayCurrencyHelper::calculate_price_by_currency( $shipping_fee, true, $this->apply_currency );
			$formatted_shipping_fee = YayCurrencyHelper::format_price( $converted_shipping_fee );

			$formatted_fallback_currency_shipping_fee = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $shipping_fee );
			$converted_approximately                  = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				$price_label = $formatted_fallback_currency_shipping_fee . ' / ' . $this->get_period_string( $cart->recurring_cart_key );
			} else {
				$formatted_shipping_fee_html = YayCurrencyHelper::converted_approximately_html( $formatted_shipping_fee );
				$price_label                 = $formatted_fallback_currency_shipping_fee . $formatted_shipping_fee_html . ' / ' . $this->get_period_string( $cart->recurring_cart_key );
			}

			if ( $method->get_shipping_tax() > 0 && ! $cart->prices_include_tax ) {
				$price_label .= ' <small>' . WC()->countries->inc_tax_or_vat() . '</small>';
			}
		}

		return $price_label;
	}

	public function woocommerce_cart_subscription_string_details( $data, $cart ) {
		if ( is_checkout() ) {
			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $data;
			}
			$recurring_cart_amount                   = $cart->get_displayed_subtotal();
			$convert_recurring_cart_amount           = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $recurring_cart_amount );
			$subtotal                                = $this->get_subtotal_price_sign_up_fee( $this->apply_currency, true );
			$formatted_convert_recurring_cart_amount = YayCurrencyHelper::format_price( $subtotal );

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				$data['recurring_amount'] = $convert_recurring_cart_amount;
			} else {
				$formatted_convert_recurring_cart_amount_html = YayCurrencyHelper::converted_approximately_html( $formatted_convert_recurring_cart_amount );
				$data['recurring_amount']                     = $convert_recurring_cart_amount . $formatted_convert_recurring_cart_amount_html;
			}
		}
		return $data;

	}

	public function get_subtotal_price_sign_up_fee( $apply_currency, $recurring_cart_tax = false ) {
		$subtotal      = 0;
		$cart_contents = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $key => $cart_item ) {
			$product  = $cart_item['data'];
			$quantity = $cart_item['quantity'];

			if ( $recurring_cart_tax ) {
				if ( ! class_exists( 'WC_Subscriptions_Product' ) || ! \WC_Subscriptions_Product::is_subscription( $product ) ) {
					continue;
				}
			}

			$product_id    = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
			$product_price = SupportHelper::get_product_price( $product_id, $apply_currency );
			$product_price = apply_filters( 'yay_currency_get_product_price_subscription_by_cart_item', $product_price, $cart_item, $apply_currency );
			$price_options = apply_filters( 'yay_currency_get_price_options_by_cart_item', 0, $cart_item, $product_id, $product_price, $apply_currency );
			$subtotal      = $subtotal + ( $product_price + $price_options ) * $quantity;
		}

		return $subtotal;

	}

	public function get_recurring_shiping_total() {
		$recurring_total = 0;
		if ( isset( WC()->cart->recurring_carts ) && ! empty( WC()->cart->recurring_carts ) ) {
			foreach ( WC()->cart->recurring_carts as $cart ) {
				$recurring_total += $cart->shipping_total;
			}
		}

		return $recurring_total;
	}

	public function get_recurring_cart_total_tax( $recurring_tax ) {
		$total_tax         = 0;
		$subtotal          = $this->get_subtotal_price_sign_up_fee( $this->apply_currency, true );
		$tax_rate          = \WC_Tax::_get_tax_rate( $recurring_tax->tax_rate_id );
		$shipping_total    = YayCurrencyHelper::calculate_price_by_currency( $this->get_recurring_shiping_total(), false, $this->apply_currency );
		$tax_rate_shipping = isset( $tax_rate['tax_rate_shipping'] ) ? (int) $tax_rate['tax_rate_shipping'] : false;
		$tax_amount        = (float) $tax_rate['tax_rate'];
		if ( $tax_rate_shipping ) {
			$total_tax = ( $subtotal + $shipping_total ) * $tax_amount / 100;
		} else {
			$total_tax = $subtotal * $tax_amount / 100;
		}
		return $total_tax;
	}

	public function wcs_recurring_cart_itemized_tax_totals_html( $amount_html, $recurring_cart, $recurring_code, $recurring_tax ) {
		if ( is_checkout() ) {
			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $amount_html;
			}
			$amount = $recurring_tax->amount;
			if ( $this->enable_fixed_or_rounding( $this->apply_currency ) ) {
				$total_tax = $this->get_recurring_cart_total_tax( $recurring_tax );
			} else {
				$total_tax = YayCurrencyHelper::calculate_price_by_currency( $amount, false, $this->apply_currency );
			}

			$converted_tax_amount           = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $amount );
			$formatted_converted_tax_amount = YayCurrencyHelper::format_price( $total_tax );

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				$amount_html = $converted_tax_amount . ' / ' . $this->get_period_string( $recurring_cart->recurring_cart_key );
			} else {
				$formatted_converted_tax_amount_html = YayCurrencyHelper::converted_approximately_html( $formatted_converted_tax_amount );
				$amount_html                         = $converted_tax_amount . $formatted_converted_tax_amount_html . ' / ' . $this->get_period_string( $recurring_cart->recurring_cart_key );
			}
		}
		return $amount_html;
	}

	public function get_recurring_cart_total_include_tax_shipping() {
		$total_tax      = 0;
		$subtotal       = $this->get_subtotal_price_sign_up_fee( $this->apply_currency, true );
		$shipping_total = YayCurrencyHelper::calculate_price_by_currency( $this->get_recurring_shiping_total(), false, $this->apply_currency );
		foreach ( WC()->cart->get_taxes() as $tax_id => $tax_total ) {
			foreach ( WC()->cart->recurring_carts as $recurring_cart_key => $recurring_cart ) {
				foreach ( $recurring_cart->get_tax_totals() as $recurring_code => $recurring_tax ) {
					if ( ! isset( $recurring_tax->tax_rate_id ) || $recurring_tax->tax_rate_id !== $tax_id ) {
						continue;
					}
					$total_tax += $this->get_recurring_cart_total_tax( $recurring_tax );
				}
			}
		}
		return $subtotal + $total_tax + $shipping_total;
	}

	public function get_recurring_cart_total() {
		$recurring_total = 0;
		if ( ! empty( WC()->cart->recurring_carts ) ) {
			foreach ( WC()->cart->recurring_carts as $recurring_cart ) {
				$recurring_total += $recurring_cart->total;
			}
		}
		return $recurring_total;
	}

	public function woocommerce_cart_totals_order_total_html( $order_total_html, $cart ) {
		if ( is_checkout() ) {
			$currencies_data = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $currencies_data ) ) {
				return $order_total_html;
			}
			$recurring_total                         = $this->get_recurring_cart_total();
			$convert_recurring_total                 = YayCurrencyHelper::calculate_price_by_currency_html( $currencies_data['fallback_currency'], $recurring_total );
			$recurring_total_apply_currency          = $this->enable_fixed_or_rounding( $this->apply_currency ) ? $this->get_recurring_cart_total_include_tax_shipping() : YayCurrencyHelper::calculate_price_by_currency( $recurring_total, false, $this->apply_currency );
			$formatted_convert_recurring_cart_amount = YayCurrencyHelper::format_price( $recurring_total_apply_currency );

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				$order_total_html = '<strong>' . $convert_recurring_total . '</strong> / ' . $this->get_period_string( $cart->recurring_cart_key );
			} else {
				$formatted_convert_recurring_cart_amount_html = YayCurrencyHelper::converted_approximately_html( $formatted_convert_recurring_cart_amount );
				$order_total_html                             = '<strong>' . $convert_recurring_total . $formatted_convert_recurring_cart_amount_html . '</strong> / ' . $this->get_period_string( $cart->recurring_cart_key );
			}
		}

		return $order_total_html;
	}

	public function get_product_price_by_cart_item( $price, $cart_item, $apply_currency ) {
		$renewal_price_original     = SupportHelper::get_cart_item_objects_property( $cart_item['data'], 'subscription_renewal_price_original' );
		$resubscribe_price_original = SupportHelper::get_cart_item_objects_property( $cart_item['data'], 'subscription_resubscribe_price_original' );
		if ( $renewal_price_original || $resubscribe_price_original ) {
			$price = $resubscribe_price_original ? $resubscribe_price_original : $renewal_price_original;
			return $price;
		}

		if ( class_exists( 'WC_Subscriptions_Product' ) ) {
			$sign_up_fee = \WC_Subscriptions_Product::get_sign_up_fee( $cart_item['data'] );
			$sign_up_fee = YayCurrencyHelper::calculate_price_by_currency( $sign_up_fee, false, $apply_currency );
			$sign_up_fee = self::get_fixed_signup_fee_price_by_currency( $cart_item['data'], $sign_up_fee, $this->apply_currency );
			$price       = $price + $sign_up_fee;
		}

		return $price;
	}

	public function get_product_price_default_by_cart_item( $price, $cart_item ) {
		$renewal_price_original     = SupportHelper::get_cart_item_objects_property( $cart_item['data'], 'subscription_renewal_price_original_default' );
		$resubscribe_price_original = SupportHelper::get_cart_item_objects_property( $cart_item['data'], 'subscription_resubscribe_price_original_default' );

		if ( ( $renewal_price_original || $resubscribe_price_original ) ) {
			$price = isset( $resubscribe_price_original ) ? $resubscribe_price_original : $renewal_price_original;
			return $price;
		}

		if ( class_exists( 'WC_Subscriptions_Product' ) ) {
			$sign_up_fee = \WC_Subscriptions_Product::get_sign_up_fee( $cart_item['data'] );
			$price       = $price + $sign_up_fee;
		}

		return $price;
	}



	public function get_cart_item_extra_price_3rd( $product_price, $cart_item, $apply_currency ) {
		if ( class_exists( 'WC_Subscriptions_Product' ) ) {
			$sign_up_fee   = \WC_Subscriptions_Product::get_sign_up_fee( $cart_item['data'] );
			$sign_up_fee   = YayCurrencyHelper::calculate_price_by_currency( $sign_up_fee, false, $apply_currency );
			$sign_up_fee   = self::get_fixed_signup_fee_price_by_currency( $cart_item['data'], $sign_up_fee, $this->apply_currency );
			$product_price = $product_price + $sign_up_fee;
		}

		return $product_price;
	}

	public function get_fixed_cart_subtotal_3rd_plugin( $cart_subtotal, $apply_currency ) {

		$subtotal = $this->get_subtotal_price_sign_up_fee( $apply_currency );
		if ( $subtotal ) {
			return $subtotal;
		}
		return $cart_subtotal;

	}
	// RELATE FIXED SIGNUP FEE

	public function add_fixed_prices_simple_product( $yay_currencies, $product_id, $product_object ) {
		if ( ! $product_object || ! is_object( $product_object ) && 'subscription' !== $product_object->get_type() ) {
			return;
		}
		$custom_sign_up_fee_prices = get_post_meta( $product_id, 'yay_currency_custom_fixed_sign_up_fee_prices', true );
		?>
		<div class="yay-currency-product-custom-fixed-prices-simple">
			<div class="yay-currency-fixed-price-checkbox-wrapper">
				<h3><?php esc_html_e( 'Fixed Sign-up fee price for each currency', 'yay-currency' ); ?></h3>
			</div>
			<div class="yay-currency-fixed-prices-input-wrapper">
				<i class="checkbox-sub-text"><?php esc_html_e( 'You can manually set fixed Sign-up fee price for each currency. Leave blank to get the rate automatically.', 'yay-currency' ); ?></i>
				<?php
				foreach ( $yay_currencies as $currency ) {
					if ( $this->default_currency === $currency->post_title ) {
						continue;
					}
					echo '<div class="yay-currency-fixed-prices-input">';
					woocommerce_wp_text_input(
						array(
							'id'          => "signup_fee_{$currency->post_title}",
							'placeholder' => 'Auto',
							'label'       => 'Sign-up fee (' . $currency->post_title . ')',
							'desc_tip'    => 'true',
							'value'       => ! empty( $custom_sign_up_fee_prices[ $currency->post_title ] ) ? $custom_sign_up_fee_prices[ $currency->post_title ]['signup_fee_price'] : '',
						)
					);
					echo '</div>';
				}
				?>
			</div>
		</div>
		<?php

	}

	public function save_custom_sign_up_fee_prices_product( $currencies = array(), $variation_idx = null ) {
		$nonce = isset( $_POST['yay-custom-fixed-prices-nonce'] ) ? sanitize_text_field( $_POST['yay-custom-fixed-prices-nonce'] ) : false;
		if ( ! $nonce && ! wp_verify_nonce( $nonce, 'yay-custom-fixed-prices-nonce' ) ) {
			return;
		}

		$custom_sign_up_fee_prices = array();

		foreach ( $currencies as $currency ) {

			if ( $this->default_currency === $currency->post_title ) {
				continue;
			}

			$fixed_prices_meta = ! is_null( $variation_idx ) ? $currency->post_title . '_' . $variation_idx : $currency->post_title;
			$signup_fee_price  = '';
			if ( isset( $_POST[ 'signup_fee_' . $fixed_prices_meta ] ) ) {
				$signup_fee_price = sanitize_text_field( $_POST[ 'signup_fee_' . $fixed_prices_meta ] );
			}

			$custom_sign_up_fee_prices[ $currency->post_title ] = array(
				'signup_fee_price' => $signup_fee_price,
			);
		}

		return $custom_sign_up_fee_prices;
	}

	public function custom_save_fixed_prices_single_product( $yay_currencies, $post_id, $product_type ) {
		if ( 'subscription' === $product_type ) {
			$custom_sign_up_fee_prices = $this->save_custom_sign_up_fee_prices_product( $yay_currencies );
			if ( ! $custom_sign_up_fee_prices ) {
				return;
			}
			update_post_meta( $post_id, 'yay_currency_custom_fixed_sign_up_fee_prices', $custom_sign_up_fee_prices );
		}

	}
	// VARIABLE
	public function add_fixed_prices_variable_product( $yay_currencies, $variation_id, $index ) {

		$product_variation = wc_get_product( $variation_id );
		if ( ! in_array( $product_variation->get_type(), array( 'variable-subscription', 'subscription_variation' ), true ) ) {
			return;
		}

		$custom_sign_up_fee_prices = get_post_meta( $variation_id, 'yay_currency_custom_fixed_sign_up_fee_prices', true );
		?>
		<div class="yay-currency-product-custom-fixed-prices-variable">
			<div class="yay-currency-fixed-price-checkbox-wrapper">
				<p class="yay-currency-fixed-price-text"><?php esc_html_e( 'Fixed Sign-up fee price for each currency', 'yay-currency' ); ?></p>
			</div>
			<div class='yay-currency-fixed-prices-input-wrapper'>
				<i class="checkbox-sub-text"><?php esc_html_e( 'You can manually set fixed Sign-up fee price for each currency. Leave blank to get the rate automatically.', 'yay-currency' ); ?></i>
				<div class="yay-currency-fixed-prices-input">
					<?php
					foreach ( $yay_currencies as $currency ) {
						if ( $this->default_currency === $currency->post_title ) {
							continue;
						}

						woocommerce_wp_text_input(
							array(
								'id'            => 'signup_fee_' . $currency->post_title . '_' . $index,
								'placeholder'   => 'Auto',
								'label'         => 'Sign-up fee (' . $currency->post_title . ')',
								'desc_tip'      => 'true',
								'wrapper_class' => 'form-row form-row-first',
								'value'         => ! empty( $custom_sign_up_fee_prices[ $currency->post_title ] ) ? $custom_sign_up_fee_prices[ $currency->post_title ]['signup_fee_price'] : '',
							)
						);

					}
					?>
				</div>
			</div>
		</div>
		<?php
	}

	public function custom_save_fixed_prices_variable_product( $yay_currencies, $variation_id, $index ) {

		$product_variation = wc_get_product( $variation_id );
		if ( ! in_array( $product_variation->get_type(), array( 'variable-subscription', 'subscription_variation' ), true ) ) {
			return;
		}
		$custom_sign_up_fee_prices = $this->save_custom_sign_up_fee_prices_product( $yay_currencies, $index );
		if ( ! $custom_sign_up_fee_prices ) {
			return;
		}
		update_post_meta( $variation_id, 'yay_currency_custom_fixed_sign_up_fee_prices', $custom_sign_up_fee_prices );
	}

	protected function is_fixed_sing_up_fee_price( $product, $apply_currency ) {
		$signup_fee_price = false;
		if ( ! FixedPriceHelper::is_set_fixed_price() || Helper::default_currency_code() === $apply_currency['currency'] ) {
			return $signup_fee_price;
		}

		$product_id          = $product->get_id();
		$custom_fixed_prices = get_post_meta( $product_id, 'yay_currency_custom_fixed_sign_up_fee_prices', true );
		if ( ! empty( $custom_fixed_prices ) && isset( $custom_fixed_prices[ $apply_currency['currency'] ] ) ) {
			$signup_fee_price = ! empty( $custom_fixed_prices[ $apply_currency['currency'] ]['signup_fee_price'] ) ? (float) $custom_fixed_prices[ $apply_currency['currency'] ]['signup_fee_price'] : $signup_fee_price;
		}

		return $signup_fee_price;
	}

	protected function get_fixed_signup_fee_price_by_currency( $product, $signup_fee_price, $apply_currency ) {
		$fixed_signup_fee_price = self::is_fixed_sing_up_fee_price( $product, $apply_currency );
		return $fixed_signup_fee_price ? $fixed_signup_fee_price : $signup_fee_price;
	}

	public function custom_converted_product_subtotal_sign_up_fee( $converted_product_subtotal, $product, $quantity ) {
		$fixed_signup_fee_price = self::is_fixed_sing_up_fee_price( $product, $this->apply_currency );

		if ( $fixed_signup_fee_price ) {
			$converted_product_subtotal = YayCurrencyHelper::format_price( $fixed_signup_fee_price * $quantity );
		}

		return $converted_product_subtotal;
	}

	// Recalculate Report data
	protected function recalculate_signup_revenue( $converted_currency ) {
		global $wpdb;

		$results = $wpdb->get_results(
			$wpdb->prepare(
				"
			SELECT order_total_meta.post_id as order_id, order_total_meta.meta_value as order_total
				FROM {$wpdb->postmeta} AS order_total_meta
					RIGHT JOIN
					(
						SELECT DISTINCT wcorder.ID
						FROM {$wpdb->posts} AS wcsubs
						INNER JOIN {$wpdb->posts} AS wcorder
							ON wcsubs.post_parent = wcorder.ID
						WHERE wcorder.post_type IN ( 'shop_order' )
							AND wcsubs.post_type IN ( 'shop_subscription' )
							AND wcorder.post_status IN ( 'wc-completed', 'wc-processing', 'wc-on-hold', 'wc-refunded' )
							AND wcorder.post_date >= %s
							AND wcorder.post_date < %s
					) AS orders ON orders.ID = order_total_meta.post_id
				WHERE order_total_meta.meta_key = '_order_total'
				",
				gmdate( 'Y-m-01', current_time( 'timestamp' ) ),
				gmdate( 'Y-m-d', strtotime( '+1 DAY', current_time( 'timestamp' ) ) )
			)
		);

		$signup_revenue = 0;
		foreach ( $results as $result ) {
			if ( ! isset( $result->order_id ) ) {
				continue;
			}
			$order = wc_get_order( $result->order_id );
			if ( ! $order ) {
				continue;
			}
			$rate_fee = Helper::get_yay_currency_order_rate( $result->order_id, $order );
			$rate_fee = $rate_fee ? $rate_fee : 1;

			$signup_revenue += $this->default_currency === $order->get_currency() ? floatval( $result->order_total ) : floatval( $result->order_total / $rate_fee );
		}
		return $signup_revenue;
	}

	protected function recalculate_renewal_revenue( $converted_currency ) {
		global $wpdb;
		// Renewal revenue this month
		$results = $wpdb->get_results(
			$wpdb->prepare(
				"
			SELECT order_total_meta.post_id as order_id, order_total_meta.meta_value as order_total
			FROM {$wpdb->postmeta} as order_total_meta
			RIGHT JOIN
			(
				SELECT DISTINCT wcorder.ID
				FROM {$wpdb->posts} AS wcorder
				INNER JOIN {$wpdb->postmeta} AS meta__subscription_renewal
					ON (
						wcorder.id = meta__subscription_renewal.post_id
						AND
						meta__subscription_renewal.meta_key = '_subscription_renewal'
					)
				WHERE wcorder.post_type IN ( 'shop_order' )
					AND wcorder.post_status IN ( 'wc-completed', 'wc-processing', 'wc-on-hold', 'wc-refunded' )
					AND wcorder.post_date >= %s
					AND wcorder.post_date < %s
			) AS orders ON orders.ID = order_total_meta.post_id
			WHERE order_total_meta.meta_key = '_order_total'
				",
				gmdate( 'Y-m-01', current_time( 'timestamp' ) ),
				gmdate( 'Y-m-d', strtotime( '+1 DAY', current_time( 'timestamp' ) ) )
			)
		);

		$renewal_revenue = 0;
		foreach ( $results as $result ) {

			if ( ! isset( $result->order_id ) ) {
				continue;
			}

			$order = wc_get_order( $result->order_id );
			if ( ! $order ) {
				continue;
			}

			$rate_fee         = Helper::get_yay_currency_order_rate( $result->order_id, $order );
			$rate_fee         = $rate_fee ? $rate_fee : 1;
			$renewal_revenue += $this->default_currency === $order->get_currency() ? floatval( $result->order_total ) : floatval( $result->order_total / $rate_fee );
		}
		return $renewal_revenue;
	}

	public function set_email_order_id( $order, $sent_to_admin, $plain_text, $email ) {
		$order_id                                = $order->get_id();
		$_REQUEST['yay_currency_email_order_id'] = $order_id;
	}

	public function renewal_email_woocommerce_currency( $currency ) {

		if ( doing_action( 'woocommerce_subscriptions_email_order_details' ) && isset( $_REQUEST['yay_currency_email_order_id'] ) ) {
			$order_id = sanitize_text_field( $_REQUEST['yay_currency_email_order_id'] );
			$order_id = intval( $order_id );
			if ( $order_id ) {
				$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id );
				return isset( $order_currency['currency'] ) ? $order_currency['currency'] : $currency;
			}
		}

		return $currency;
	}

	public function renewal_email_woocommerce_currency_symbol( $currency_symbol, $currency ) {
		if ( doing_action( 'woocommerce_subscriptions_email_order_details' ) && isset( $_REQUEST['yay_currency_email_order_id'] ) ) {
			$order_id = sanitize_text_field( $_REQUEST['yay_currency_email_order_id'] );
			$order_id = intval( $order_id );
			if ( $order_id ) {
				$order_currency = YayCurrencyHelper::get_order_currency_by_order_id( $order_id );
				return isset( $order_currency['symbol'] ) ? wp_kses_post( html_entity_decode( $order_currency['symbol'] ) ) : $currency_symbol;
			}
		}
		return $currency_symbol;
	}

	// Renewals Action

	public function wcs_new_order_created( $new_order, $subscription, $type ) {

		if ( in_array( $type, array( 'renewal_order', 'resubscribe_order' ), true ) && ! apply_filters( 'yay_currency_keep_first_price_subscription', true ) ) {
			$order_subscription_id = $subscription ? $subscription->get_id() : false;
			if ( ! $order_subscription_id || ! is_numeric( $order_subscription_id ) ) {
				return $new_order;
			}
			$parent_order    = wc_get_order( $order_subscription_id );
			$parent_order_id = $parent_order->get_parent_id();
			if ( $parent_order_id ) {
				self::handle_manual_new_order_on_admin( $parent_order_id, $new_order );
			}
		}

		return $new_order;
	}

	public function handle_manual_new_order_on_admin( $parent_order_id, $order ) {

		$currency_code = $order->get_currency();

		if ( Helper::default_currency_code() !== $currency_code ) {
			$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );
			$new_rate_fee   = YayCurrencyHelper::get_rate_fee( $apply_currency );

			if ( Helper::check_custom_orders_table_usage_enabled() ) {
				$this->parent_rate_fee = $order->get_meta( 'yay_currency_order_rate', true ) ? (float) $order->get_meta( 'yay_currency_order_rate', true ) : $new_rate_fee;
			} else {
				$this->parent_rate_fee = get_post_meta( $parent_order_id, 'yay_currency_order_rate', true ) ? (float) get_post_meta( $parent_order_id, 'yay_currency_order_rate', true ) : $new_rate_fee;
			}

			if ( $this->parent_rate_fee === $new_rate_fee ) {
				return;
			}

			do_action( 'yay_currency_handle_manual_order_line_items', $order, $apply_currency, $this->parent_rate_fee );
			do_action( 'yay_currency_handle_manual_order_fee_lines', $order, $apply_currency, $this->parent_rate_fee );
			do_action( 'yay_currency_handle_manual_order_shipping_lines', $order, $apply_currency, $this->parent_rate_fee );
			do_action( 'yay_currency_handle_manual_order_tax_lines', $order, $apply_currency, $this->parent_rate_fee );
			do_action( 'yay_currency_handle_manual_order_coupon_lines', $order, $apply_currency, $this->parent_rate_fee );
			do_action( 'yay_currency_handle_manual_order_totals', $order, $apply_currency, $this->parent_rate_fee );

			do_action( 'yay_currency_handle_manual_set_order_data', $order, $new_rate_fee, $currency_code );

		}

	}

	public function yay_currency_caching_localize_args( $localize_args ) {
		if ( ! isset( $localize_args['product_html_caching_enable'] ) ) {
			$localize_args['product_html_caching_enable'] = 'yes';
		}
		return $localize_args;
	}
}