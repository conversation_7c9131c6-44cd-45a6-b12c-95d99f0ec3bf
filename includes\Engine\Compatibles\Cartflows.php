<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;

class Cartflows {
	use SingletonTrait;

	private $apply_currency = array();
	public function __construct() {

		if ( ! class_exists( 'Cartflows_Loader' ) ) {
			return;
		}

		$this->apply_currency = YayCurrencyHelper::detect_current_currency();

		add_action( 'woocommerce_before_calculate_totals', array( $this, 'custom_price_to_cart_item' ), 9999 );
		add_filter( 'yay_currency_product_price_3rd_with_condition', array( $this, 'get_price_with_options' ), 10, 2 );
		add_filter( 'yay_currency_get_fixed_product_price_3rd_plugin', array( $this, 'get_product_price_fixed_3rd_plugin' ), 10, 3 );
	}

	public function custom_price_to_cart_item( $cart_object ) {
		if ( wp_doing_ajax() && ! WC()->session->__isset( 'reload_checkout' ) ) {
			$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );
			foreach ( $cart_object->cart_contents as $key => $value ) {
				$fixed_price = FixedPriceHelper::product_is_set_fixed_price_by_currency( $value['data'], $apply_currency );
				if ( isset( $value['custom_price'] ) && $fixed_price ) {
					$custom_price = floatval( $value['custom_price'] );
					SupportHelper::set_cart_item_objects_property( $value['data'], 'yay_currency_cart_flow_custom_price', $custom_price );
				}
			}
		}
	}

	public function get_price_with_options( $price, $product ) {
		$cart_flow_custom_price = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_cart_flow_custom_price' );
		if ( $cart_flow_custom_price ) {
			return $cart_flow_custom_price;
		}
		return $price;
	}

	public function get_product_price_fixed_3rd_plugin( $fixed_product_price, $product, $apply_currency ) {
		$cart_flow_custom_price = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_cart_flow_custom_price' );
		if ( $cart_flow_custom_price ) {
			return $cart_flow_custom_price;
		}
		return $fixed_product_price;
	}
}
