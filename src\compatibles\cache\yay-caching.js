(function ($) {
    'use strict';
    var yayCachingScript = function () {
        var self = this;
        self.currencyParamInput = 'input[name="yay_currency_current_url"]';
        self.oldCurrency = YayCurrency_Callback.Helper.getCookie(window.yayCurrency.cookie_name);

        self.init = function () {
            self.customCachingCompatibles();
            $(document).on('append.infiniteScroll', () => {
                self.customCachingCompatibles();
            });
        };

        self.detectAllowCaching = function () {
            // Cart page
            if ('1' === yayCurrency.cart_page || '1' === yayCurrency.hide_dropdown_switcher || '1' === yayCurrency.checkout_page) {
                return false;
            }

            // Checkout page
            if (typeof wc_checkout_params !== 'undefined') {
                if (parseInt(wc_checkout_params.is_checkout)) {
                    return false;
                }
            }

            const allow = YayCurrency_Callback.Helper.detectAllowCaching(true);

            return allow;
        }

        self.getCachingAjaxUrl = function () {
            let ajax_url = yay_currency_caching_data.ajax_url;
            if (yay_currency_caching_data.gtranslate_active) {
                ajax_url = document.location.origin + '/wp-admin/admin-ajax.php';
            }
            return ajax_url;
        };

        self.customCachingCompatibles = function () {

            if (!self.detectAllowCaching()) {
                self.resetCachingLoading();
                self.resetProductCacheLoading();
                return;
            }

            const productIds = YayCurrency_Callback.Helper.getListProductIdsAvailable();

            if (productIds.length) {
                $.ajax({
                    url: self.getCachingAjaxUrl(),
                    type: 'POST',
                    data: self.getListDataCaching(true, productIds),
                    beforeSend: function (res) {
                        self.customBeforeSendCaching(res);
                    },
                    xhrFields: {
                        withCredentials: true
                    },
                    success: function success(res) {
                        self.customResponseCaching(res, true);
                    },
                    error: function (xhr, ajaxOptions, thrownError) {
                        console.log("Error responseText: ", xhr.responseText);
                        self.resetCachingLoading();
                        self.resetProductCacheLoading();
                    }
                });
            } else {
                self.customCachingSwitcher();
            }

        };

        self.customCachingSwitcher = function () {
            $.ajax({
                url: yay_currency_caching_data.ajax_url,
                type: 'POST',
                data: self.getListDataCaching(),
                beforeSend: function (res) {
                    self.customBeforeSendCaching(res);
                },
                xhrFields: {
                    withCredentials: true
                },
                success: function success(res) {
                    self.customResponseCaching(res);
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    console.log("Error responseText: ", xhr.responseText);
                    self.resetCachingLoading();
                    self.resetProductCacheLoading();
                }
            });
        };

        self.getListDataCaching = function (prices = false, productIds = []) {
            let data = YayCurrency_Callback.Helper.getListDataCaching(prices, productIds);
            if (prices && yay_currency_caching_data.product_id && yay_currency_caching_data.product_is_variable) {
                data.product_variable_id = yay_currency_caching_data.product_id;
            }
            data = self.getDataCaching3rdPlugins(data);
            return data;
        };

        self.customBeforeSendCaching = function (res) {
            if ('1' === yayCurrency.cache_compatible) {
                if (yay_currency_caching_data.is_loading_mark) {
                    $(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).addClass(yay_currency_data_args.common_data_args.yayCurrencyLoading);
                }

                self.customBeforeSendCaching3rdPlugins(res);
            }
        };

        self.customResponseCaching = function (res, prices = false) {
            self.resetCachingLoading();
            if (res.success) {
                if ('no' === yay_currency_caching_data.is_switch_currency) {
                    $(document.body).trigger('wc_fragment_refresh');
                }

                if (res.data.product_variable_should_not_render_html) {
                    const single_variation_wrap_class = yay_currency_caching_data.product_single_variation_wrap_class ?? '.single_variation_wrap';
                    $(single_variation_wrap_class).addClass('yay-caching-hide-variation-render-html');
                }

                if (res.data.current_currency_id && +self.oldCurrency != res.data.current_currency_id) {
                    YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name, res.data.current_currency_id, +yayCurrency.cookie_lifetime_days);
                }

                YayCurrency_Callback.Helper.customResponseCommon(res);
                /*change current currency In product*/
                if (res.data.product_content) {
                    $(yay_currency_data_args.caching_data_args.yayCurrencyProduct).html(res.data.product_content);
                }
                /* apply for case use param */
                $(self.currencyParamInput).val(yay_currency_caching_data.yay_currency_current_url);

                self.customResponseProductCaching(res, prices);

                self.customResponseCaching3rdPlugins(res);

            } else {
                self.resetProductCacheLoading();
            }

        };

        self.customResponseProductCaching = function (res, prices = false) {
            if (prices) {
                if (res.data.price_html) {
                    const html_prices = res.data.price_html;
                    for (let id in html_prices) {
                        const el_product = `.yay-currency-cache-product-id[data-yay_currency-product-id=${id}]`,
                            el_price_html = html_prices[id],
                            price_wrapper_parent = $(el_product).closest('.price');
                        if (yay_currency_caching_data.product_html_caching_enable) {
                            if (price_wrapper_parent.length) {
                                price_wrapper_parent.html(el_price_html);
                            } else {
                                $(el_product).replaceWith(el_price_html);
                            }
                        } else {
                            $(el_product).replaceWith(el_price_html);
                        }

                    }

                    $(yay_currency_data_args.caching_data_args.yayVariationId).each((i, form) => {
                        let data = $(form).data('product_variations');
                        if (data) {
                            data.map((element) => {
                                let pid = element.variation_id;
                                element.price_html = html_prices[pid];
                                return element
                            });
                            $(form).data('product_variations', data);
                        }
                    });
                }
                self.resetProductCacheLoading();
            }
        };

        self.resetProductCacheLoading = function () {
            // reset list product caching
            if ($(yay_currency_data_args.caching_data_args.yayProductId).length) {
                $(yay_currency_data_args.caching_data_args.yayProductId).addClass('price').removeClass('yay-currency-cache-product-id yay-currency-cache-loading').removeAttr('data-yay_currency-product-id');
            }
        }

        self.resetCachingLoading = function () {
            YayCurrency_Callback.Helper.resetCachingLoading();
            self.resetCachingLoading3rdPlugins();
        };

        // Compatible with 3rd plugins

        self.getDataCaching3rdPlugins = function (data) {
            return yayCurrencyHooks.applyFilters('yayCurrencyGetDataCaching', data);
        };

        self.customBeforeSendCaching3rdPlugins = function (res) {
            // Compatible with third party [Themes / Plugins]
            yayCurrencyHooks.doAction('yayCurrencyBeforeSendCaching', [{ response: res }]);
        };

        self.customResponseCaching3rdPlugins = function (res) {
            // Compatible with third party [Themes / Plugins]
            yayCurrencyHooks.doAction('yayCurrencyResponseCaching', [{ response: res }]);

        };

        self.resetCachingLoading3rdPlugins = function () {
            // Compatible with third party [Themes / Plugins]
            yayCurrencyHooks.doAction('yayCurrencyResetLoadingCaching', []);
        };

    };

    jQuery(document).ready(function ($) {
        var yay_caching_fr = new yayCachingScript();
        yay_caching_fr.init();
    });
})(jQuery);