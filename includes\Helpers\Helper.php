<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;

class Helper {

	use SingletonTrait;

	protected function __construct() {}

	private static $YAY_CURRENCY_POST_TYPE   = 'yay-currency-manage';
	private static $YAY_CURRENCIES_TRANSIENT = 'yay-currencies-transient';

	public static function get_value_variable( $variable, $value_default = false ) {
		return isset( $variable ) && ! empty( $variable ) ? $variable : $value_default;
	}

	public static function sanitize( $args = array() ) {
		return wp_kses_post_deep( $args['data'] );
	}

	public static function is_method_executed( $class_name, $method_name ) {
		$ref_class  = new \ReflectionClass( $class_name );
		$ref_method = $ref_class->getMethod( $method_name );
		return $ref_method->isPublic() && ! $ref_method->isAbstract();
	}

	public static function get_post_type() {
		return self::$YAY_CURRENCY_POST_TYPE;
	}

	public static function get_yay_currency_by_currency_code( $currency_code = '' ) {
		$currency_code = ! empty( $currency_code ) ? $currency_code : self::default_currency_code();
		$currencies    = get_posts(
			array(
				'post_type' => self::get_post_type(),
				'title'     => $currency_code,
			)
		);
		return $currencies ? $currencies[0] : false;
	}

	public static function use_yay_currency_params() {
		$use_params = apply_filters( 'yay_currency_use_params', false );
		return $use_params;
	}

	public static function get_instance_classes( $engine_classes = array(), $yay_classes = array() ) {
		$last_length = count( $engine_classes );
		foreach ( $yay_classes as $yay_class ) {
			$engine_classes[ $last_length ] = $yay_class;
			$class                          = implode( '\\', $engine_classes );
			$class::get_instance();
		}
	}

	public static function engine_classes() {
		$classes = array(
			'Hooks',
			'Ajax',
			'ScheduleJobs',
		);

		return $classes;
	}

	public static function appearance_classes() {
		$classes = array(
			'MenuDropdown',
			'Widget',
		);

		return $classes;
	}

	public static function backend_classes() {
		$classes = array(
			'WooCommerceFilterAnalytics',
			'WooCommerceFilterReport',
			'Settings',
			'FixedPricesPerProduct',
			'WooCommerceOrderAdmin',
		);

		return $classes;
	}

	public static function frontend_classes() {
		$classes = array(
			'WooCommerceCurrency',
			'WooCommerceCheckoutPage',
			'WooCommerceTaxCalculate',
			'SingleProductDropdown',
			'Shortcodes',
		);

		return $classes;
	}

	public static function compatible_classes() {
		$classes = array(
			// PLUGINS
			'ThirdPartyPlugins',
			'AdvancedProductFieldsForWooCommerce',
			'WpmlCompatible',
			'PolylangCompatible',
			'BundlerPro',
			'WPCProductBundles',
			'B2BMarket',
			'B2BKingPro',
			'BookingsAppointmentsForWooCommercePremium',
			'Cartflows',
			'CheckoutWC',
			'Dokan',
			'EventTickets',
			'RoleBasedPricingFoWooCommerce',
			'HivePress',
			'PaymentPluginsBraintreeForWooCommerce',
			'JetSmartFilters',
			'FunnelKitAutomations',
			'WooCommerceSimpleAuction',
			'WooCommerceGutenbergBlocks',
			'WooCommerceSubscriptions',
			'WooCommercePointsAndRewards',
			'BuyOnceOrSubscribeWooCommerceSubscriptions',
			'WPCFrequentlyBoughtTogetherForWooCommerce',
			'WooAllProductsForSubscriptions',
			'WooCommerceProductFeed',
			'WooCommercePayments',
			'WooCommercePayPalPayments',
			'WooDiscountRules',
			'WooCommerceTMExtraProductOptions',
			'WooCommerceProductAddons',
			'WooCommerceProductAddOnsUltimate',
			'WoocommerceCustomProductAddons',
			'QuantityDiscountsAndPricingForWoocommerce',
			'Barn2WooCommerceWholesalePro',
			'Barn2WooCommerceDiscountManager',
			'YayExtra',
			'WPFunnels',
			'WooCommerceTeraWallet',
			'WooCommerceProductBundles',
			'LearnPress',
			'WooCommerceNameYourPrice',
			'WooCommerceRequestAQuote',
			'PPOM',
			'YITHPointsAndRewards',
			'YITHWoocommerceGiftCards',
			'YITHWooCommerceAddOnsExtraPremiumOptions',
			'YITHWooCommerceSubscription',
			'YITHBookingAndAppointmentForWooCommercePremium',
			'WoocommerceGiftCards',
			'WooCommerceDeposits',
			'WooCommerceBookings',
			'WooCommerceAppointments',
			'TranslatePressMultilingual',
			'TieredPricingTableForWooCommerce',
			'Measurement_Price_Calculator',
			'PaymentGatewayForPayPalWooCommerce',
			'PaymentPluginsForPayPalWooCommerce',
			'FunnelKitCart',
			'TravelBooking',
			'WooPaymentDiscounts',
			//THEMES
			'BreakdanceTheme',
			'EnfoldTheme',
			'WoodmartTheme',
			//CACHES
			'WooCommerceCaching',
		);

		return $classes;

	}

	public static function default_currency_code() {
		return get_option( 'woocommerce_currency' );
	}

	public static function get_yay_currencies_transient() {
		$yay_currencies = get_transient( self::$YAY_CURRENCIES_TRANSIENT );
		return apply_filters( 'yay_currency_transient_cache_currencies', $yay_currencies );
	}

	public static function delete_yay_currencies_transient() {
		if ( self::get_yay_currencies_transient() ) {
			delete_transient( self::$YAY_CURRENCIES_TRANSIENT );
		}
	}

	public static function get_currencies_post_type() {
		$currencies = self::get_yay_currencies_transient();
		if ( ! $currencies ) {
			$post_type_args = array(
				'posts_per_page' => -1,
				'post_type'      => 'yay-currency-manage',
				'post_status'    => 'publish',
				'order'          => 'ASC',
				'orderby'        => 'menu_order',
			);

			$currencies   = get_posts( $post_type_args );
			$dup_currency = array();

			foreach ( $currencies as $key => $currency ) {
				if ( in_array( $currency->post_title, $dup_currency ) ) {
					wp_delete_post( $currency->ID );
					unset( $currencies[ $key ] );
				} else {
					array_push( $dup_currency, $currency->post_title );
				}
			}
			set_transient( self::$YAY_CURRENCIES_TRANSIENT, $currencies );
		}
		return $currencies;
	}

	public static function change_existing_currency_symbol( $apply_currency = array(), $currency_symbol = '' ) {
		if ( ! $apply_currency ) {
			return $currency_symbol;
		}
		$currency_symbol = isset( $apply_currency['symbol'] ) ? $apply_currency['symbol'] : $currency_symbol;
		return wp_kses_post( html_entity_decode( $currency_symbol ) );
	}

	public static function set_default_currency_options( $apply_currency = array() ) {

		$thousand_separator = isset( $apply_currency['thousandSeparator'] ) ? $apply_currency['thousandSeparator'] : ',';
		$decimal_separator  = isset( $apply_currency['decimalSeparator'] ) ? $apply_currency['decimalSeparator'] : '.';
		$number_decimals    = isset( $apply_currency['numberDecimal'] ) && ! empty( $apply_currency['numberDecimal'] ) ? $apply_currency['numberDecimal'] : '0';

		return array(
			'thousandSeparator' => $thousand_separator,
			'decimalSeparator'  => $decimal_separator,
			'numberDecimal'     => $number_decimals,
		);

	}

	public static function change_price_format( $apply_currency = array(), $format = '' ) {

		if ( ! $apply_currency || YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {
			$apply_currency = YayCurrencyHelper::get_fallback_currency();
		}

		$format = YayCurrencyHelper::format_currency_symbol( $apply_currency );

		return apply_filters( 'yay_currency_custom_price_format', $format, $apply_currency );
	}

	public static function get_yay_currency_order_rate( $order_id, $order ) {
		$order_rate = false;
		if ( self::check_custom_orders_table_usage_enabled() ) {
			$order_rate = $order->get_meta( 'yay_currency_order_rate', true );
		} else {
			$order_rate = get_post_meta( $order_id, 'yay_currency_order_rate', true );
		}

		if ( ! $order_rate ) {
			$order_rate = YayCurrencyHelper::get_rate_fee_by_order( $order );
		}

		return $order_rate;
	}

	public static function calculate_order_rate( $order_id ) {
		$order = wc_get_order( $order_id );
		if ( ! $order ) {
			return false;
		}

		$currency_code = $order->get_currency();

		if ( empty( $currency_code ) || self::default_currency_code() === $currency_code ) {
			return false;
		}

		$order_rate = self::get_yay_currency_order_rate( $order_id, $order );

		return $order_rate ? $order_rate : false;
	}

	public static function revert_coupon_loop_to_default( $coupon_id, $order_id ) {
		$order_rate = self::calculate_order_rate( $order_id );
		if ( $order_rate ) {
			global $wpdb;
			$coupon_item = $wpdb->get_row(
				$wpdb->prepare(
					"SELECT discount_amount FROM {$wpdb->prefix}wc_order_coupon_lookup WHERE order_id = %d AND coupon_id = %d",
					$order_id,
					$coupon_id
				),
				ARRAY_A
			);

			foreach ( $coupon_item as $key => $amount ) {
				if ( floatval( $amount ) ) {
					$coupon_item[ $key ] = round( floatval( $amount ) / $order_rate, wc_get_price_decimals() );
				}
			}

			$wpdb->update(
				$wpdb->prefix . 'wc_order_coupon_lookup',
				$coupon_item,
				array(
					'order_id'  => $order_id,
					'coupon_id' => $coupon_id,
				),
				array( '%f' ),
				array( '%d', '%d' )
			);

		}
	}

	public static function revert_product_loop_to_default( $order_item_id, $order_id ) {
		$order_rate = self::calculate_order_rate( $order_id );
		if ( $order_rate ) {
			global $wpdb;
			$product_item = $wpdb->get_row(
				$wpdb->prepare(
					"SELECT product_net_revenue,product_gross_revenue,tax_amount,shipping_amount,shipping_tax_amount,coupon_amount FROM {$wpdb->prefix}wc_order_product_lookup WHERE order_id = %d AND order_item_id = %d",
					$order_id,
					$order_item_id
				),
				ARRAY_A
			);

			foreach ( $product_item as $key => $amount ) {

				if ( floatval( $amount ) ) {
					$value_to_default     = floatval( $amount ) / $order_rate;
					$product_item[ $key ] = round( $value_to_default, wc_get_price_decimals() );
				}
			}

			$wpdb->update(
				$wpdb->prefix . 'wc_order_product_lookup',
				$product_item,
				array(
					'order_id'      => $order_id,
					'order_item_id' => $order_item_id,
				),
				array( '%f', '%f', '%f', '%f', '%f', '%f' ),
				array( '%d', '%d' )
			);
		}
	}

	public static function revert_tax_loop_to_default( $tax_rate_id, $order_id ) {
		$order_rate = self::calculate_order_rate( $order_id );
		if ( $order_rate ) {
			global $wpdb;
			$tax_item = $wpdb->get_row(
				$wpdb->prepare(
					"SELECT shipping_tax,order_tax,total_tax FROM {$wpdb->prefix}wc_order_tax_lookup WHERE order_id = %d AND tax_rate_id = %d",
					$order_id,
					$tax_rate_id
				),
				ARRAY_A
			);

			foreach ( $tax_item as $key => $amount ) {
				if ( floatval( $amount ) ) {
					$value_to_default = floatval( $amount ) / $order_rate;
					$tax_item[ $key ] = round( $value_to_default, wc_get_price_decimals() );
				}
			}

			$wpdb->update(
				$wpdb->prefix . 'wc_order_tax_lookup',
				$tax_item,
				array(
					'order_id'    => $order_id,
					'tax_rate_id' => $tax_rate_id,
				),
				array( '%f', '%f', '%f' ),
				array( '%d', '%d' )
			);

		}

	}

	public static function revert_order_stats_to_default( $order_id ) {
		$order_rate = self::calculate_order_rate( $order_id );
		if ( $order_rate ) {
			global $wpdb;
			$order_data = $wpdb->get_row( $wpdb->prepare( "SELECT total_sales,tax_total,shipping_total,net_total FROM {$wpdb->prefix}wc_order_stats WHERE order_id = %d", $order_id ), ARRAY_A );

			foreach ( $order_data as $key => $value ) {
				$value_to_default   = floatval( $value ) / $order_rate;
				$order_data[ $key ] = round( $value_to_default, wc_get_price_decimals() );
			}

			$wpdb->update(
				$wpdb->prefix . 'wc_order_stats',
				$order_data,
				array( 'order_id' => $order_id ),
				array(
					'%f',
					'%f',
					'%f',
					'%f',
				),
				array( '%d' )
			);

		}
	}

	public static function get_list_order_ids( $sync_currencies = array(), $paged = 1 ) {
		$key_statuses = array_keys( wc_get_order_statuses() );
		$limit        = apply_filters( 'yay_currency_limit_sync_orders_to_base', 500 );
		$post_types   = array( 'shop_order', 'shop_order_refund' );
		$args         = array(
			'posts_per_page' => $limit,
			'paged'          => $paged,
			'post_type'      => $post_types,
			'post_status'    => $key_statuses,
			'orderby'        => 'ID',
			'order'          => 'ASC',
		);
		if ( self::check_custom_orders_table_usage_enabled() ) {
			$orders_args = array_merge(
				$args,
				array(
					'currency' => $sync_currencies,
					'return'   => 'ids',
				)
			);
			$orders_args = apply_filters( 'yay_currency_orders_args', $orders_args );
			$orders      = wc_get_orders( $orders_args );
		} else {
			$args['meta_query'] = array(
				array(
					'key'     => '_order_currency',
					'value'   => self::default_currency_code(),
					'compare' => '!=',
				),
			);
			$args['fields']     = 'ids';
			$orders_args        = apply_filters( 'yay_currency_post_type_orders_args', $args );
			$orders             = get_posts( $orders_args );
		}

		return $orders;
	}

	// support analytics
	public static function order_match_reverted( $order_id, $order = false ) {

		if ( ! $order ) {
			$order = wc_get_order( $order_id );
		}

		if ( self::check_custom_orders_table_usage_enabled() ) {
			$flag = false;
			if ( ! $order->get_meta( '_yay_currency_order_synced', true ) ) {
				$flag = true;
				$order->update_meta_data( '_yay_currency_order_synced', 'yes' );
			}
			if ( ! $order->get_meta( 'yay_currency_order_rate', true ) ) {
				$flag       = true;
				$order_rate = YayCurrencyHelper::get_rate_fee_by_order( $order );
				$order->update_meta_data( 'yay_currency_order_rate', $order_rate );
			}
			if ( $flag ) {
				$order->save();
			}
		} else {
			if ( ! get_post_meta( $order_id, '_yay_currency_order_synced', true ) ) {
				update_post_meta( $order_id, '_yay_currency_order_synced', 'yes' );
			}
			if ( ! get_post_meta( $order_id, 'yay_currency_order_rate', true ) ) {
				$order_rate = YayCurrencyHelper::get_rate_fee_by_order( $order );
				update_post_meta( $order_id, 'yay_currency_order_rate', $order_rate );
			}
		}

	}

	public static function get_list_orders_not_revert_to_base( $sync_currencies, $paged ) {

		$orders = self::get_list_order_ids( $sync_currencies, $paged );

		$results = array();

		foreach ( $orders as $order_id ) {

			$order = wc_get_order( $order_id );
			//detect order not exists or used other other multiple currency plugin previous then ignore
			if ( ! $order || SupportHelper::detect_used_other_currency_3rd_plugin( $order_id, $order ) || get_post_meta( $order_id, '_yay_currency_order_synced', true ) ) {
				continue;
			}

			if ( self::check_custom_orders_table_usage_enabled() && $order->get_meta( '_yay_currency_order_synced', true ) ) {
				continue;
			}

			$order_currency = $order->get_currency();

			if ( empty( $order_currency ) || self::default_currency_code() === $order_currency ) {
				continue;
			}

			array_push( $results, $order_id );

		}

		return array(
			'orders'  => $orders,
			'results' => $results,
		);
	}

	public static function get_woo_current_settings() {
		$default_currency = self::default_currency_code();
		return array(
			'currentCurrency'       => $default_currency,
			'currentCurrencySymbol' => YayCurrencyHelper::get_symbol_by_currency_code( $default_currency ),
			'currencyPosition'      => get_option( 'woocommerce_currency_pos' ),
			'thousandSeparator'     => get_option( 'woocommerce_price_thousand_sep' ),
			'decimalSeparator'      => get_option( 'woocommerce_price_decimal_sep' ),
			'numberDecimals'        => get_option( 'woocommerce_price_num_decimals' ),
		);
	}

	public static function woo_list_currencies() {
		$list_currencies        = get_woocommerce_currencies();
		$list_currencies['USD'] = 'United States dollar'; // Remove (US) from default
		return $list_currencies;
	}

	public static function convert_currencies_data() {
		$most_traded_currencies_code           = array( 'USD', 'EUR', 'GBP', 'INR', 'AUD', 'CAD', 'SGD', 'CHF', 'MYR', 'JPY' );
		$most_traded_converted_currencies_data = array();
		$converted_currencies_data             = array();

		$currency_code_by_country_code = CountryHelper::currency_code_by_country_code();
		$woo_list_currencies           = self::woo_list_currencies();

		foreach ( $currency_code_by_country_code as $key => $value ) {
			$currency_data = array(
				'currency'      => isset( $woo_list_currencies[ $key ] ) ? html_entity_decode( $woo_list_currencies[ $key ] ) : 'USD',
				'currency_code' => $key,
				'country_code'  => $value,
			);
			if ( in_array( $key, $most_traded_currencies_code ) ) {
				array_push( $most_traded_converted_currencies_data, $currency_data );
			} else {
				array_push( $converted_currencies_data, $currency_data );
			}
		}
		usort(
			$most_traded_converted_currencies_data,
			function ( $a, $b ) use ( $most_traded_currencies_code ) {
				$pos_a = array_search( $a['currency_code'], $most_traded_currencies_code );
				$pos_b = array_search( $b['currency_code'], $most_traded_currencies_code );
				return $pos_a - $pos_b;
			}
		);
		$result = array_merge( $most_traded_converted_currencies_data, $converted_currencies_data );
		return $result;
	}

	public static function get_default_currency() {
		$default_currency     = self::default_currency_code();
		$woo_current_settings = self::get_woo_current_settings();
		$currentCurrency      = isset( $woo_current_settings['currentCurrency'] ) ? $woo_current_settings['currentCurrency'] : $default_currency;
		$symbol               = isset( $woo_current_settings['currentCurrencySymbol'] ) ? $woo_current_settings['currentCurrencySymbol'] : YayCurrencyHelper::get_symbol_by_currency_code( $default_currency );
		$default_currency     = array(
			'currency'             => $currentCurrency,
			'currencySymbol'       => html_entity_decode( $symbol ),
			'currencyPosition'     => $woo_current_settings['currencyPosition'],
			'currencyCodePosition' => 'not_display',
			'thousandSeparator'    => $woo_current_settings['thousandSeparator'],
			'decimalSeparator'     => $woo_current_settings['decimalSeparator'],
			'numberDecimal'        => $woo_current_settings['numberDecimals'],
			'rate'                 => array(
				'type'  => 'auto',
				'value' => '1',
			),
			'fee'                  => array(
				'value' => '0',
				'type'  => 'fixed',
			),
			'status'               => '1',
			'paymentMethods'       => array( 'all' ),
			'countries'            => array( 'default' ),
			'default'              => true,
			'isLoading'            => false,
			'roundingType'         => 'disabled',
			'roundingValue'        => 1,
			'subtractAmount'       => 0,
		);

		return $default_currency;

	}

	public static function create_new_currency( $currentCurrency = '', $is_wc_settings_page = false ) {
		if ( ! $is_wc_settings_page ) {
			$woo_current_settings = self::get_woo_current_settings();
			$currentCurrency      = $woo_current_settings['currentCurrency'];
		}
		$args            = array(
			'post_title'  => $currentCurrency,
			'post_type'   => self::$YAY_CURRENCY_POST_TYPE,
			'post_status' => 'publish',
			'menu_order'  => 0,
		);
		$new_currency_ID = wp_insert_post( $args );
		if ( ! is_wp_error( $new_currency_ID ) ) {
			if ( ! $is_wc_settings_page ) {
				self::update_currency_meta( $new_currency_ID, 'currency_position', $woo_current_settings['currencyPosition'] );
				self::update_currency_meta( $new_currency_ID, 'thousand_separator', $woo_current_settings['thousandSeparator'] );
				self::update_currency_meta( $new_currency_ID, 'decimal_separator', $woo_current_settings['decimalSeparator'] );
				self::update_currency_meta( $new_currency_ID, 'number_decimal', $woo_current_settings['numberDecimals'] );
				self::update_currency_meta( $new_currency_ID, 'currency_code_position', 'not_display' );
			}
			self::update_post_meta_currency( $new_currency_ID );
		}
	}

	public static function update_post_meta_currency( $currency_id = 0, $currency = false ) {
		if ( $currency ) {
			$default_currency_options = self::set_default_currency_options( $currency );
			self::update_currency_meta( $currency_id, 'currency_position', $currency['currencyPosition'] );
			self::update_currency_meta( $currency_id, 'currency_code_position', $currency['currencyCodePosition'] );
			self::update_currency_meta( $currency_id, 'thousand_separator', $currency['thousandSeparator'] );
			self::update_currency_meta( $currency_id, 'decimal_separator', $default_currency_options['decimalSeparator'] );
			self::update_currency_meta( $currency_id, 'number_decimal', $default_currency_options['numberDecimal'] );
		}
		self::update_currency_meta( $currency_id, 'rounding_type', isset( $currency['roundingType'] ) ? $currency['roundingType'] : 'disabled' );
		self::update_currency_meta( $currency_id, 'rounding_value', isset( $currency['roundingValue'] ) ? $currency['roundingValue'] : 1 );
		self::update_currency_meta( $currency_id, 'subtract_amount', isset( $currency['subtractAmount'] ) ? $currency['subtractAmount'] : 0 );
		self::update_currency_meta( $currency_id, 'rate', isset( $currency['rate'] ) ? $currency['rate']['value'] : 1 );
		self::update_currency_meta( $currency_id, 'rate_type', isset( $currency['rate'] ) && isset( $currency['rate']['type'] ) ? $currency['rate']['type'] : 'auto' );
		$fee_currency = isset( $currency['fee'] ) && isset( $currency['fee']['type'] ) ? $currency['fee'] : array(
			'value' => '0',
			'type'  => 'fixed',
		);
		self::update_currency_meta( $currency_id, 'fee', $fee_currency );
		self::update_currency_meta( $currency_id, 'status', isset( $currency['status'] ) ? $currency['status'] : '1' );
		self::update_currency_meta( $currency_id, 'payment_methods', isset( $currency['paymentMethods'] ) ? $currency['paymentMethods'] : array( 'all' ) );
		self::update_currency_meta( $currency_id, 'countries', isset( $currency['countries'] ) ? $currency['countries'] : array( 'default' ) );
	}

	public static function update_currency_meta( $currency_id, $meta_key, $meta_value ) {
		if ( metadata_exists( 'post', $currency_id, $meta_key ) ) {
			update_post_meta( $currency_id, $meta_key, $meta_value );
		} else {
			add_post_meta( $currency_id, $meta_key, $meta_value );
		}
	}

	public static function get_current_theme() {
		$theme = wp_get_theme()->template;
		return strtolower( $theme );
	}

	public static function get_current_url() {
		global $wp;
		if ( isset( $_SERVER['QUERY_STRING'] ) && ! empty( $_SERVER['QUERY_STRING'] ) ) {
			$query_string = sanitize_text_field( $_SERVER['QUERY_STRING'] );
			$current_url  = add_query_arg( $query_string, '', home_url( $wp->request ) );
		} else {
			$current_url = add_query_arg( array(), home_url( $wp->request ) );
		}
		$current_url = urldecode( $current_url );
		return $current_url;
	}

	public static function create_nonce_field( $action = 'yay-currency-check-nonce', $name = 'yay-currency-nonce' ) {
		$name        = esc_attr( $name );
		$request_url = remove_query_arg( '_wp_http_referer' );
		$current_url = self::get_current_url();
		echo '<input type="hidden" class="' . esc_attr( $name ) . '" name="' . esc_attr( $name ) . '" value="' . esc_attr( wp_create_nonce( $action ) ) . '" />';
		echo '<input type="hidden" name="_wp_http_referer" value="' . esc_url( $request_url ) . '" />';
		echo '<input type="hidden" name="yay_currency_current_url" value="' . esc_url( $current_url ) . '" />';
	}

	public static function detect_force_currency_by_payment_method() {
		$force_currency                 = self::force_currency_specific_payment_option();
		$is_checkout_different_currency = get_option( 'yay_currency_checkout_different_currency', 0 );
		if ( ! $force_currency || ! isset( $force_currency['force_currency_enable'] ) || ! intval( $force_currency['force_currency_enable'] ) || 0 === intval( $is_checkout_different_currency ) ) {
			return false;
		}
		return $force_currency;
	}

	public static function force_currency_specific_payment_option() {
		$args           = array(
			'force_currency_enable'      => 0,
			'force_currency_reload_page' => 0,
		);
		$force_currency = get_option( 'yay_currency_force_currency_specific_payment_option', $args );
		return $force_currency;
	}

	public static function get_currency_payment_method_id( $payment_methods, $method_id ) {

		if ( ! $payment_methods ) {
			return false;
		}

		$find_currency = false;

		foreach ( $payment_methods as $payment_method ) {
			if ( isset( $payment_method['id'] ) && $method_id === $payment_method['id'] ) {
				$find_currency = $payment_method['currency'];
				break;
			}
		}

		return $find_currency;
	}

	// Approximate Price

	public static function approximate_price_info() {
		$args              = array(
			'status'   => 0,
			'label'    => '<span>Approximately:</span> ~(%formatted-price%)',
			'position' => 'before',
			'show_on'  => array( 'product_price_html' ),
		);
		$approximate_price = get_option( 'yay_currency_approximate_price_info', $args );
		return $approximate_price;
	}

	public static function approximate_price_enable() {
		$approximate_price = self::approximate_price_info();
		$status            = isset( $approximate_price['status'] ) ? intval( $approximate_price['status'] ) : 0;
		return $status;
	}

	// Caching
	public static function force_cache_compatible() {
		$default = array(
			'cache_enable'         => 0,
			'cache_loading_enable' => 0,
		);
		return get_option( 'yay_currency_cache_compatible_args', $default );
	}

	public static function cache_enable() {
		$cache_compatible = self::force_cache_compatible();
		$enable_cache     = isset( $cache_compatible['cache_enable'] ) ? intval( $cache_compatible['cache_enable'] ) : 0;
		return $enable_cache;
	}

	public static function loading_mark_enable() {
		$cache_compatible    = self::force_cache_compatible();
		$loading_mark_enable = isset( $cache_compatible['cache_loading_enable'] ) ? intval( $cache_compatible['cache_loading_enable'] ) : 0;
		return $loading_mark_enable;
	}

	public static function allow_detect_caching() {
		$flag = true;
		if ( is_cart() || is_checkout() ) {
			$flag = false;
		}
		$flag = apply_filters( 'yay_currency_allow_detect_caching', $flag );
		return $flag;
	}

	public static function get_data_currency_switcher( $type = 'widget', $attributes = array() ) {
		switch ( $type ) {
			case 'menu':
				$is_show_flag            = get_option( 'yay_currency_show_flag_in_menu_item', 1 );
				$is_show_currency_name   = get_option( 'yay_currency_show_currency_name_in_menu_item', 1 );
				$is_show_currency_symbol = get_option( 'yay_currency_show_currency_symbol_in_menu_item', 1 );
				$is_show_currency_code   = get_option( 'yay_currency_show_currency_code_in_menu_item', 1 );
				$switcher_size           = get_option( 'yay_currency_menu_item_size', 'small' );
				$switcher_wrap           = 'yay-currency-menu-shortcode-switcher';
				break;
			case 'block':
				$is_show_flag            = $attributes['isShowFlag'];
				$is_show_currency_name   = $attributes['isShowCurrencyName'];
				$is_show_currency_symbol = $attributes['isShowCurrencySymbol'];
				$is_show_currency_code   = $attributes['isShowCurrencyCode'];
				$switcher_size           = $attributes['widgetSize'];
				$switcher_wrap           = false;
				break;
			case 'shortcode':
			case 'product':
				$is_show_flag            = get_option( 'yay_currency_show_flag_in_switcher', 1 );
				$is_show_currency_name   = get_option( 'yay_currency_show_currency_name_in_switcher', 1 );
				$is_show_currency_symbol = get_option( 'yay_currency_show_currency_symbol_in_switcher', 1 );
				$is_show_currency_code   = get_option( 'yay_currency_show_currency_code_in_switcher', 1 );
				$switcher_size           = get_option( 'yay_currency_switcher_size', 'medium' );
				$switcher_wrap           = 'shortcode' === $type ? 'yay-currency-shortcode-switcher' : 'yay-currency-single-product-switcher';
				break;
			default:
				// widget
				$is_show_flag            = get_option( 'yay_currency_show_flag_in_widget', 1 );
				$is_show_currency_name   = get_option( 'yay_currency_show_currency_name_in_widget', 1 );
				$is_show_currency_symbol = get_option( 'yay_currency_show_currency_symbol_in_widget', 1 );
				$is_show_currency_code   = get_option( 'yay_currency_show_currency_code_in_widget', 1 );
				$switcher_size           = get_option( 'yay_currency_widget_size', 'small' );
				$switcher_wrap           = 'yay-currency-widget-switcher';
				break;
		}

		$data = array(
			'isShowFlag'           => $is_show_flag,
			'isShowCurrencyName'   => $is_show_currency_name,
			'isShowCurrencySymbol' => $is_show_currency_symbol,
			'isShowCurrencyCode'   => $is_show_currency_code,
			'widgetSize'           => $switcher_size,
			'switcherWrapper'      => $switcher_wrap,
		);

		return $data;

	}

	public static function currency_switcher_html( $type = 'widget', $attributes = array(), $is_ajax = false ) {
		$data = self::get_data_currency_switcher( $type, $attributes );

		$is_show_flag            = $data['isShowFlag'];
		$is_show_currency_name   = $data['isShowCurrencyName'];
		$is_show_currency_symbol = $data['isShowCurrencySymbol'];
		$is_show_currency_code   = $data['isShowCurrencyCode'];
		$switcher_size           = $data['widgetSize'];
		$switcher_wrap           = $data['switcherWrapper'];

		$switcher_template_args = apply_filters( 'yay_currency_switcher_template_args', compact( 'is_show_flag', 'is_show_currency_name', 'is_show_currency_symbol', 'is_show_currency_code', 'switcher_size' ) );

		if ( ! $is_ajax ) {
			if ( ! $switcher_wrap ) {
				echo '<div data-block-id="' . esc_attr( uniqid() ) . '" data-switcher-size="' . esc_attr( $switcher_size ) . '" data-show-flag="' . esc_attr( $is_show_flag ) . '" data-show-currency-name="' . esc_attr( $is_show_currency_name ) . '" data-show-currency-symbol="' . esc_attr( $is_show_currency_symbol ) . '" data-show-currency-code="' . esc_attr( $is_show_currency_code ) . '" class="yay-currency-block-switcher">';
			} else {
				echo '<div class="' . esc_attr( $switcher_wrap ) . '">';
			}
		}

		if ( 'widget' === $type ) {
			echo '<h4>' . esc_html__( 'Currency Switcher', 'yay-currency' ) . '</h4>';
		}

		wc_get_template( 'switcher/template.php', $switcher_template_args, '', trailingslashit( YAY_CURRENCY_PLUGIN_DIR . '/includes/templates' ) );

		if ( ! $is_ajax ) {
			echo '</div>';
		}

	}

	public static function currency_switcher_html_with_shortcode_params( $data, $is_ajax = false ) {

		$switcher_size           = ! $data['switcher_size'] ? get_option( 'yay_currency_switcher_size', 'medium' ) : ( ! in_array( $data['switcher_size'], array( 'medium', 'small' ) ) ? 'medium' : $data['switcher_size'] );
		$is_show_flag            = ! $data['show_flag'] ? get_option( 'yay_currency_show_flag_in_switcher', 1 ) : ( 'yes' === $data['show_flag'] || '1' === $data['show_flag'] ? 1 : 0 );
		$is_show_currency_name   = ! $data['show_name'] ? get_option( 'yay_currency_show_currency_name_in_switcher', 1 ) : ( 'yes' === $data['show_name'] || '1' === $data['show_name'] ? 1 : 0 );
		$is_show_currency_symbol = ! $data['show_symbol'] ? get_option( 'yay_currency_show_currency_symbol_in_switcher', 1 ) : ( 'yes' === $data['show_symbol'] || '1' === $data['show_symbol'] ? 1 : 0 );
		$is_show_currency_code   = ! $data['show_code'] ? get_option( 'yay_currency_show_currency_code_in_switcher', 1 ) : ( 'yes' === $data['show_code'] || '1' === $data['show_code'] ? 1 : 0 );

		$switcher_template_args = apply_filters( 'yay_currency_switcher_template_args', compact( 'is_show_flag', 'is_show_currency_name', 'is_show_currency_symbol', 'is_show_currency_code', 'switcher_size' ) );

		if ( ! $is_ajax ) {
			echo '<div data-block-id="' . esc_attr( uniqid() ) . '" data-switcher-size="' . esc_attr( $switcher_size ) . '" data-show-flag="' . esc_attr( $is_show_flag ) . '" data-show-currency-name="' . esc_attr( $is_show_currency_name ) . '" data-show-currency-symbol="' . esc_attr( $is_show_currency_symbol ) . '" data-show-currency-code="' . esc_attr( $is_show_currency_code ) . '" class="yay-currency-block-switcher">';
		}

		wc_get_template( 'switcher/template.php', $switcher_template_args, '', trailingslashit( YAY_CURRENCY_PLUGIN_DIR . '/includes/templates' ) );

		if ( ! $is_ajax ) {
			echo '</div>';
		}

	}

	public static function ajax_get_currency_switcher_html( $type = 'widget', $attributes = array() ) {
		ob_start();

		self::currency_switcher_html( $type, $attributes, true );

		$content = ob_get_clean();

		return $content;
	}

	public static function check_custom_orders_table_usage_enabled() {
		if ( class_exists( \Automattic\WooCommerce\Utilities\OrderUtil::class ) ) {
			if ( \Automattic\WooCommerce\Utilities\OrderUtil::custom_orders_table_usage_is_enabled() ) {
				return true;
			}
		}
		return false;
	}
}
