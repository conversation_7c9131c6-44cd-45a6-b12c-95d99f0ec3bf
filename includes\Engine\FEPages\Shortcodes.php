<?php
namespace Yay_Currency\Engine\FEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

class Shortcodes {

	use SingletonTrait;

	protected function __construct() {

		/*******************************Currency Switcher********************************/
		add_shortcode( 'yaycurrency-switcher', array( $this, 'currency_dropdown_shortcode' ) );

		/*******************************Menu Currency Switcher********************************/
		add_shortcode( 'yaycurrency-menu-item-switcher', array( $this, 'menu_item_switcher_shortcode' ) );

		/*******************************Display Current Currency Country Notice********************************/
		add_shortcode( 'yaycurrency-country-currency-notice', array( $this, 'generate_country_currency_notice_html' ) );

		/*******************************Current currency: Flag, Country (Location), Currency, Symbol********************************/
		add_shortcode( 'yaycurrency-flag', array( $this, 'current_flag_shortcode' ) );
		add_shortcode( 'yaycurrency-country', array( $this, 'current_country_shortcode' ) );
		add_shortcode( 'yaycurrency-currency', array( $this, 'current_currency_shortcode' ) );
		add_shortcode( 'yaycurrency-symbol', array( $this, 'current_symbol_shortcode' ) );

		/*******************************Fee Calculate********************************/
		add_shortcode( 'yaycurrency-fee', array( $this, 'calculate_fee_shortcode' ) );
		add_shortcode( 'yaycurrency-fee-fallback', array( $this, 'calculate_fee_fallback_shortcode' ) );
		add_shortcode( 'yaycurrency-fee-default', array( $this, 'calculate_fee_default_shortcode' ) );

		/*******************************Currency convertor HTML********************************/
		add_shortcode( 'yaycurrency-converter', array( $this, 'yay_currency_converter' ) );

		/*******************************Price HTML********************************/
		add_shortcode( 'yaycurrency-price-html', array( $this, 'generate_price_html' ) );
		add_shortcode( 'yaycurrency-product-price-html', array( $this, 'generate_product_price_html' ) ); // Support for Single Product pages and Product Archive Loop.
		add_shortcode( 'yaycurrency-fixed-price-html', array( $this, 'generate_price_html_by_country' ) );
		add_shortcode( 'yaycurrency-approximately-price-html', array( $this, 'generate_approximately_price_html' ) );
	}

	public function currency_dropdown_shortcode( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'switcher_size' => null,
				'show_flag'     => null,
				'show_name'     => null,
				'show_symbol'   => null,
				'show_code'     => null,
				'device'        => 'all',
			),
			$atts
		);
		ob_start();
		if ( YayCurrencyHelper::detect_allow_hide_dropdown_currencies() ) {
			return '';
		}
		if ( ! $atts['switcher_size'] && ! $atts['show_flag'] && ! $atts['show_name'] && ! $atts['show_symbol'] && ! $atts['show_code'] ) {
			Helper::currency_switcher_html( 'shortcode' );
		} else {

			switch ( $atts['device'] ) {
				case 'mobile':
					$device_allow = wp_is_mobile();
					break;
				case 'desktop':
					$device_allow = ! wp_is_mobile();
					break;
				default:
					$device_allow = true;
					break;
			}

			if ( ! $device_allow ) {
				echo '';
			} else {
				Helper::currency_switcher_html_with_shortcode_params( $atts );
			}
		}
		$content = ob_get_clean();
		return $content;

	}

	public function menu_item_switcher_shortcode( $content = null ) {
		if ( YayCurrencyHelper::detect_allow_hide_dropdown_currencies() ) {
			return '';
		}
		ob_start();
		Helper::currency_switcher_html( 'menu' );
		$content = ob_get_clean();
		return $content;

	}

	public function generate_country_currency_notice_html( $content = null ) {
		$auto_detect_currency_by_countries = get_option( 'yay_currency_auto_select_currency_by_countries', 0 );

		ob_start();

		if ( ! $auto_detect_currency_by_countries ) {
			return ob_get_clean();
		}

		$html = CountryHelper::get_country_currency_notice();

		$html = ! empty( $html ) ? apply_filters( 'yay_currency_country_currency_notice_html', $html ) : '';

		echo wp_kses_post( $html );

		return ob_get_clean();

	}

	public function current_flag_shortcode( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'width'  => '40px',
				'height' => '40px',
				'margin' => '0',
			),
			$atts
		);
		ob_start();
		$country_info = CountryHelper::get_country_info_from_IP();
		$country_code = isset( $country_info['country_code'] ) && ! empty( $country_info['country_code'] ) ? $country_info['country_code'] : false;
		$flag_url     = CountryHelper::get_flag_by_country_code( $country_code );
		$flag_style   = '';
		if ( ! empty( $atts['width'] ) || ! empty( $atts['height'] ) ) {
			$flag_style .= '--flag-width:' . $atts['width'] . ';--flag-height:' . $atts['height'] . ';--flag-margin:' . $atts['margin'];
		}
		echo '<img style="' . esc_attr( $flag_style ) . '" src="' . esc_url( $flag_url ) . '" class="yay-currency-country-flag" />';
		return ob_get_clean();

	}

	public function current_country_shortcode( $content = null ) {

		ob_start();

		$country_info = CountryHelper::get_country_info_from_IP();
		$country_code = isset( $country_info['country_code'] ) && ! empty( $country_info['country_code'] ) ? strtoupper( $country_info['country_code'] ) : false;
		$country_name = $country_code ? WC()->countries->countries[ $country_code ] : '';
		echo esc_html( $country_name );

		return ob_get_clean();

	}

	public function current_currency_shortcode( $content = null ) {

		ob_start();

		$apply_currency = YayCurrencyHelper::detect_current_currency();

		if ( isset( $apply_currency['currency'] ) ) {
			echo esc_html( $apply_currency['currency'] );
		}

		return ob_get_clean();

	}

	public function current_symbol_shortcode( $content = null ) {

		ob_start();

		$apply_currency = YayCurrencyHelper::detect_current_currency();

		if ( isset( $apply_currency['symbol'] ) ) {
			echo esc_html( $apply_currency['symbol'] );
		}

		return ob_get_clean();

	}

	public function get_cart_subtotal_by_shipping_fee( $apply_currency = false ) {
		$cart_subtotal = SupportHelper::get_cart_subtotal_shipping_fee( $apply_currency );
		if ( WC()->cart->applied_coupons ) {
			$discount_total = apply_filters( 'yay_currency_get_discount_total', 0, $apply_currency );
			$cart_subtotal  = $cart_subtotal - $discount_total;
		}
		return $cart_subtotal;
	}

	/*******************************Fee Calculate********************************/

	public function calculate_fee_shortcode( $atts ) {
		$atts = shortcode_atts(
			array(
				'percent' => '',
				'min_fee' => '',
				'max_fee' => '',
			),
			$atts,
			'yaycurrency-fee'
		);

		$apply_currency = YayCurrencyHelper::detect_current_currency();

		$atts['min_fee'] = YayCurrencyHelper::calculate_price_by_currency( $atts['min_fee'], true, $apply_currency );
		$atts['max_fee'] = YayCurrencyHelper::calculate_price_by_currency( $atts['max_fee'], true, $apply_currency );

		$cart_subtotal = $this->get_cart_subtotal_by_shipping_fee( $apply_currency );

		$calculated_fee = $this->get_fee_cost_by_shortcode( $cart_subtotal, $atts );

		return $calculated_fee;
	}

	public function calculate_fee_fallback_shortcode( $atts ) {
		$atts               = shortcode_atts(
			array(
				'percent' => '',
				'min_fee' => '',
				'max_fee' => '',
			),
			$atts,
			'yaycurrency-fee-fallback'
		);
		$converted_currency = YayCurrencyHelper::converted_currency();
		$fallback_currency  = YayCurrencyHelper::get_fallback_currency( $converted_currency );

		$atts['min_fee'] = YayCurrencyHelper::calculate_price_by_currency( $atts['min_fee'], true, $fallback_currency );
		$atts['max_fee'] = YayCurrencyHelper::calculate_price_by_currency( $atts['max_fee'], true, $fallback_currency );

		$cart_subtotal = $this->get_cart_subtotal_by_shipping_fee( $fallback_currency );

		$calculated_fee = $this->get_fee_cost_by_shortcode( $cart_subtotal, $atts );

		return $calculated_fee;
	}

	public function calculate_fee_default_shortcode( $atts ) {
		$atts           = shortcode_atts(
			array(
				'percent' => '',
				'min_fee' => '',
				'max_fee' => '',
			),
			$atts,
			'yaycurrency-fee-default'
		);
		$cart_subtotal  = $this->get_cart_subtotal_by_shipping_fee();
		$calculated_fee = $this->get_fee_cost_by_shortcode( $cart_subtotal, $atts );
		return $calculated_fee;
	}

	public function get_fee_cost_by_shortcode( $cart_subtotal, $atts ) {
		$calculated_fee = 0;

		if ( $atts['percent'] ) {
			$calculated_fee = $cart_subtotal * ( floatval( $atts['percent'] ) / 100 );
		}

		if ( $atts['min_fee'] && $calculated_fee < $atts['min_fee'] ) {
			$calculated_fee = $atts['min_fee'];
		}

		if ( $atts['max_fee'] && $calculated_fee > $atts['max_fee'] ) {
			$calculated_fee = $atts['max_fee'];
		}

		return $calculated_fee;
	}

	/*******************************Currency convertor HTML********************************/

	public function yay_currency_converter( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'heading'      => '',
				'amount_text'  => __( 'Amount', 'yay-currency' ),
				'from_text'    => __( 'From', 'yay-currency' ),
				'to_text'      => __( 'To', 'yay-currency' ),
				'hide_heading' => '',
			),
			$atts
		);
		ob_start();
		require YAY_CURRENCY_PLUGIN_DIR . 'includes/templates/shortcodes/converter.php';
		$content = ob_get_clean();
		return $content;
	}

	/*******************************Price HTML********************************/

	public function generate_price_html( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'price' => '',
			),
			$atts
		);

		ob_start();
		$apply_currency = YayCurrencyHelper::detect_current_currency();
		$price          = isset( $atts['price'] ) && is_numeric( $atts['price'] ) ? floatval( $atts['price'] ) : 0;
		$price_html     = '<span data-default-price="' . esc_attr( $price ) . '" class="yay-currency-shortcode-price-html-wrapper">' . YayCurrencyHelper::calculate_price_by_currency_html( $apply_currency, $price ) . '</span>';
		$price_html     = apply_filters( 'yay_currency_shortcode_get_price_html', $price_html, $price, $apply_currency );
		echo wp_kses_post( $price_html );
		return ob_get_clean();
	}

	public function generate_product_price_html( $atts, $content = null ) {

		$atts = shortcode_atts(
			array(
				'product_id'               => '',
				'hide_approximately_price' => 'no',
			),
			$atts
		);

		ob_start();
		if ( ! empty( $atts['product_id'] ) ) {
			$product = wc_get_product( $atts['product_id'] );
		} else {
			global $product, $post;
			if ( ! $product || ! is_object( $product ) ) {
				if ( $post && isset( $post->post_type ) && 'product' === $post->post_type ) {
					$product = wc_get_product( $post->ID );
				}
			}
		}

		$price_html = '';
		if ( $product && is_object( $product ) ) {
			if ( 'yes' === $atts['hide_approximately_price'] ) {
				$_REQUEST['yay_currency_price_html_use_shortcode'] = true;
			}

			$price_html = $product->get_price_html();
			$price_html = '<span data-shortcode-product-id="' . esc_attr( $atts['product_id'] ) . '" class="yay-currency-shortcode-product-price-html-wrapper">' . $price_html . '</span>';
		}
		echo wp_kses_post( $price_html );
		return ob_get_clean();
	}

	public function generate_approximately_price_html( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'price'               => '',
				'product_id'          => '',
				'show_original_price' => 'yes',
				'position'            => 'after',
			),
			$atts
		);
		ob_start();
		if ( is_admin() ) {
			return ob_get_clean();
		}
		if ( ! empty( $atts['price'] ) && is_numeric( $atts['price'] ) ) {
			$apply_currency           = YayCurrencyHelper::detect_current_currency();
			$country_info             = CountryHelper::get_country_info_from_IP();
			$approximately_price_data = SupportHelper::get_approximately_price_data( $country_info, $apply_currency );
			if ( ! $approximately_price_data ) {
				return ob_get_clean();
			}
			$params              = $approximately_price_data['params'];
			$price               = floatval( $atts['price'] );
			$approximately_price = $price * $params['rate'];

			$formatted_price = SupportHelper::get_formatted_price( $approximately_price, $params );
			if ( ! empty( $atts['approximately_label'] ) ) {
				$approximately_label = $atts['approximately_label'];
			} else {
				$approximate_price_info = Helper::approximate_price_info();
				$approximately_label    = isset( $approximate_price_info['status'] ) && $approximate_price_info['status'] ? ( isset( $approximate_price_info['label'] ) ? $approximate_price_info['label'] : '' ) : '';
			}

			if ( 'yes' === $atts['show_original_price'] ) {
				$original_price_html = YayCurrencyHelper::format_price( YayCurrencyHelper::calculate_price_by_currency( $price, false, $apply_currency ) );
				if ( ! empty( $approximately_label ) ) {
					$approximately_label      = wp_kses_post( $approximately_label );
					$approximately_price_html = strpos( $approximately_label, '%formatted-price%' ) ? str_replace( '%formatted-price%', $formatted_price, $approximately_label ) : $approximately_label . $formatted_price;
					$approximately_price_html = '<span class="yay-currency-approximately-price-by-country">' . $approximately_price_html . '</span>';
					$approximately_price_html = 'after' === $atts['position'] ? $original_price_html . $approximately_price_html : $approximately_price_html . $original_price_html;
				} else {
					$approximately_price_html = $original_price_html;
				}
			}
		} else {
			if ( ! empty( $atts['product_id'] ) ) {
				$product = wc_get_product( $atts['product_id'] );
			} else {
				global $product, $post;
				if ( ! $product || ! is_object( $product ) ) {
					if ( $post && isset( $post->post_type ) && 'product' === $post->post_type ) {
						$product = wc_get_product( $post->ID );
					}
				}
			}

			$price_html = '';
			if ( $product && is_object( $product ) ) {
				$price_html               = $product->get_price_html();
				$approximately_price_html = '<span data-shortcode-product-id="' . esc_attr( $atts['product_id'] ) . '" class="yay-currency-shortcode-product-price-html-wrapper">' . $price_html . '</span>';
			}
		}

		echo wp_kses_post( $approximately_price_html );
		return ob_get_clean();
	}

	protected function get_fixed_price_data( $price_data ) {
		$price_data = explode( ',', $price_data );
		$result     = [];
		foreach ( $price_data as $price_entry ) {
			$entry_parts = explode( '|', $price_entry );
			if ( ! $entry_parts ) {
				continue;
			}
			$entry_type = isset( $entry_parts[1] ) && ! empty( $entry_parts[1] ) ? $entry_parts[1] : false;
			if ( ! $entry_type ) {
				continue;
			}
			$result[ $entry_type ] = [
				'price' => isset( $entry_parts[0] ) && is_numeric( $entry_parts[0] ) ? $entry_parts[0] : false,
			];
			if ( 'countries' === 'type' && isset( $entry_parts[2] ) && ! empty( $entry_parts[2] ) ) {
				$result[ $entry_type ]['currency_code'] = $entry_parts[2];
			}
		}

		return $result;
	}

	protected function get_convert_price_by_countries( $convert_price = 0, $fixed_prices = array(), $apply_currency = array() ) {
		$country_info          = CountryHelper::get_country_info_from_IP();
		$guest_country_code    = isset( $country_info['country_code'] ) && ! empty( $country_info['country_code'] ) ? $country_info['country_code'] : false;
		$fixed_price_countries = self::get_fixed_price_data( $fixed_prices, 'countries' );
		$fixed_prices          = isset( $fixed_price_countries[ $guest_country_code ] ) ? $fixed_price_countries[ $guest_country_code ] : false;

		$fixed_price_country = isset( $fixed_prices['price'] ) && ! empty( $fixed_prices['price'] ) ? $fixed_prices['price'] : false;
		if ( $guest_country_code && $fixed_price_country ) {
			$fixed_price_currency_code = isset( $fixed_prices['currency_code'] ) && $fixed_prices['currency_code'] ? $fixed_prices['currency_code'] : false;
			if ( $fixed_price_currency_code ) {
				$convert_price = $fixed_price_currency_code === $apply_currency['currency'] ? $fixed_price_country : $convert_price;
			} elseif ( isset( $country_info['currency_code'] ) && ! empty( $country_info['currency_code'] ) ) {
				$convert_price = $country_info['currency_code'] === $apply_currency['currency'] ? $fixed_price_country : $convert_price;
			}
		}

		return $convert_price;
	}

	protected function get_convert_price_by_currencies( $convert_price = 0, $fixed_prices = array(), $apply_currency = array() ) {
		$fixed_price_currencies = self::get_fixed_price_data( $fixed_prices );
		$fixed_currency_code    = isset( $fixed_price_currencies[ $apply_currency['currency'] ] ) ? $fixed_price_currencies[ $apply_currency['currency'] ] : false;
		if ( $fixed_currency_code ) {
			$fixed_price_currency = isset( $fixed_currency_code['price'] ) && ! empty( $fixed_currency_code['price'] ) ? $fixed_currency_code['price'] : false;
			$convert_price        = $fixed_price_currency ? $fixed_price_currency : $convert_price;
		}
		return $convert_price;
	}

	public function generate_price_html_by_country( $atts, $content = null ) {
		$atts = shortcode_atts(
			array(
				'price'            => '',
				'fixed_countries'  => '',
				'fixed_currencies' => '',
			),
			$atts
		);
		ob_start();

		$price = isset( $atts['price'] ) && is_numeric( $atts['price'] ) ? floatval( $atts['price'] ) : 0;
		if ( ! $price ) {
			return '';
		}
		$apply_currency  = YayCurrencyHelper::detect_current_currency();
		$convert_price   = YayCurrencyHelper::calculate_price_by_currency( $price, false, $apply_currency );
		$price_data      = array(
			'data-default-price' => $price,
		);
		$fixed_countries = isset( $atts['fixed_countries'] ) && ! empty( $atts['fixed_countries'] ) ? $atts['fixed_countries'] : false;
		if ( $fixed_countries ) {
			$convert_price                      = self::get_convert_price_by_countries( $convert_price, $fixed_countries, $apply_currency );
			$price_data['data-fixed-countries'] = $fixed_countries;
		}
		$fixed_currencies = isset( $atts['fixed_currencies'] ) && ! empty( $atts['fixed_currencies'] ) ? $atts['fixed_currencies'] : false;
		if ( $fixed_currencies ) {
			$convert_price                       = self::get_convert_price_by_currencies( $convert_price, $fixed_currencies, $apply_currency );
			$price_data['data-fixed-currencies'] = $fixed_currencies;
		}
		$price_data['data-fixed-price'] = $convert_price;
		$metadata                       = '';
		foreach ( $price_data as $key => $value ) {
			$metadata .= "$key=$value ";
		}

		$price_html = '<span ' . esc_attr( rtrim( $metadata ) ) . ' class="yay-currency-shortcode-price-html-wrapper">' . YayCurrencyHelper::format_price( $convert_price ) . '</span>';
		$price_html = apply_filters( 'yay_currency_shortcode_get_fixed_price_html', $price_html, $convert_price, $price_data, $apply_currency );
		echo wp_kses_post( $price_html );
		return ob_get_clean();
	}
}
