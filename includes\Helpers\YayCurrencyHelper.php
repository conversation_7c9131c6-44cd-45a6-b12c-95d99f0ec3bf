<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class YayCurrencyHelper {

	use SingletonTrait;

	private static $COOKIE_NAME        = 'yay_currency_widget';
	private static $COOKIE_LANGUAGE    = 'yay_currency_current_language';
	private static $COOKIE_SWITCHER    = 'yay_currency_do_change_switcher';
	private static $CURRENCY_ID_BLOCKS = 'yay-currency-force-country-blocks';

	protected function __construct() {}

	public static function get_cookie_name( $type = 'default' ) {
		switch ( $type ) {
			case 'language':
				$cookie_name = self::$COOKIE_LANGUAGE;
				break;
			case 'switcher':
				$cookie_name = self::$COOKIE_SWITCHER;
				break;
			default:
				$cookie_name = self::$COOKIE_NAME;
				break;
		}
		return $cookie_name;
	}

	public static function get_currency_id_blocks_name() {
		return self::$CURRENCY_ID_BLOCKS;
	}

	public static function get_symbol_by_currency_code( $currency_code = '' ) {
		$default_currency_code = Helper::default_currency_code();
		$currency_code         = ! empty( $currency_code ) ? $currency_code : $default_currency_code;
		$all_symbols           = get_woocommerce_currency_symbols();
		$currency_symbol       = isset( $all_symbols[ $currency_code ] ) ? $all_symbols[ $currency_code ] : get_woocommerce_currency_symbol( $default_currency_code );
		return $currency_symbol;
	}

	public static function checkout_in_fallback_currency( $apply_currency = array() ) {

		if ( isset( $apply_currency['status'] ) && isset( $apply_currency['status'] ) ) {
			$is_checkout_different_currency = (int) get_option( 'yay_currency_checkout_different_currency', 0 );
			$status                         = (int) $apply_currency['status'];
			if ( $is_checkout_different_currency && ! $status ) {
				return true;
			}
		}

		return false;
	}

	public static function is_dis_checkout_diff_currency( $apply_currency = array() ) {
		$is_checkout_different_currency = (int) get_option( 'yay_currency_checkout_different_currency', 0 );
		if ( ! $is_checkout_different_currency ) {
			return true;
		}
		if ( isset( $apply_currency['status'] ) ) {
			if ( ! (int) $apply_currency['status'] ) {
				return true;
			}
		}
		return false;
	}

	public static function disable_fallback_option_in_checkout_page( $apply_currency = array() ) {
		$is_dis_checkout_diff_currency = self::is_dis_checkout_diff_currency( $apply_currency );
		$checkout_blocks               = SupportHelper::is_checkout_blocks(); // checkout use gutenberg blocks
		$is_checkout_page              = is_checkout() || $checkout_blocks || apply_filters( 'yay_currency_disable_fallback_checkout_conditions', false );
		$order_received_page           = $is_checkout_page && ( is_wc_endpoint_url( 'order-pay' ) || is_wc_endpoint_url( 'order-received' ) );
		return $is_dis_checkout_diff_currency && ( $is_checkout_page || $order_received_page );
	}

	public static function detect_woocommerce_blocks_page( $is_page = 'checkout', $apply_currency = false ) {
		$detect = false;

		if ( ! $apply_currency ) {
			$apply_currency = self::detect_current_currency();
			$apply_currency = self::get_current_currency( $apply_currency );
		}

		$is_dis_checkout_diff_currency = self::is_dis_checkout_diff_currency( $apply_currency );
		if ( ! $is_dis_checkout_diff_currency ) {
			return $detect;
		}

		// detect checkout use block
		if ( is_checkout() ) {
			$post_id = get_the_ID();
			if ( $post_id && has_block( 'woocommerce/checkout', $post_id ) ) {
				return true;
			}
		}

		return $detect;
	}

	public static function get_lifetime_days() {
		$lifetime_days = apply_filters( 'yay_currency_cookie_lifetime_days', 30 );
		return $lifetime_days;
	}

	public static function get_cookie_lifetime() {
		$lifetime_days = self::get_lifetime_days();
		$lifetime      = time() + ( 86400 * $lifetime_days );
		return apply_filters( 'yay_currency_cookie_lifetime', $lifetime );
	}

	public static function set_cookies( $apply_currency = array(), $converted_currency = array() ) {
		if ( ! $apply_currency || ! isset( $apply_currency['ID'] ) || headers_sent() ) {
			return;
		}

		if ( Helper::cache_enable() ) {
			$apply_currency = self::get_apply_currency( $converted_currency );
			$cookie_value   = $apply_currency['ID'];
		} else {
			$cookie_value = $apply_currency['ID'];
			if ( isset( $_COOKIE[ self::$COOKIE_NAME ] ) ) {
				$currency_ID = intval( sanitize_key( $_COOKIE[ self::$COOKIE_NAME ] ) );
				if ( $currency_ID === $cookie_value ) {
					return;
				}
			}
		}

		setcookie( self::$COOKIE_NAME, $cookie_value, self::get_cookie_lifetime(), '/' );

		$_COOKIE[ self::$COOKIE_NAME ] = $cookie_value;

	}

	public static function set_cookie( $cookie_name, $cookie_value ) {
		setcookie( $cookie_name, $cookie_value, self::get_cookie_lifetime(), '/' );
	}

	public static function delete_cookie( $cookie_name ) {
		if ( isset( $_COOKIE[ $cookie_name ] ) ) {
			unset( $_COOKIE[ $cookie_name ] );
			setcookie( $cookie_name, '', -1, '/' );
		}
	}

	public static function get_id_selected_currency() {
		$current_currency_id = 0;
		if ( isset( $_COOKIE[ self::$COOKIE_NAME ] ) ) {
			$current_currency_id = sanitize_key( $_COOKIE[ self::$COOKIE_NAME ] );
		}

		if ( isset( $_REQUEST['yay-currency-nonce'] ) && wp_verify_nonce( sanitize_key( $_REQUEST['yay-currency-nonce'] ), 'yay-currency-check-nonce' ) ) {
			$current_currency_id = isset( $_POST['currency'] ) ? sanitize_key( wp_unslash( $_POST['currency'] ) ) : $current_currency_id;
		}

		return intval( $current_currency_id );

	}

	public static function converted_currency( $currencies = false ) {
		$yay_list_currencies = $currencies ? $currencies : Helper::get_currencies_post_type();
		$converted_currency  = array();
		if ( $yay_list_currencies ) {
			foreach ( $yay_list_currencies as $currency ) {
				$currency_meta = get_post_meta( $currency->ID, '', false );
				if ( ! $currency_meta ) {
					continue;
				}
				$apply_currency = self::get_apply_currency_meta_data( $currency, $currency_meta );
				array_push( $converted_currency, $apply_currency );
			}
		}
		return $converted_currency;
	}

	public static function get_yay_currency_hooks() {
		$yay_currency_hooks = array(
			'price_hooks'         => self::get_product_price_hooks(),
			'coupon_hooks'        => self::get_coupon_hooks(),
			'shipping_hooks'      => self::get_shipping_hooks(),
			'checkout_diff_hooks' => self::get_checkout_diff_currency_hooks(),
			'order_details_hooks' => self::get_order_details_my_account_hooks(),
			'create_order_hooks'  => self::get_checkout_create_order_hooks(),
			'price_format_hooks'  => self::get_price_format_hooks(),
		);
		return $yay_currency_hooks;
	}

	// WooCommerce Product Price Filter hooks
	public static function get_product_price_hooks() {
		$product_price_hooks = array(
			'woocommerce_product_get_price',
			'woocommerce_product_get_regular_price',
			'woocommerce_product_get_sale_price',

			'woocommerce_product_variation_get_price',
			'woocommerce_product_variation_get_regular_price',
			'woocommerce_product_variation_get_sale_price',

			'woocommerce_variation_prices_price',
			'woocommerce_variation_prices_regular_price',
			'woocommerce_variation_prices_sale_price',
		);
		return $product_price_hooks;
	}

	// WooCommerce Coupon hooks
	public static function get_coupon_hooks() {
		$coupon_hooks = array(
			'woocommerce_coupon_get_amount'         => 'change_coupon_amount',
			'woocommerce_coupon_get_minimum_amount' => 'change_coupon_min_max_amount',
			'woocommerce_coupon_get_maximum_amount' => 'change_coupon_min_max_amount',
		);
		return $coupon_hooks;
	}

	// WooCommerce Shipping hooks
	public static function get_shipping_hooks() {
		$shipping_hooks = array(
			'woocommerce_package_rates'                 => 2,
			'woocommerce_shipping_local_pickup_instance_option' => 3,
			'woocommerce_shipping_free_shipping_instance_option' => 3,
			'woocommerce_shipping_free_shipping_option' => 3,
		);
		return $shipping_hooks;
	}

	// WooCommerce Order hooks
	public static function get_checkout_create_order_hooks() {
		$order_hooks = array(
			'woocommerce_checkout_create_order'           => 2,
			'woocommerce_checkout_create_order_line_item' => 4,
			'woocommerce_checkout_create_order_shipping_item' => 4,
			'woocommerce_checkout_create_order_fee_item'  => 4,
			'woocommerce_checkout_create_order_tax_item'  => 3,
		);
		return $order_hooks;
	}

	// WooCommerce Checkout hooks : Checkout in different currency disable
	public static function get_checkout_diff_currency_hooks() {
		$order_hooks = array(
			'woocommerce_cart_product_subtotal'           => 4,
			'woocommerce_cart_subtotal'                   => 3,
			'woocommerce_cart_totals_coupon_html'         => 3,
			'woocommerce_cart_shipping_method_full_label' => 2,
			'woocommerce_cart_totals_fee_html'            => 2,
			'woocommerce_cart_tax_totals'                 => 2,
			'woocommerce_cart_totals_taxes_total_html'    => 1,
			'woocommerce_cart_total'                      => 1,
		);
		return $order_hooks;
	}

	// WooCommerce MyAccount hooks
	public static function get_order_details_my_account_hooks() {
		$order_hooks = array(
			'woocommerce_order_formatted_line_subtotal' => 3,
			'woocommerce_get_order_item_totals'         => 3,
			'woocommerce_order_subtotal_to_display'     => 3,
			'woocommerce_order_shipping_to_display'     => 3,
			'woocommerce_order_discount_to_display'     => 2,
			'woocommerce_get_formatted_order_total'     => 4,
		);

		return $order_hooks;
	}

	// Price Format Currency hooks
	public static function get_price_format_hooks() {
		$format_hooks = array(
			'woocommerce_currency'            => 1,
			'woocommerce_currency_symbol'     => 2,
			'wc_get_price_thousand_separator' => 1,
			'wc_get_price_decimal_separator'  => 1,
			'wc_get_price_decimals'           => 1,
			'woocommerce_price_format'        => 2,
		);

		return $format_hooks;
	}

	// Approximate price hooks
	public static function get_approximate_price_hooks() {
		$args = [
			'product_price_html'    => [ 'woocommerce_get_price_html', 'custom_approximately_price_html', 2, ! Helper::cache_enable() ],
			'product_price_in_cart' => [ 'woocommerce_cart_product_price', 'custom_approximately_price_cart_product_price_html', 2, true ],
			'product_subtotal'      => [ 'woocommerce_cart_product_subtotal', 'custom_approximately_price_cart_product_subtotal_html', 4, true ],
			'subtotal'              => [ 'woocommerce_cart_subtotal', 'custom_approximately_price_cart_subtotal_html', 3, true ],
			'coupon'                => [ 'woocommerce_cart_totals_coupon_html', 'custom_approximately_price_coupon_html', 3, true ],
			'shipping'              => [ 'woocommerce_cart_shipping_method_full_label', 'custom_approximately_price_shipping_label', 2, true ],
			'fee'                   => [ 'woocommerce_cart_totals_fee_html', 'custom_approximately_price_cart_totals_fee_html', 2, true ],
			'tax'                   => [ 'woocommerce_cart_totals_taxes_total_html', 'custom_approximately_price_cart_totals_taxes_total_html', 1, true ],
			'total'                 => [ 'woocommerce_cart_total', 'custom_approximately_price_cart_total_html', 1, true ],
		];
		return apply_filters( 'yay_currency_approximate_price_hooks', $args );
	}

	// GET CURRENT CURRENCY & APPLY CURRENCY
	public static function get_currency_by_currency_code( $currency_code = '', $converted_currency = array() ) {
		$currency_code      = ! empty( $currency_code ) ? $currency_code : Helper::default_currency_code();
		$converted_currency = $converted_currency ? $converted_currency : self::converted_currency();
		foreach ( $converted_currency as $currency ) {
			if ( $currency['currency'] === $currency_code ) {
				return $currency;
			}
		}
		return false;
	}

	public static function get_apply_currency_meta_data( $currency, $currency_meta = array() ) {
		return array(
			'ID'                   => $currency->ID,
			'currency'             => $currency->post_title,
			'currencyPosition'     => $currency_meta['currency_position'][0] ?? 'left',
			'currencyCodePosition' => isset( $currency_meta['currency_code_position'] ) ? $currency_meta['currency_code_position'][0] : 'not_display',
			'thousandSeparator'    => isset( $currency_meta['thousand_separator'] ) ? $currency_meta['thousand_separator'][0] : ',',
			'decimalSeparator'     => isset( $currency_meta['decimal_separator'] ) ? $currency_meta['decimal_separator'][0] : '.',
			'numberDecimal'        => isset( $currency_meta['number_decimal'] ) ? $currency_meta['number_decimal'][0] : '0',
			'roundingType'         => isset( $currency_meta['rounding_type'] ) ? $currency_meta['rounding_type'][0] : 'disabled',
			'roundingValue'        => isset( $currency_meta['rounding_value'] ) ? $currency_meta['rounding_value'][0] : 1,
			'subtractAmount'       => isset( $currency_meta['subtract_amount'] ) ? $currency_meta['subtract_amount'][0] : 0,
			'rate'                 => isset( $currency_meta['rate'] ) ? $currency_meta['rate'][0] : array(
				'type'  => 'auto',
				'value' => '1',
			),
			'fee'                  => isset( $currency_meta['fee'] ) ? maybe_unserialize( $currency_meta['fee'][0] ) : array(
				'value' => '0',
				'type'  => 'fixed',
			),
			'status'               => isset( $currency_meta['status'] ) ? $currency_meta['status'][0] : '1',
			'paymentMethods'       => isset( $currency_meta['payment_methods'] ) ? maybe_unserialize( $currency_meta['payment_methods'][0] ) : array( 'all' ),
			'countries'            => isset( $currency_meta['countries'] ) ? maybe_unserialize( $currency_meta['countries'][0] ) : array( 'default' ),
			'symbol'               => self::get_symbol_by_currency_code( $currency->post_title ),
		);
	}

	public static function get_currency_by_ID( $currency_id = 0 ) {
		$currency      = get_post( $currency_id );
		$currency_meta = get_post_meta( $currency_id, '', false );
		if ( ! $currency || ! $currency_meta || Helper::get_post_type() !== $currency->post_type ) {
			return self::get_currency_by_currency_code();
		}
		$apply_currency = self::get_apply_currency_meta_data( $currency, $currency_meta );
		return $apply_currency;
	}

	public static function display_content_payment_notice_html( $html = '' ) {
		$current_theme       = Helper::get_current_theme();
		$hide_class          = empty( $html ) ? 'yay-currency-force-payment-hide' : '';
		$force_payment_class = 'yay-currency-checkout-force-payment-notice yay-currency-checkout-notice user yay-currency-with-' . $current_theme . ' ' . $hide_class;
		echo "<div class='" . esc_attr( $force_payment_class ) . "'>" . wp_kses_post( $html ) . '</div>';
	}

	public static function set_cookie_currency_switcher( $selected_currency_id, $do_set_cookie = false ) {
		$cookie_switcher_name = self::get_cookie_name( 'switcher' );
		if ( $do_set_cookie ) {
			self::set_cookie( $cookie_switcher_name, $selected_currency_id );
		}
		$_COOKIE[ $cookie_switcher_name ] = $selected_currency_id;
	}

	public static function get_apply_currency_by_payment_method_id( $payment_method_id_selected, $force_currency ) {
		$apply_currency        = false;
		$currency_by_method_id = Helper::get_currency_payment_method_id( $force_currency['force_currency_payment_options'], $payment_method_id_selected );
		if ( isset( $currency_by_method_id ) && is_array( $currency_by_method_id ) ) {
			$find_currency = array_shift( $currency_by_method_id );
			if ( ! empty( $find_currency ) && 'default' !== $find_currency ) {
				$apply_currency = self::get_currency_by_currency_code( $find_currency );
			}
		}
		return $apply_currency;
	}

	public static function get_apply_currency_by_force_payment_selected( $force_currency ) {
		$apply_currency = false;
		if ( CountryHelper::detect_force_country_by_checkout_page() ) {
			$payment_method_id_selected = isset( $_REQUEST['payment_method'] ) ? sanitize_text_field( $_REQUEST['payment_method'] ) : WC()->session->get( 'chosen_payment_method' );
			$apply_currency             = self::get_apply_currency_by_payment_method_id( $payment_method_id_selected, $force_currency );
		}
		return $apply_currency;
	}

	public static function get_currency_change_switcher( $apply_currency = array() ) {

		if ( isset( $_REQUEST['yay-currency-nonce'] ) && wp_verify_nonce( sanitize_key( $_REQUEST['yay-currency-nonce'] ), 'yay-currency-check-nonce' ) ) {
			$selected_currency_id = false;

			if ( isset( $_POST['currency'] ) ) {
				do_action( 'yay_currency_before_change_currency_switcher', $_POST );

				$old_apply_currency = Helper::get_default_currency();

				$selected_currency_id = sanitize_text_field( $_POST['currency'] );
				$apply_currency       = self::get_currency_by_ID( $selected_currency_id );
				self::set_cookie_currency_switcher( $selected_currency_id );

				do_action( 'yay_currency_after_change_currency_switcher', $apply_currency, $old_apply_currency );
			}

			if ( isset( $_POST['yay_currency'] ) && isset( $_POST['yay_currency_current_url'] ) ) {
				do_action( 'yay_currency_before_change_currency_switcher', $_POST );

				$old_apply_currency = Helper::get_default_currency();

				$selected_currency_id = sanitize_text_field( $_POST['yay_currency'] );
				$current_url          = sanitize_text_field( $_POST['yay_currency_current_url'] );
				self::set_cookie_currency_switcher( $selected_currency_id );

				do_action( 'yay_currency_after_change_currency_switcher', $apply_currency, $old_apply_currency );

				do_action( 'yay_currency_redirect_to_url', $current_url, $selected_currency_id );
			}
		}

		// CHANGE CURRENCY ON URL --- ?yay-currency=EUR
		$currency_param_name = apply_filters( 'yay_currency_param_name', 'yay-currency' );
		if ( Helper::use_yay_currency_params() && isset( $_REQUEST[ $currency_param_name ] ) && ! empty( $_REQUEST[ $currency_param_name ] ) ) {
			$currency_code          = sanitize_text_field( $_REQUEST[ $currency_param_name ] );
			$apply_currency_by_code = self::get_currency_by_currency_code( strtoupper( $currency_code ) );
			$apply_currency         = $apply_currency_by_code ? $apply_currency_by_code : $apply_currency;
			self::set_cookie_currency_switcher( $apply_currency['ID'] );
		}

		if ( apply_filters( 'yay_currency_apply_found_on_checkout_page_for_all_pages', false ) ) {

			// Force Currency to Billing/Shipping Country at Checkout
			if ( CountryHelper::force_payment_country() ) {
				// CHECKOUT PAGE
				$apply_currency_force_country_code = CountryHelper::get_apply_currency_by_force_payment_country();
				if ( $apply_currency_force_country_code ) {
					return $apply_currency_force_country_code;
				}
				// CART PAGE
				$apply_currency_force_shipping_cart = CountryHelper::get_apply_currency_by_force_shipping_country_cart_page();
				if ( $apply_currency_force_shipping_cart ) {
					return $apply_currency_force_shipping_cart;
				}
			}

			// Force Currency by Payment Method Selected at Checkout
			$force_currency = Helper::detect_force_currency_by_payment_method();
			if ( $force_currency && isset( $force_currency['force_currency_payment_options'] ) ) {
				$apply_currency_by_payment_selected = self::get_apply_currency_by_force_payment_selected( $force_currency );
				if ( $apply_currency_by_payment_selected ) {
					return $apply_currency_by_payment_selected;
				}
			}
		}

		$apply_currency = apply_filters( 'yay_currency_apply_currency', $apply_currency );
		return $apply_currency;
	}

	public static function auto_detect_countries( $apply_currency = array(), $converted_currency = array() ) {
		$current_currency_id  = isset( $_COOKIE[ self::$COOKIE_NAME ] ) ? sanitize_key( $_COOKIE[ self::$COOKIE_NAME ] ) : false;
		$switcher_currency_id = isset( $_COOKIE[ self::$COOKIE_SWITCHER ] ) ? sanitize_key( $_COOKIE[ self::$COOKIE_SWITCHER ] ) : false;

		if ( $current_currency_id ) {
			$selected_currency_id = $switcher_currency_id && ! wp_doing_ajax() ? $switcher_currency_id : $current_currency_id;
			$apply_currency       = self::get_currency_by_ID( $selected_currency_id );
		}
		// Exit early if auto-select by country setting is disabled
		if ( ! get_option( 'yay_currency_auto_select_currency_by_countries', 0 ) ) {
			return $apply_currency;
		}
		// Return the current currency if switcher or currency cookie is set, Or based on payment method or country, if applicable
		$is_force_enable = ( CountryHelper::force_payment_country() || Helper::detect_force_currency_by_payment_method() ) && ! SupportHelper::detect_ajax_caching_doing();
		if ( $current_currency_id && ( $switcher_currency_id || $is_force_enable ) ) {
			return $apply_currency;
		}
		// Detect currency by IP Address or customer Country
		$country_info             = CountryHelper::get_country_info_from_IP();
		$currency_by_country_code = CountryHelper::get_yay_currency_by_country_code( $country_info['country_code'], $country_info['currency_code'], $converted_currency );
		return $currency_by_country_code ? $currency_by_country_code : $apply_currency;

	}

	public static function get_default_apply_currency_not_exists_post_type() {
		$default_apply_currency = Helper::get_default_currency();
		if ( $default_apply_currency ) {
			$default_apply_currency['rate']   = 1;
			$default_apply_currency['symbol'] = self::get_symbol_by_currency_code();
		}
		return $default_apply_currency;
	}

	public static function get_default_apply_currency( $converted_currency = array() ) {
		if ( $converted_currency ) {
			$default_currency_code  = Helper::default_currency_code();
			$default_apply_currency = reset( $converted_currency );
			if ( isset( $default_apply_currency['currency'] ) && $default_currency_code === $default_apply_currency['currency'] ) {
				return $default_apply_currency;
			} else {
				$found_key              = array_search( $default_currency_code, array_column( $converted_currency, 'currency' ) );
				$default_apply_currency = isset( $converted_currency[ $found_key ] ) ? $converted_currency[ $found_key ] : $default_apply_currency;
			}
		} else {
			$default_apply_currency = self::get_default_apply_currency_not_exists_post_type();
		}

		return $default_apply_currency;
	}

	public static function get_apply_currency( $converted_currency = array() ) {

		$default_apply_currency = self::get_default_apply_currency( $converted_currency );

		$apply_currency = self::auto_detect_countries( $default_apply_currency, $converted_currency );

		if ( apply_filters( 'yay_currency_multiple_language_active', false ) ) {
			$apply_currency = apply_filters( 'yay_currency_get_apply_currency_by_language_3rd_plugin', $apply_currency, $converted_currency );
		}

		$apply_currency = self::get_currency_change_switcher( $apply_currency );

		return $apply_currency;
	}

	public static function detect_current_currency() {
		$currency_id = self::get_id_selected_currency();
		if ( $currency_id ) {
			$apply_currency = self::get_currency_by_ID( $currency_id );
		} else {
			$converted_currency = self::converted_currency();
			$apply_currency     = self::get_apply_currency( $converted_currency );
		}
		return $apply_currency;
	}

	public static function list_ajax_actions_allow() {
		$action_args = array( 'woosq_quickview', 'pvtfw_woocommerce_ajax_add_to_cart', 'woocommerce_get_refreshed_fragments', 'loadmore', 'yayCurrency_handle_force_payment_response', 'yayCurrency_handle_force_currency_by_payment_selected_response' );
		if ( defined( 'ELEMENTOR_PRO_VERSION' ) ) {
			array_push( $action_args, 'elementor_menu_cart_fragments' );
		}

		if ( function_exists( 'Barn2\Plugin\WC_Product_Table\wpt' ) ) {
			array_push( $action_args, 'wcpt_load_products' );
		}

		$current_theme = Helper::get_current_theme();
		switch ( $current_theme ) {
			case 'kapee':
				$action_args = array_unique( array_merge( $action_args, array( 'kapee_update_cart_widget_quantity', 'kapee_ajax_add_to_cart' ) ) );
				break;
			case 'betheme':
				$action_args = array_unique( array_merge( $action_args, array( 'mfnrefreshcart', 'mfnproductquickview' ) ) );
				break;
			case 'flatsome':
				array_push( $action_args, 'flatsome_quickview' );
				break;
			case 'salient':
				array_push( $action_args, 'nectar_woo_get_product' );
				break;
			default:
				break;
		}
		return apply_filters( 'yay_currency_detect_action_args', $action_args );
	}

	public static function list_wc_ajax_actions_allow() {
		$wc_ajax_args = array( 'xoo_wsc_update_item_quantity', 'get_refreshed_fragments' );
		return apply_filters( 'yay_currency_detect_wc_ajax_args', $wc_ajax_args );
	}

	public static function is_reload_permitted() {

		if ( apply_filters( 'yay_currency_detect_block_convert_price', false ) ) {
			return false;
		}

		if ( ! is_admin() ) {
			return true;
		}

		if ( wp_doing_ajax() ) {
			// Ajax actions
			$action_args = self::list_ajax_actions_allow();
			if ( isset( $_REQUEST['action'] ) && in_array( $_REQUEST['action'], $action_args, true ) ) {
				return true;
			}
			// WC Ajax actions
			$wc_ajax_args = self::list_wc_ajax_actions_allow();
			if ( isset( $_REQUEST['wc-ajax'] ) && in_array( $_REQUEST['wc-ajax'], $wc_ajax_args, true ) ) {
				return true;
			}
		}

		return apply_filters( 'yay_currency_is_reload_permitted', false );
	}

	public static function get_current_currency( $apply_currency = array() ) {
		$apply_currency = apply_filters( 'yay_currency_detect_current_currency', $apply_currency );
		return $apply_currency ? $apply_currency : self::detect_current_currency();
	}

	public static function detect_allow_hide_dropdown_currencies() {
		$hide_dropdown = false;

		if ( is_checkout() && ( is_wc_endpoint_url( 'order-pay' ) || is_wc_endpoint_url( 'order-received' ) ) ) {
			$hide_dropdown = true;
		}

		// detect manual order with payment link
		if ( isset( $_REQUEST['pay_for_order'] ) && isset( $_REQUEST['key'] ) ) {
			$hide_dropdown = true;
		}

		return apply_filters( 'yay_currency_should_hide_dropdown_switcher', $hide_dropdown );

	}

	public static function format_price( $price = 0 ) {
		$formatted_price = wc_price( $price );
		$apply_currency  = self::detect_current_currency();

		if ( $apply_currency ) {
			$formatted_price = wc_price(
				$price,
				self::get_apply_currency_format_info( $apply_currency )
			);
		}

		return apply_filters( 'yaycurrency_formatted_price', $formatted_price, $price, $apply_currency );

	}

	public static function formatted_price_by_currency( $price = 0, $apply_currency = array() ) {
		$apply_currency  = $apply_currency ? $apply_currency : self::detect_current_currency();
		$price           = self::format_price_currency( $price, $apply_currency );
		$format          = self::format_currency_symbol( $apply_currency );
		$formatted_price = sprintf( $format, '<span class="woocommerce-Price-currencySymbol">' . $apply_currency['symbol'] . '</span>', $price );
		$return          = '<span class="woocommerce-Price-amount amount"><bdi>' . $formatted_price . '</bdi></span><span>';
		return apply_filters( 'yaycurrency_formatted_price_by_currency', $return, $price, $format, $apply_currency );
	}

	public static function format_sale_price( $regular_price, $sale_price ) {
		$formatted_price = '<del aria-hidden="true">' . ( is_numeric( $regular_price ) ? wc_price( $regular_price ) : $regular_price ) . '</del> <ins>' . ( is_numeric( $sale_price ) ? wc_price( $sale_price ) : $sale_price ) . '</ins>';
		$apply_currency  = self::detect_current_currency();
		if ( $apply_currency ) {
			$format          = self::get_apply_currency_format_info( $apply_currency );
			$regular_price   = wc_price( $regular_price, $format );
			$sale_price      = wc_price( $sale_price, $format );
			$formatted_price = '<del aria-hidden="true">' . ( is_numeric( $regular_price ) ? $regular_price : $regular_price ) . '</del> <ins>' . ( is_numeric( $sale_price ) ? $sale_price : $sale_price ) . '</ins>';
		}
		return apply_filters( 'yaycurrency_format_sale_price', $formatted_price, $regular_price, $sale_price, $apply_currency );
	}

	public static function formatted_sale_price_by_currency( $regular_price = 0, $sale_price = 0, $apply_currency = array() ) {
		$apply_currency  = $apply_currency ? $apply_currency : self::detect_current_currency();
		$regular_price   = self::formatted_price_by_currency( $regular_price, $apply_currency );
		$sale_price      = self::formatted_price_by_currency( $sale_price, $apply_currency );
		$formatted_price = '<del aria-hidden="true">' . $regular_price . '</del> <ins>' . $sale_price . '</ins>';
		return apply_filters( 'yaycurrency_formatted_sale_price_by_currency', $formatted_price, $regular_price, $sale_price, $apply_currency );
	}

	public static function get_apply_currency_format_info( $apply_currency = array() ) {
		$format                     = self::format_currency_symbol( $apply_currency );
		$default_currency_options   = Helper::set_default_currency_options( $apply_currency );
		$apply_currency_format_info = array(
			'ex_tax_label'       => false,
			'currency'           => $apply_currency['currency'],
			'decimal_separator'  => $default_currency_options['decimalSeparator'],
			'thousand_separator' => $apply_currency['thousandSeparator'],
			'decimals'           => $default_currency_options['numberDecimal'],
			'price_format'       => $format,
		);
		return $apply_currency_format_info;
	}

	public static function format_currency_position( $currency_position = 'left' ) {
		$format = '%1$s%2$s';
		switch ( $currency_position ) {
			case 'left':
				$format = '%1$s%2$s';
				break;
			case 'right':
				$format = '%2$s%1$s';
				break;
			case 'left_space':
				$format = '%1$s&nbsp;%2$s';
				break;
			case 'right_space':
				$format = '%2$s&nbsp;%1$s';
				break;
			case 'not_display':
				$format = '%2$s';
				break;
		}
		return apply_filters( 'yay_currency_format_currency_position', $format, $currency_position );
	}

	public static function format_currency_symbol( $apply_currency = array() ) {
		$format_currency_position = isset( $apply_currency['currencyPosition'] ) ? self::format_currency_position( $apply_currency['currencyPosition'] ) : '';
		$format                   = false;
		if ( isset( $apply_currency['currencyCodePosition'] ) ) {
			$currency_code = apply_filters( 'yay_currency_switcher_change_currency_code', $apply_currency['currency'] );
			switch ( $apply_currency['currencyCodePosition'] ) {
				case 'left':
					$format = $currency_code . $format_currency_position;
					break;
				case 'right':
					$format = $format_currency_position . $currency_code;
					break;
				case 'left_space':
					$format = $currency_code . ' ' . $format_currency_position;
					break;
				case 'right_space':
					$format = $format_currency_position . ' ' . $currency_code;
					break;
				case 'not_display':
					$format = $format_currency_position;
					break;
			}
		}
		return $format ? $format : $format_currency_position;
	}

	public static function get_rate_fee( $currency = array() ) {
		if ( isset( $currency['fee']['type'] ) && 'percentage' === $currency['fee']['type'] ) {
			$rate_after_fee = (float) $currency['rate'] + ( (float) $currency['rate'] * ( (float) $currency['fee']['value'] / 100 ) );
		} else {
			$rate_after_fee = (float) $currency['rate'] + (float) $currency['fee']['value'];
		}
		return $rate_after_fee;
	}

	public static function enable_rounding_or_fixed_price_currency( $apply_currency = array(), $type = 'cart', $product = false ) {
		$is_fixed_price = FixedPriceHelper::is_set_fixed_price();
		if ( 'cart' === $type ) {
			$flag = $is_fixed_price && FixedPriceHelper::has_fixed_price_product_in_cart( $apply_currency );
		} else {
			$flag = $is_fixed_price && $product && FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $apply_currency );
		}
		$condition = $flag || ( isset( $apply_currency['roundingType'] ) && 'disabled' !== $apply_currency['roundingType'] ) || isset( $_REQUEST['yay_currency_recalculate_fee_cost'] );
		return apply_filters( 'yay_currency_recalculate_in_checkout', $condition, $apply_currency );
	}

	public static function round_price_by_currency( $price = 0, $apply_currency = array() ) {

		if ( ! $price || $price <= 0 ) {
			return $price;
		}

		if ( isset( $apply_currency['roundingType'] ) && 'disabled' !== $apply_currency['roundingType'] ) {
			$rounding_value  = isset( $apply_currency['roundingValue'] ) ? floatval( $apply_currency['roundingValue'] ) : 1;
			$subtract_amount = isset( $apply_currency['subtractAmount'] ) ? floatval( $apply_currency['subtractAmount'] ) : 0;
			switch ( $apply_currency['roundingType'] ) {
				case 'up':
					$price = ceil( $price / $rounding_value ) * $rounding_value - $subtract_amount;
					break;
				case 'down':
					$price = floor( $price / $rounding_value ) * $rounding_value - $subtract_amount;
					break;
				case 'nearest':
					$price = round( $price / $rounding_value ) * $rounding_value - $subtract_amount;
					break;
				default:
					break;
			}
		} elseif ( apply_filters( 'yay_currency_round_product_price', true ) ) {
			$round_type     = apply_filters( 'yay_currency_round_type', PHP_ROUND_HALF_UP );
			$number_decimal = isset( $apply_currency['numberDecimal'] ) ? $apply_currency['numberDecimal'] : get_option( 'woocommerce_price_num_decimals' );
			$number_decimal = ! empty( $number_decimal ) ? $number_decimal : '0';
			$price          = round( $price, $number_decimal, $round_type );
		}

		return $price;
	}

	public static function calculate_price_by_currency( $price = 0, $exclude = false, $apply_currency = array() ) {
		if ( $apply_currency ) {
			$rate_after_fee = self::get_rate_fee( $apply_currency );
			$price          = (float) $price * $rate_after_fee;
			if ( ! $exclude ) {
				$price = self::round_price_by_currency( $price, $apply_currency );
			}
		}
		return $price;
	}

	public static function convert_product_price( $product_price, $product, $apply_currency, $is_cart ) {

		$product_price = self::calculate_price_by_currency( $product_price, false, $apply_currency );

		$fixed_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $product, $product_price, $apply_currency );

		if ( $is_cart && FixedPriceHelper::is_set_fixed_price() ) {
			$fixed_price = apply_filters( 'yay_currency_get_price_fixed_by_currency', $fixed_price, $product, $apply_currency, $product_price );
		}

		return apply_filters( 'yay_currency_get_price_by_currency', $fixed_price, $product, $apply_currency );

	}

	public static function calculate_price_by_currency_html( $apply_currency = array(), $original_price = 0, $quantity = 1 ) {
		$rate_fee        = self::get_rate_fee( $apply_currency );
		$price           = $original_price * $rate_fee;
		$price           = self::round_price_by_currency( $price, $apply_currency );
		$formatted_price = self::get_formatted_total_by_convert_currency( $price * $quantity, $apply_currency, $apply_currency['currency'] );
		return $formatted_price;
	}

	public static function calculate_custom_price_by_currency_html( $apply_currency = array(), $price = 0 ) {
		$formatted_price = self::get_formatted_total_by_convert_currency( $price, $apply_currency, $apply_currency['currency'] );
		return $formatted_price;
	}

	public static function converted_approximately_html( $price_html = '', $hide_label = false ) {
		$class = 'yay-currency-checkout-converted-approximately';
		$html  = " <span class='" . esc_attr( $class ) . "'>(~$price_html)</span>";
		if ( wc_tax_enabled() && WC()->cart->display_prices_including_tax() ) {
			if ( apply_filters( 'yay_currency_enable_label_incl_tax', false ) && ! $hide_label ) {
				$html .= ' <small class="tax_label">' . WC()->countries->inc_tax_or_vat() . '</small>';
			}
		} elseif ( apply_filters( 'yay_currency_enable_label_excl_tax', false ) && ! $hide_label ) {
			$html .= ' <small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>';
		}

		return $html;
	}

	public static function reverse_calculate_price_by_currency( $price = 0, $apply_currency = array() ) {
		if ( ! $apply_currency ) {
			$apply_currency = self::detect_current_currency();
		}
		$rate_fee = self::get_rate_fee( $apply_currency );
		return $rate_fee ? (float) ( $price / $rate_fee ) : $price;
	}

	public static function is_current_fallback_currency( $currencies_data = array() ) {

		if ( ! $currencies_data || ! isset( $currencies_data['current_currency'] ) || ! $currencies_data['fallback_currency'] ) {
			return false;
		}

		if ( $currencies_data['current_currency']['currency'] === $currencies_data['fallback_currency']['currency'] ) {
			return true;
		}

		return false;
	}

	public static function is_checkout_in_fallback() {
		$default_currency_code              = Helper::default_currency_code();
		$fallback_currency_code             = get_option( 'yay_currency_checkout_fallback_currency', $default_currency_code );
		$checkout_different_currency_enable = get_option( 'yay_currency_checkout_different_currency', 0 );
		return $checkout_different_currency_enable && $default_currency_code !== $fallback_currency_code ? true : false;
	}

	public static function get_fallback_currency( $converted_currency = array() ) {
		$default_currency_code = Helper::default_currency_code();
		$converted_currency    = $converted_currency ? $converted_currency : self::converted_currency();
		$fallback_currency     = self::get_currency_by_currency_code( $default_currency_code, $converted_currency );

		if ( isset( $fallback_currency['status'] ) && 0 === intval( $fallback_currency['status'] ) && self::is_checkout_in_fallback() ) {
			$fallback_currency_code = get_option( 'yay_currency_checkout_fallback_currency' );
			$fallback_currency      = self::get_currency_by_currency_code( $fallback_currency_code, $converted_currency );
		}

		return $fallback_currency ? $fallback_currency : reset( $converted_currency );
	}

	public static function get_current_and_fallback_currency( $apply_currency = array(), $converted_currency = array() ) {
		return array(
			'current_currency'  => $apply_currency ? $apply_currency : self::detect_current_currency(),
			'fallback_currency' => self::get_fallback_currency( $converted_currency ),
		);
	}

	public static function get_formatted_total_by_convert_currency( $price = 0, $convert_currency = array(), $yay_currency = '', $ex_tax_label = false ) {
		$format                   = self::format_currency_symbol( $convert_currency );
		$default_currency_options = Helper::set_default_currency_options( $convert_currency );
		$args                     = array(
			'ex_tax_label'       => $ex_tax_label,
			'currency'           => $yay_currency,
			'decimal_separator'  => $default_currency_options['decimalSeparator'],
			'thousand_separator' => $default_currency_options['thousandSeparator'],
			'decimals'           => $default_currency_options['numberDecimal'],
			'price_format'       => $format,
		);

		$formatted_total = wc_price( $price, $args );
		return $formatted_total;

	}

	// ORDER CURRENCY

	public static function get_total_by_order( $order ) {
		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$order_total = $order->get_total();
		} else {
			$order_id    = $order->get_id();
			$order_total = get_post_meta( $order_id, '_order_total', true );
		}
		return $order_total;
	}

	public static function get_currency_code_by_order( $order ) {
		if ( Helper::check_custom_orders_table_usage_enabled() ) {
			$order_currency_code = ! empty( $order->get_currency() ) ? $order->get_currency() : Helper::default_currency_code();
		} else {
			$order_id            = $order->get_id();
			$order_currency_code = get_post_meta( $order_id, '_order_currency', true ) ? get_post_meta( $order_id, '_order_currency', true ) : Helper::default_currency_code();
		}
		return $order_currency_code;
	}

	public static function get_rate_fee_by_order( $order ) {
		$currency_code  = $order->get_currency();
		$order_currency = self::get_currency_by_currency_code( $currency_code );
		if ( ! $order_currency ) {
			$order_rate = RateHelper::get_rate_fee_from_currency_not_exists_in_list( $currency_code );
		} else {
			$order_rate = self::get_rate_fee( $order_currency );
		}
		return $order_rate;
	}

	public static function get_order_currency_by_order_id( $order_id, $converted_currency = array() ) {
		$converted_currency = $converted_currency ? $converted_currency : self::converted_currency();
		$order              = wc_get_order( $order_id );
		if ( ! $order ) {
			return $converted_currency ? reset( $converted_currency ) : false;
		}

		$currency_code  = self::get_currency_code_by_order( $order );
		$order_currency = self::get_currency_by_currency_code( $currency_code, $converted_currency );

		return $order_currency ? $order_currency : reset( $converted_currency );

	}

	public static function format_price_currency( $price, $apply_currency = false ) {
		if ( ! $apply_currency ) {
			$apply_currency = self::get_default_apply_currency( self::converted_currency() );
		}

		$args  = Helper::set_default_currency_options( $apply_currency );
		$price = number_format( $price, $args['numberDecimal'], $args['decimalSeparator'], $args['thousandSeparator'] );

		return $price;
	}

	// PAYMENT
	public static function filter_payment_methods_by_currency( $currency = array(), $available_gateways = array() ) {
		if ( ! $currency || array( 'all' ) === $currency['paymentMethods'] ) {
			return $available_gateways;
		}
		$allowed_payment_methods = $currency['paymentMethods'];
		$filtered                = array_filter(
			$available_gateways,
			function ( $key ) use ( $allowed_payment_methods ) {
				return in_array( $key, $allowed_payment_methods );
			},
			ARRAY_FILTER_USE_KEY
		);
		$available_gateways      = $filtered;
		return $available_gateways;
	}

	// return price default (wp-json/wc/v3/products?consumer_key=&consumer_secret=&per_page=&page=
	public static function is_wc_json_products() {
		return isset( $_REQUEST['consumer_key'] ) && isset( $_REQUEST['consumer_secret'] );
	}
}