<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

// Link plugin: https://pluginrepublic.com/wordpress-plugins/woocommerce-product-add-ons-ultimate/

class WooCommerceProductAddOnsUltimate {
	use SingletonTrait;

	private $apply_currency = array();

	public function __construct() {

		if ( ! defined( 'PEWC_PLUGIN_VERSION' ) ) {
			return;
		}

		$this->apply_currency = YayCurrencyHelper::detect_current_currency();

		add_action( 'yay_currency_set_cart_contents', array( $this, 'product_addons_set_cart_contents' ), 10, 4 );

		add_filter( 'pewc_after_add_cart_item_data', array( $this, 'pewc_after_add_cart_item_data' ), 10, 1 );
		add_filter( 'yay_currency_product_price_3rd_with_condition', array( $this, 'get_price_with_options' ), 10, 2 );
		add_filter( 'yay_currency_get_price_default_in_checkout_page', array( $this, 'get_price_default_in_checkout_page' ), 10, 2 );

		add_filter( 'pewc_filter_field_price', array( $this, 'pewc_yay_currency_convert_price' ), 10, 3 );
		add_filter( 'pewc_filter_option_price', array( $this, 'pewc_yay_currency_convert_price' ), 10, 3 );

		add_filter( 'yay_currency_is_original_product_price', array( $this, 'is_original_product_price' ), 10, 3 );
		add_filter( 'pewc_price_with_extras_before_calc_totals', array( $this, 'pewc_price_with_extras_before_calc_totals' ), 10, 2 );
	}

	public function is_original_product_price( $flag, $price, $product ) {
		$changes = $product->get_changes();
		if ( is_array( $changes ) && isset( $changes['price'] ) && $price === $changes['price'] ) {
			return true;
		}
		return $flag;
	}

	public function pewc_price_with_extras_before_calc_totals( $price, $cart_item ) {
		$currency_code_when_add_to_cart = $cart_item['product_extras']['yay_currency'] ?? '';
		if ( ! empty( $currency_code_when_add_to_cart ) ) {
			$current_currency = YayCurrencyHelper::get_current_currency();
			if ( $current_currency['currency'] !== $currency_code_when_add_to_cart ) {
				$currency_when_add_to_cart = YayCurrencyHelper::get_currency_by_currency_code( $currency_code_when_add_to_cart );
				$base_price                = YayCurrencyHelper::reverse_calculate_price_by_currency( $cart_item['product_extras']['price_with_extras'], $currency_when_add_to_cart );

				$price = YayCurrencyHelper::calculate_price_by_currency( $base_price, false, $current_currency );

			}
		}
		return $price;
	}
	public function product_addons_set_cart_contents( $cart_contents, $cart_item_key, $cart_item, $apply_currency ) {
		$product_extras = isset( $cart_item['product_extras'] ) ? $cart_item['product_extras'] : false;
		if ( $product_extras && isset( $product_extras['yay_currency'] ) ) {
			$product_id                = $cart_item['variation_id'] ? $cart_item['variation_id'] : $cart_item['product_id'];
			$currency_code             = $product_extras['yay_currency'];
			$current_currency          = YayCurrencyHelper::get_currency_by_currency_code( $currency_code );
			$price_with_extras_default = YayCurrencyHelper::reverse_calculate_price_by_currency( $product_extras['price_with_extras'], $current_currency );
			//$product_price_with_currency = apply_filters( 'yay_currency_convert_price', $price_with_extras_default, $apply_currency );
			//$product_price_with_currency = FixedPriceHelper::get_price_fixed_by_apply_currency( $cart_item['data'], $product_price_with_currency, $apply_currency );
			$price_with_extras = $product_extras['yay_currency'] === $apply_currency['currency'] ? $product_extras['price_with_extras'] : apply_filters( 'yay_currency_convert_price', $price_with_extras_default, $apply_currency );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_product_price_with_extras_by_currency', $price_with_extras );
			SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_product_price_with_extras_by_default', $price_with_extras_default );

			$cfw_order_bump_id = isset( $cart_item['_cfw_order_bump_id'] ) ? $cart_item['_cfw_order_bump_id'] : false;
			if ( $cfw_order_bump_id ) {
				$discount_type = get_post_meta( $cfw_order_bump_id, 'cfw_ob_discount_type', true );
				$discount      = get_post_meta( $cfw_order_bump_id, 'cfw_ob_offer_discount', true );
				if ( 'percent' === $discount_type && $discount > 0 ) {
					$discount_value_default = $price_with_extras_default * ( $discount / 100 );
					$discount_value         = $price_with_extras * ( $discount / 100 );
				} else {
					$discount_value_default = $discount;
					$discount_value         = YayCurrencyHelper::calculate_price_by_currency( $discount, false, $this->apply_currency );
				}

				SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_product_price_with_extras_by_currency', $price_with_extras - $discount_value );
				SupportHelper::set_cart_item_objects_property( $cart_contents[ $cart_item_key ]['data'], 'yay_currency_product_price_with_extras_by_default', $price_with_extras_default - $discount_value_default );

			}
		}
	}

	public function pewc_after_add_cart_item_data( $cart_item_data ) {
		$product_extras = isset( $cart_item_data['product_extras'] ) && ! empty( $cart_item_data['product_extras'] ) ? $cart_item_data['product_extras'] : false;
		if ( $product_extras ) {
			$cart_item_data['product_extras']['yay_currency'] = $this->apply_currency['currency'];
		}
		return $cart_item_data;
	}

	public function get_price_with_options( $price, $product ) {
		$product_price_with_extras = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_product_price_with_extras_by_currency' );
		if ( $product_price_with_extras ) {
			return $product_price_with_extras;
		}
		return $price;
	}

	public function get_price_default_in_checkout_page( $price, $product ) {
		$product_price_with_extras_default = SupportHelper::get_cart_item_objects_property( $product, 'yay_currency_product_price_with_extras_by_default' );
		if ( $product_price_with_extras_default ) {
			return $product_price_with_extras_default;
		}
		return $price;
	}


	public function pewc_yay_currency_convert_price( $option_price, $item, $product ) {

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $this->apply_currency ) ) {
			return $option_price;
		}

		$option_price = YayCurrencyHelper::calculate_price_by_currency( $option_price, false, $this->apply_currency );

		return $option_price;

	}
}
