<?php

namespace Yay_Currency\Engine\FEPages;

use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\SupportHelper;

use Yay_Currency\Utils\SingletonTrait;

defined( 'ABSPATH' ) || exit;
class WooCommerceCurrency {
	use SingletonTrait;

	private $default_currency;
	private $converted_currency = array();
	private $apply_currency     = array();
	private $fallback_currency  = array();

	private $currencies_data               = array();
	private $is_dis_checkout_diff_currency = false;
	private $country_info;

	public function __construct() {
		add_action( 'init', array( $this, 'yay_currency_init' ) );
	}

	public function yay_currency_init() {

		if ( YayCurrencyHelper::is_reload_permitted() ) {
			$this->default_currency   = Helper::default_currency_code();
			$this->converted_currency = YayCurrencyHelper::converted_currency();
			$this->apply_currency     = YayCurrencyHelper::get_apply_currency( $this->converted_currency );

			YayCurrencyHelper::set_cookies( $this->apply_currency, $this->converted_currency );

			$this->currencies_data   = YayCurrencyHelper::get_current_and_fallback_currency( $this->apply_currency, $this->converted_currency );
			$this->fallback_currency = isset( $this->currencies_data['fallback_currency'] ) ? $this->currencies_data['fallback_currency'] : $this->apply_currency;

			$this->is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $this->apply_currency );

			$hooks = YayCurrencyHelper::get_yay_currency_hooks();

			add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_scripts' ) );

			$price_priority = SupportHelper::get_product_prices_filters_priority();

			foreach ( $hooks['price_hooks'] as $price_hook ) {
				add_filter( $price_hook, array( $this, 'custom_raw_price' ), $price_priority, 2 );
			}

			add_filter( 'woocommerce_get_variation_prices_hash', array( $this, 'custom_variation_price_hash' ), $price_priority, 1 );

			add_filter( 'woocommerce_available_payment_gateways', array( $this, 'conditional_payment_gateways' ), 10, 1 );

			add_action( 'woocommerce_before_mini_cart', array( $this, 'custom_mini_cart_price' ), 999 );

			// Pass currency code into cart_contents ( Support for 3rd plugins: Abandoned Cart,...)
			add_filter( 'woocommerce_get_cart_contents', array( $this, 'woocommerce_get_cart_contents' ), 10, 1 );

			// WooCommerce Coupon Hooks
			foreach ( $hooks['coupon_hooks'] as $coupon_hook => $coupon_callback ) {
				add_filter( $coupon_hook, array( $this, $coupon_callback ), 10, 2 );
			}

			// Custom price fees
			add_action( 'woocommerce_cart_calculate_fees', array( $this, 'recalculate_cart_fees' ), SupportHelper::get_fee_priority(), 1 );

			// WooCommerce Checkout Create Order Hooks
			foreach ( $hooks['create_order_hooks'] as $hook_type => $param ) {
				$filter_priority = SupportHelper::get_product_prices_filters_priority( 10, $hook_type );
				add_action( $hook_type, array( $this, $hook_type ), $filter_priority, $param );
			}

			// Add Notice Before Checkout Form.
			add_action( 'woocommerce_before_checkout_form', array( $this, 'add_notice_before_checkout_form' ), 999 );

			if ( $this->is_dis_checkout_diff_currency ) {
				// WooCommerce Checkout Different Currency Hooks
				foreach ( $hooks['checkout_diff_hooks'] as $hook_type => $param ) {
					add_filter( $hook_type, array( $this, $hook_type ), 10, $param );
				}
			}

			// Shipping Methods & Free shipping with minimum amount
			foreach ( $hooks['shipping_hooks'] as $hook_type => $param ) {
				add_filter( $hook_type, array( $this, $hook_type ), 20, $param );
			}

			// Order Details in My Account
			foreach ( $hooks['order_details_hooks'] as $hook_type => $param ) {
				add_filter( $hook_type, array( $this, $hook_type ), 10, $param );
			}

			// Price Format Currency
			$format_priority = SupportHelper::get_format_filters_priority();
			foreach ( $hooks['price_format_hooks'] as $hook_type => $param ) {
				add_filter( $hook_type, array( $this, $hook_type ), $format_priority, $param );
			}

			// Approximate price
			$approximate_price_info = Helper::approximate_price_info();
			if ( isset( $approximate_price_info['status'] ) && $approximate_price_info['status'] ) {
				$this->country_info      = CountryHelper::get_country_info_from_IP();
				$args_show_on            = isset( $approximate_price_info['show_on'] ) ? $approximate_price_info['show_on'] : array();
				$is_show_all             = in_array( 'default', $args_show_on, true );
				$approximate_price_hooks = YayCurrencyHelper::get_approximate_price_hooks();
				$priority                = SupportHelper::get_approximate_price_filters_priority();

				foreach ( $approximate_price_hooks as $key => [$hook, $method, $accepted_args, $condition] ) {
					if ( $condition && ( $is_show_all || in_array( $key, $args_show_on, true ) ) ) {
						add_filter( $hook, array( $this, $method ), $priority, $accepted_args );
					}
				}
			}
		}

	}

	public function is_fallback_currency() {
		if ( YayCurrencyHelper::checkout_in_fallback_currency( $this->apply_currency ) && ! YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) && $this->default_currency !== $this->fallback_currency['currency'] ) {
			return true;
		}
		return false;
	}

	public function enqueue_scripts() {

		$suffix = defined( 'YAY_CURRENCY_SCRIPT_DEBUG' ) ? '' : '.min';

		$localize_args = array(
			'admin_url'               => admin_url( 'admin.php?page=wc-settings' ),
			'ajaxurl'                 => admin_url( 'admin-ajax.php' ),
			'nonce'                   => wp_create_nonce( 'yay-currency-nonce' ),
			'isShowOnMenu'            => get_option( 'yay_currency_show_menu', 0 ),
			'isPolylangCompatible'    => get_option( 'yay_currency_polylang_compatible', 0 ),
			'isDisplayFlagInSwitcher' => get_option( 'yay_currency_show_flag_in_switcher', 1 ),
			'yayCurrencyPluginURL'    => YAY_CURRENCY_PLUGIN_URL,
			'converted_currency'      => $this->converted_currency,
			'checkout_diff_currency'  => get_option( 'yay_currency_checkout_different_currency', 0 ),
			'fallback_currency_code'  => isset( $this->fallback_currency['currency'] ) ? $this->fallback_currency['currency'] : $this->default_currency,
			'default_currency_code'   => $this->default_currency,
			'show_approximate_price'  => SupportHelper::display_approximately_converted_price( $this->apply_currency ) ? 'yes' : 'no',
			'cart_page'               => function_exists( 'is_cart' ) ? is_cart() : '',
			'cookie_lifetime_days'    => YayCurrencyHelper::get_lifetime_days(),
			'hide_dropdown_switcher'  => YayCurrencyHelper::detect_allow_hide_dropdown_currencies(),
			'cookie_name'             => YayCurrencyHelper::get_cookie_name(),
			'cookie_switcher_name'    => YayCurrencyHelper::get_cookie_name( 'switcher' ),
			'cache_compatible'        => Helper::cache_enable(),
			'current_theme'           => Helper::get_current_theme(),
		);

		if ( $this->is_dis_checkout_diff_currency ) {
			$localize_args['checkout_notice_html'] = apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $this->currencies_data, Helper::get_current_theme() );
			$localize_args['checkout_page']        = function_exists( 'is_checkout' ) ? is_checkout() : '';
		}

		wp_enqueue_style(
			'yay-currency-frontend-style',
			YAY_CURRENCY_PLUGIN_URL . 'src/styles.css',
			array(),
			YAY_CURRENCY_VERSION
		);

		$force_payment = CountryHelper::force_payment_country();

		if ( $force_payment ) {
			$localize_args['force_payment']     = $force_payment;
			$localize_args['country_code']      = is_checkout() ? CountryHelper::detect_country_code() : '';
			$total_class                        = is_checkout() ? apply_filters( 'yay_currency_total_checkout_block_class', '.wp-block-woocommerce-checkout .wc-block-components-totals-footer-item' ) : apply_filters( 'yay_currency_total_cart_block_class', '.wp-block-woocommerce-cart .wc-block-components-totals-footer-item' );
			$localize_args['total_block_class'] = $total_class;
		}

		$force_currency = Helper::detect_force_currency_by_payment_method();
		if ( $force_currency ) {
			$localize_args['force_currency'] = $force_currency;
		}

		if ( Helper::use_yay_currency_params() ) {
			$localize_args['yay_currency_use_params'] = 'yes';
			$param_name                               = apply_filters( 'yay_currency_param_name', 'yay-currency' );
			if ( isset( $_REQUEST[ $param_name ] ) ) {
				$localize_args['yay_currency_param__name'] = $param_name;
			}
		}

		wp_enqueue_script( 'yay-currency-callback', YAY_CURRENCY_PLUGIN_URL . 'src/callback' . $suffix . '.js', array( 'jquery' ), YAY_CURRENCY_VERSION, true );

		do_action( 'yay_currency_callback_enqueue_scripts', $localize_args, $force_payment, $this->apply_currency );

		wp_enqueue_script( 'yay-currency-frontend-script', YAY_CURRENCY_PLUGIN_URL . 'src/script' . $suffix . '.js', array( 'jquery' ), YAY_CURRENCY_VERSION, true );

		wp_localize_script(
			'yay-currency-frontend-script',
			'yayCurrency',
			apply_filters( 'yay_currency_localize_args', $localize_args )
		);

		wp_enqueue_script( 'yay-currency-third-party', YAY_CURRENCY_PLUGIN_URL . 'src/compatibles/third-party' . $suffix . '.js', array( 'jquery' ), YAY_CURRENCY_VERSION, true );

		do_action( 'yay_currency_enqueue_scripts' );

	}

	public function custom_raw_price( $price, $product ) {

		$this->apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );

		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			if ( $this->is_fallback_currency() ) {
				$price = apply_filters( 'yay_currency_get_price_fallback_in_checkout_page', $price, $product, $this->fallback_currency );
			} else {
				$price = apply_filters( 'yay_currency_get_price_default_in_checkout_page', $price, $product );
			}
			return apply_filters( 'yay_currency_get_product_price_default_conditions', $price, $product, $this->apply_currency, $this->fallback_currency, $this->converted_currency );
		}

		if ( SupportHelper::detect_original_product_price( false, $price, $product ) ) {
			return $price;
		}

		$conditions_3rd_plugin = apply_filters( 'yay_currency_3rd_plugins_conditions', false, $product, $this->apply_currency );

		if ( is_checkout() || is_cart() || wp_doing_ajax() || $conditions_3rd_plugin ) {

			$price_with_conditions = SupportHelper::detect_price_with_conditions( $price, $product, $this->apply_currency );

			if ( $price_with_conditions ) {
				return $price_with_conditions;
			}

			return YayCurrencyHelper::convert_product_price( $price, $product, $this->apply_currency, true );

		}

		return YayCurrencyHelper::convert_product_price( $price, $product, $this->apply_currency, false );

	}

	public function custom_variation_price_hash( $price_hash ) {
		$cookie_name = YayCurrencyHelper::get_cookie_name();
		if ( isset( $_COOKIE[ $cookie_name ] ) ) {
			$price_hash[] = (int) sanitize_key( $_COOKIE[ $cookie_name ] );
		}
		return $price_hash;
	}

	public function conditional_payment_gateways( $available_gateways ) {

		if ( ! $this->apply_currency ) {
			return $available_gateways;
		}

		if ( self::should_skip_fallback() ) {
			$available_gateways = YayCurrencyHelper::filter_payment_methods_by_currency( $this->fallback_currency, $available_gateways );
			return $available_gateways;
		}

		$available_gateways = YayCurrencyHelper::filter_payment_methods_by_currency( $this->apply_currency, $available_gateways );
		$available_gateways = apply_filters( 'yay_currency_available_gateways', $available_gateways, $this->apply_currency );
		return $available_gateways;
	}

	public function custom_mini_cart_price() {
		if ( self::should_skip_fallback() || is_cart() || is_checkout() ) {
			return false;
		}
		WC()->cart->calculate_totals();
	}

	public function woocommerce_get_cart_contents( $cart_contents ) {

		foreach ( $cart_contents as $cart_item_key => $cart_item ) {
			$cart_contents[ $cart_item_key ]['yay_currency_applied'] = $this->apply_currency;
			$cart_contents[ $cart_item_key ]['yay_currency_rate']    = YayCurrencyHelper::get_rate_fee( $this->apply_currency );
			do_action( 'yay_currency_set_cart_contents', $cart_contents, $cart_item_key, $cart_item, $this->apply_currency );
		}

		return $cart_contents;

	}

	public function change_coupon_amount( $price, $coupon ) {

		$coupon_types = apply_filters( 'yay_currency_coupon_types', array( 'percent' ), $coupon );

		if ( $coupon->is_type( $coupon_types ) || empty( $price ) || ! $price ) {
			return $price;
		}

		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			// Fallback Currency

			if ( $this->is_fallback_currency() ) {
				$price = apply_filters( 'yay_currency_get_coupon_amount_fallback_currency', $price, $this->fallback_currency );
			} else {
				$price = apply_filters( 'yay_currency_get_amount_coupon_price_fallback_currency', $price, $coupon, $this->currencies_data );
			}

			return $price;
		}

		$converted_coupon_price = YayCurrencyHelper::calculate_price_by_currency( $price, true, $this->apply_currency );
		$converted_coupon_price = apply_filters( 'yay_currency_coupon_get_amount', $converted_coupon_price, $coupon, $this->apply_currency );

		return $converted_coupon_price;

	}

	public function change_coupon_min_max_amount( $price, $coupon ) {

		if ( empty( $price ) || ! $price ) {
			return $price;
		}

		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			// Fallback Currency
			if ( $this->is_fallback_currency() ) {
				$price = apply_filters( 'yay_currency_get_coupon_amount_fallback_currency', $price, $this->fallback_currency );
			} else {
				$price = apply_filters( 'yay_currency_get_min_max_amount_coupon_price_fallback_currency', $price, $this->currencies_data );
			}
			return $price;
		}

		$converted_coupon_price = YayCurrencyHelper::calculate_price_by_currency( $price, true, $this->apply_currency );
		$converted_coupon_price = apply_filters( 'yay_currency_coupon_get_amount', $converted_coupon_price, $coupon, $this->apply_currency );

		return $converted_coupon_price;

	}

	public function recalculate_cart_fees( $cart ) {

		if ( SupportHelper::detect_cart_fees_original() ) {
			return;
		}
		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			if ( $this->is_fallback_currency() ) {
				do_action( 'yay_currency_recalculate_cart_fees_fallback_currency', $cart, $this->fallback_currency );
			}
			return;
		}

		foreach ( $cart->get_fees() as $fee ) {
			if ( ! isset( $fee->yay_currency_fee_converted ) || ! $fee->yay_currency_fee_converted ) {
				$amount                          = YayCurrencyHelper::calculate_price_by_currency( $fee->amount, true, $this->apply_currency );
				$amount                          = apply_filters( 'yay_currency_get_fee_amount_after_calculate', $amount, $fee );
				$fee->amount                     = $amount;
				$fee->yay_currency_fee_converted = true;
			}
		}
	}

	public function woocommerce_checkout_create_order( $order, $data ) {
		// Fallback Currency
		if ( $this->is_fallback_currency() ) {
			do_action( 'yay_currency_create_order_fallback_currency', $order, $data, $this->fallback_currency );
		} else {
			$current_currency = $this->currencies_data['current_currency'];
			if ( 0 === intval( $current_currency['status'] ) && YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return;
			}
			do_action( 'yay_currency_set_order_original_total', $order, $data, $this->default_currency, $this->fallback_currency, $current_currency );
		}
	}

	public function woocommerce_checkout_create_order_line_item( $item, $cart_item_key, $values, $order ) {
		if ( $this->is_fallback_currency() ) {
			if ( ! YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' ) ) {
				do_action( 'yay_currency_create_create_order_line_item_fallback_currency', $item, $cart_item_key, $values, $order, $this->fallback_currency );
			}
		}
	}

	public function woocommerce_checkout_create_order_shipping_item( $item, $package_key, $package, $order ) {
		if ( $this->is_fallback_currency() ) {
			if ( ! YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' ) ) {
				do_action( 'yay_currency_create_order_shipping_item_fallback_currency', $item, $package_key, $package, $order, $this->fallback_currency );
			}
		}

	}

	public function woocommerce_checkout_create_order_fee_item( $item, $fee_key, $fee, $order ) {
		if ( $this->is_fallback_currency() ) {
			if ( ! YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' ) ) {
				do_action( 'yay_currency_create_order_fee_item_fallback_currency', $item, $fee_key, $fee, $order, $this->fallback_currency );
			}
		}

	}

	public function woocommerce_checkout_create_order_tax_item( $item, $tax_rate_id, $order ) {
		if ( $this->is_fallback_currency() ) {
			if ( ! YayCurrencyHelper::detect_woocommerce_blocks_page( 'checkout' ) ) {
				if ( $tax_rate_id && apply_filters( 'woocommerce_cart_remove_taxes_zero_rate_id', 'zero-rated' ) !== $tax_rate_id ) {
					do_action( 'yay_currency_create_order_tax_item_fallback_currency', $item, $tax_rate_id, $order, $this->fallback_currency );
				}
			}
		}

	}

	public function add_notice_before_checkout_form() {

		if ( $this->is_dis_checkout_diff_currency ) {
			// Add Notice Payment Methods
			$notice_payment_methods = apply_filters( 'yay_currency_checkout_notice_payment_methods', true, $this->apply_currency );
			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) || ! $notice_payment_methods ) {
				return;
			}

			do_action( 'yay_currency_before_notice_checkout_payment_methods', $this->currencies_data );
			echo wp_kses_post( apply_filters( 'yay_currency_notice_checkout_payment_methods', '', $this->currencies_data, Helper::get_current_theme() ) );

			if ( CountryHelper::force_payment_country() ) {
				YayCurrencyHelper::display_content_payment_notice_html();
			}

			do_action( 'yay_currency_after_notice_checkout_payment_methods', $this->currencies_data );

		} elseif ( CountryHelper::force_payment_country() ) {
			echo '<div class="yay-currency-checkout-notice-force-payment-wrapper"></div>';
			$html = CountryHelper::display_force_payment_notice_html( false );
			if ( $html ) {
				YayCurrencyHelper::display_content_payment_notice_html( $html );
			}
		} elseif ( Helper::detect_force_currency_by_payment_method() ) {
			echo '<div class="yay-currency-checkout-notice-force-payment-wrapper"></div>';
		}

	}

	public function woocommerce_cart_product_subtotal( $product_subtotal, $product, $quantity, $cart ) {
		if ( is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $product_subtotal;
			}
			$fallback_product_price    = $product->get_price();
			$product_price             = apply_filters( 'yay_currency_get_product_price_default_currency', $fallback_product_price, $product, 1 );
			$original_product_subtotal = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $product_price, $quantity );
			$original_product_subtotal = apply_filters( 'yay_currency_get_original_product_subtotal', $original_product_subtotal, $product_price, $product, $quantity, $this->fallback_currency );
			$converted_approximately   = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				return $original_product_subtotal;
			}
			$product_price              = YayCurrencyHelper::calculate_price_by_currency( $fallback_product_price, false, $this->apply_currency );
			$product_price              = apply_filters( 'yay_currency_get_product_price_apply_currency', $product_price, $product, 1 );
			$converted_product_subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $product_price * $quantity, $this->apply_currency, $this->apply_currency['currency'] );
			$converted_product_subtotal = apply_filters( 'yay_currency_checkout_converted_product_subtotal_fixed', $converted_product_subtotal, $product, $this->apply_currency, $quantity );
			//  Display approximate price only on the checkout page
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $converted_product_subtotal;
			}
			$converted_product_subtotal_html = YayCurrencyHelper::converted_approximately_html( $converted_product_subtotal );
			$product_subtotal                = $original_product_subtotal . $converted_product_subtotal_html;
		}
		return $product_subtotal;
	}

	public function woocommerce_cart_subtotal( $price, $compound, $cart ) {
		if ( is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $price;
			}

			$cart_subtotal = apply_filters( 'yay_currency_get_cart_subtotal_default', 0 );

			$subtotal_price = apply_filters( 'yay_currency_checkout_get_subtotal_price', $cart_subtotal, $this->apply_currency, $this->fallback_currency, $this->converted_currency );

			$original_subtotal = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $subtotal_price );

			if ( $this->default_currency !== $this->fallback_currency['currency'] ) {
				$original_subtotal = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $subtotal_price );
				$original_subtotal = apply_filters( 'yay_currency_get_original_cart_subtotal', $original_subtotal, $this->fallback_currency );
			}

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				return $original_subtotal;
			}
			$converted_subtotal = YayCurrencyHelper::calculate_price_by_currency_html( $this->currencies_data['current_currency'], $subtotal_price );
			$converted_subtotal = apply_filters( 'yay_currency_checkout_converted_subtotal_fixed', $converted_subtotal, $this->apply_currency );
			//  Display approximate price only on the checkout page
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $converted_subtotal;
			}
			$converted_product_subtotal_html = YayCurrencyHelper::converted_approximately_html( $converted_subtotal );
			$price                           = $original_subtotal . $converted_product_subtotal_html;
		}
		return $price;
	}

	public function woocommerce_cart_totals_coupon_html( $coupon_html, $coupon, $discount_amount_html ) {
		if ( is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $coupon_html;
			}

			$discount_type   = $coupon->get_discount_type();
			$discount_amount = (float) $coupon->get_amount();

			if ( 'percent' !== $discount_type ) {
				$custom_coupon_html = apply_filters( 'yay_currency_checkout_formatted_fixed_cart_or_product_discount_price', $this->currencies_data, $coupon, $coupon_html, $discount_type, $discount_amount );
			} else {
				$custom_coupon_html = apply_filters( 'yay_currency_checkout_formatted_percent_discount_price', $this->currencies_data, $coupon, $coupon_html, $discount_amount );
			}
			return $custom_coupon_html;
		}
		return $coupon_html;
	}

	public function woocommerce_cart_shipping_method_full_label( $label, $method ) {
		if ( is_checkout() ) {

			$shipping_fee = (float) $method->cost;

			if ( 'free_shipping' === $method->method_id || ! $shipping_fee || YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $label;
			}

			$shipping_fee                             = apply_filters( 'yay_currency_recalculate_shipping_fee_incl_tax', $shipping_fee, $method, $this->fallback_currency );
			$converted_shipping_fee                   = YayCurrencyHelper::calculate_price_by_currency( $shipping_fee, true, $this->apply_currency );
			$formatted_shipping_fee                   = YayCurrencyHelper::format_price( $converted_shipping_fee );
			$shipping_method_label                    = $method->label;
			$formatted_fallback_currency_shipping_fee = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $shipping_fee );

			$formatted_fallback_currency_shipping_fee = apply_filters( 'yay_currency_get_fallback_currency_shipping_fee', $formatted_fallback_currency_shipping_fee, $shipping_fee, $method, $this->fallback_currency );
			$converted_approximately                  = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				return $shipping_method_label . ': ' . $formatted_fallback_currency_shipping_fee;
			}

			$formatted_shipping_fee = apply_filters( 'yay_currency_formatted_shipping_flat_rate_fee', $formatted_shipping_fee, $method, $this->apply_currency );
			//  Display approximate price only on the checkout page
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $shipping_method_label . ': ' . $formatted_shipping_fee;
			}
			$formatted_shipping_fee_html = YayCurrencyHelper::converted_approximately_html( $formatted_shipping_fee );
			$label                       = $shipping_method_label . ': ' . $formatted_fallback_currency_shipping_fee . $formatted_shipping_fee_html;
		}
		return $label;
	}

	public function woocommerce_cart_totals_fee_html( $cart_totals_fee_html, $fee ) {
		if ( is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $cart_totals_fee_html;
			}

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			$fee_amount              = $fee->amount;
			$fee_amount              = apply_filters( 'yay_currency_recalculate_fee_incl_tax', $fee_amount, $fee );
			$fee_amount_html         = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $fee_amount );
			$fee_amount_html         = apply_filters( 'yay_currency_get_fee_amount_html', $fee_amount_html, $fee_amount, $this->fallback_currency );
			if ( ! $converted_approximately ) {
				return $fee_amount_html;
			}

			$convert_fee_amount      = YayCurrencyHelper::calculate_price_by_currency( $fee_amount, true, $this->apply_currency );
			$convert_fee_amount_html = YayCurrencyHelper::format_price( $convert_fee_amount );
			//  Display approximate price only on the checkout page
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $convert_fee_amount_html;
			}
			$cart_totals_fee_html = $fee_amount_html . YayCurrencyHelper::converted_approximately_html( $convert_fee_amount_html );
		}
		return $cart_totals_fee_html;
	}

	public function woocommerce_cart_tax_totals( $tax_display, $cart ) {
		if ( count( $tax_display ) > 0 && is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $tax_display;
			}
			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			foreach ( $tax_display as $tax_info ) {
				$tax_amount                 = $tax_info->amount;
				$fallback_tax_amount        = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $tax_amount );
				$fallback_tax_amount        = apply_filters( 'yay_currency_get_fallback_tax_amount', $fallback_tax_amount, $tax_amount, $tax_info->tax_rate_id, $this->fallback_currency );
				$tax_info->formatted_amount = $fallback_tax_amount;
				if ( $converted_approximately ) {
					$converted_tax_amount           = YayCurrencyHelper::calculate_price_by_currency( $tax_amount, true, $this->apply_currency );
					$formatted_converted_tax_amount = YayCurrencyHelper::format_price( $converted_tax_amount );
					$formatted_converted_tax_amount = apply_filters( 'yay_currency_checkout_converted_total_tax_fixed', $formatted_converted_tax_amount, $tax_info->tax_rate_id, $this->apply_currency );
					//  Display approximate price only on the checkout page
					if ( SupportHelper::display_approximate_price_checkout_only() ) {
						$tax_info->formatted_amount = $formatted_converted_tax_amount;
					} else {
						$formatted_converted_tax_amount_html = YayCurrencyHelper::converted_approximately_html( $formatted_converted_tax_amount, true );
						$tax_info->formatted_amount         .= $formatted_converted_tax_amount_html;
					}
				}
			}
		}
		return $tax_display;
	}

	public function woocommerce_cart_totals_taxes_total_html( $taxes_total_html ) {
		if ( is_checkout() ) {
			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $taxes_total_html;
			}
			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			$taxes_total             = WC()->cart->get_taxes_total();
			$taxes_total_html        = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $taxes_total );
			$taxes_total_html        = apply_filters( 'yay_currency_get_taxes_total_html', $taxes_total_html, $taxes_total, $this->fallback_currency );
			if ( ! $converted_approximately ) {
				return $taxes_total_html;
			}
			$converted_taxes_total_html = YayCurrencyHelper::calculate_price_by_currency_html( $this->apply_currency, $taxes_total );
			$converted_taxes_total_html = apply_filters( 'yay_currency_checkout_converted_taxes_total_fixed', $converted_taxes_total_html, $this->apply_currency );
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $converted_taxes_total_html;
			}
			$converted_taxes_total_html = YayCurrencyHelper::converted_approximately_html( $converted_taxes_total_html, true );
			$taxes_total_html           = $taxes_total_html . $converted_taxes_total_html;
		}
		return $taxes_total_html;
	}

	public function woocommerce_cart_total( $price ) {
		if ( is_checkout() ) {

			if ( YayCurrencyHelper::is_current_fallback_currency( $this->currencies_data ) ) {
				return $price;
			}

			$total_price    = apply_filters( 'yay_currency_checkout_get_total_price', (float) WC()->cart->total );
			$original_total = YayCurrencyHelper::calculate_price_by_currency_html( $this->fallback_currency, $total_price );
			$original_total = apply_filters( 'yay_currency_get_original_cart_total', $original_total, $total_price, $this->fallback_currency );

			$converted_approximately = SupportHelper::display_approximately_converted_price( $this->apply_currency );
			if ( ! $converted_approximately ) {
				return $original_total;
			}
			$converted_total          = YayCurrencyHelper::calculate_price_by_currency_html( $this->currencies_data['current_currency'], $total_price );
			$enable_fallback_currency = $this->default_currency !== $this->fallback_currency['currency'];
			$converted_total          = apply_filters( 'yay_currency_checkout_converted_total_fixed', $converted_total, $this->apply_currency, $enable_fallback_currency );
			if ( SupportHelper::display_approximate_price_checkout_only() ) {
				return $converted_total;
			}
			$converted_total_html = YayCurrencyHelper::converted_approximately_html( $converted_total, true );
			$price                = $original_total . $converted_total_html;
		}
		return $price;
	}

	public function woocommerce_shipping_local_pickup_instance_option( $value, $key, $shipping ) {

		if ( 'cost' !== $key ) {
			return $value;
		}

		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			if ( $this->is_fallback_currency() ) {
				$value = YayCurrencyHelper::calculate_price_by_currency( $value, true, $this->fallback_currency );
			}
		}

		return $value;
	}

	// Shipping
	public function woocommerce_package_rates( $methods, $package ) {

		if ( ! $methods || apply_filters( 'yay_currency_is_original_shipping_cost', false, $this->apply_currency ) || ! count( array_filter( $methods ) ) ) {
			return $methods;
		}

		do_action( 'yay_currency_recalculate_shipping_cost', $methods, $package, $this->apply_currency, $this->fallback_currency, $this->default_currency );

		$methods_data_args = SupportHelper::detect_shipping_methods_ignore();

		$defaults_args = array(
			'apply_currency'               => $this->apply_currency,
			'fallback_currency'            => $this->fallback_currency,
			'is_fallback_currency'         => $this->is_fallback_currency(),
			'is_original_default_currency' => SupportHelper::detect_is_origin_default_currency( $this->apply_currency ),
		);

		foreach ( $methods as $key => $method ) {
			$shipping_method_id = $method->method_id;
			if ( in_array( $shipping_method_id, $methods_data_args['shipping_methods'], true ) ) {
				continue;
			}
			if ( 'flat_rate' === $shipping_method_id ) {

				$shipping = new \WC_Shipping_Flat_Rate( $method->instance_id );
				// Calculate the costs.
				$rate = array(
					'id'      => $method->id,
					'label'   => $method->label,
					'cost'    => 0,
					'package' => $package,
				);

				$has_fee_costs = false; // True when a cost is set. False if all costs are blank strings.
				$cost          = $shipping->get_option( 'cost' );
				$shipping_args = array(
					'has_fee_costs' => $has_fee_costs,
					'rate_cost'     => $rate['cost'],
					'cost'          => $cost,
				);

				if ( ! empty( $cost ) && ! is_numeric( $cost ) ) {
					$shipping_args = apply_filters( 'yay_currency_recalculate_flat_rate_method_with_shortcode', $shipping_args, $package, $shipping, $defaults_args );
				}

				$data_shipping_class = apply_filters( 'yay_currency_recalculate_flat_rate_shipping_class_cost', $shipping_args, $package, $shipping, $defaults_args );

				$rate['cost'] = $data_shipping_class['rate_cost'];
				$cost         = ! empty( $data_shipping_class['cost'] ) ? $data_shipping_class['cost'] : 0;

				if ( $data_shipping_class['has_fee_costs'] ) {
					$rate['cost'] = apply_filters( 'yay_currency_get_shipping_cost', $rate['cost'], $method, $this->apply_currency );
					$method->set_cost( $rate['cost'] );
				} else {
					$flat_rate_args = wp_parse_args( array( 'cost' => isset( $cost ) ? $cost : '' ), $defaults_args );
					do_action( 'yay_currency_recalculate_flat_rate_method_cost', $method, $rate, $flat_rate_args );
				}
			} else {
				if ( in_array( $shipping_method_id, $methods_data_args['special_methods'] ) ) {
					if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
						do_action( 'yay_currency_fallback_special_shipping_cost', $method, $package, $this->fallback_currency, $this->apply_currency );
						return $methods;
					}

					$original_shipping_cost = $method->cost;
					$special_shipping_cost  = YayCurrencyHelper::calculate_price_by_currency( $original_shipping_cost, true, $this->apply_currency );
					$special_shipping_cost  = apply_filters( 'yay_currency_special_shipping_cost', $special_shipping_cost, $original_shipping_cost, $method, $package, $this->apply_currency );
					$method->cost           = $special_shipping_cost;

					return $methods;

				}
				if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
					return $methods;
				}
				$data = get_option( 'woocommerce_' . $shipping_method_id . '_' . $method->instance_id . '_settings' );
				$data = apply_filters( 'yay_currency_get_data_info_from_shipping_method', $data, $method->method_id, $package['contents_cost'], $this->apply_currency );

				$shipping_cost = isset( $data['cost'] ) ? YayCurrencyHelper::calculate_price_by_currency( $data['cost'], false, $this->apply_currency ) : YayCurrencyHelper::calculate_price_by_currency( $method->get_cost(), false, $this->apply_currency );
				$shipping_cost = apply_filters( 'yay_currency_get_shipping_cost', $shipping_cost, $method, $this->apply_currency );
				$method->set_cost( $shipping_cost );
			}

			// Set tax for shipping method
			$tax_args = wp_parse_args(
				array(
					'cost'      => isset( $cost ) ? $cost : '',
					'rate_cost' => isset( $rate['cost'] ) ? $rate['cost'] : '',
				),
				$defaults_args
			);
			do_action( 'yay_currency_recalculate_tax_shipping_method_cost', $method, $tax_args );
		}

		return $methods;
	}

	public function custom_free_shipping_min_amount( $option, $key, $method ) {

		if ( 'min_amount' !== $key ) {
			return $option;
		}

		if ( ! $option || empty( $option ) ) {
			return $option;
		}

		if ( self::should_skip_fallback() || SupportHelper::detect_is_origin_default_currency( $this->apply_currency ) ) {
			if ( $this->is_fallback_currency() ) {
				$option = YayCurrencyHelper::calculate_price_by_currency( $option, true, $this->fallback_currency );
			}
			return $option;
		}

		$option = YayCurrencyHelper::calculate_price_by_currency( $option, true, $this->apply_currency );
		return $option;
	}

	public function woocommerce_shipping_free_shipping_instance_option( $option, $key, $method ) {
		return self::custom_free_shipping_min_amount( $option, $key, $method );
	}

	public function woocommerce_shipping_free_shipping_option( $option, $key, $method ) {
		return self::custom_free_shipping_min_amount( $option, $key, $method );
	}

	public function woocommerce_get_order_item_totals( $total_rows, $order, $tax_display ) {
		if ( SupportHelper::detect_original_format_order_item_totals( false, $total_rows, $order, $tax_display ) ) {
			return $total_rows;
		}
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			// Fee
			$fees = $order->get_fees();
			if ( $fees ) {
				foreach ( $fees as $id => $fee ) {
					if ( apply_filters( 'woocommerce_get_order_item_totals_excl_free_fees', empty( $fee['line_total'] ) && empty( $fee['line_tax'] ), $id ) ) {
						continue;
					}
					$price_format                          = 'excl' === $tax_display ? $fee->get_total() : $fee->get_total() + $fee->get_total_tax();
					$total_rows[ 'fee_' . $fee->get_id() ] = array(
						'label' => $fee->get_name() . ':',
						'value' => YayCurrencyHelper::get_formatted_total_by_convert_currency( $price_format, $convert_currency, $currency_code ),
					);

				}
			}
			// Tax for tax exclusive prices.
			if ( 'excl' === $tax_display && wc_tax_enabled() ) {
				if ( 'itemized' === get_option( 'woocommerce_tax_total_display' ) ) {
					foreach ( $order->get_tax_totals() as $code => $tax ) {
						$formatted_tax_amount                  = YayCurrencyHelper::get_formatted_total_by_convert_currency( $tax->amount, $convert_currency, $currency_code );
						$total_rows[ sanitize_title( $code ) ] = array(
							'label' => $tax->label . ':',
							'value' => $formatted_tax_amount, // $tax->formatted_amount
						);
					}
				} else {
					$total_rows['tax'] = array(
						'label' => WC()->countries->tax_or_vat() . ':',
						'value' => YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_total_tax(), $convert_currency, $currency_code ),
					);
				}
			}
			// Refund
			if ( method_exists( $order, 'get_refunds' ) ) {
				$refunds = $order->get_refunds();
				if ( $refunds ) {
					foreach ( $refunds as $id => $refund ) {
						$total_rows[ 'refund_' . $id ] = array(
							'label' => $refund->get_reason() ? $refund->get_reason() : __( 'Refund', 'woocommerce' ) . ':',
							'value' => YayCurrencyHelper::get_formatted_total_by_convert_currency( '-' . $refund->get_amount(), $convert_currency, $currency_code ),
						);
					}
				}
			}
		}
		return $total_rows;
	}

	// Order Details in My Account

	public function woocommerce_order_formatted_line_subtotal( $subtotal, $item, $order ) {
		if ( ! apply_filters( 'yay_currency_is_change_format_order_line_subtotal', true, $subtotal, $item, $order ) ) {
			return $subtotal;
		}
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			$tax_display      = get_option( 'woocommerce_tax_display_cart' );
			if ( 'excl' === $tax_display ) {
				$ex_tax_label = $order->get_prices_include_tax() ? 1 : 0;
				$subtotal     = YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_line_subtotal( $item ), $convert_currency, $currency_code, $ex_tax_label );
			} else {
				$subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_line_subtotal( $item, true ), $convert_currency, $currency_code );
			}
		}
		return $subtotal;
	}

	protected function get_values_for_total( $field, $order ) {
		$items = array_map(
			function ( $item ) use ( $field ) {
				return wc_add_number_precision( $item[ $field ], false );
			},
			array_values( $order->get_items() )
		);
		return $items;
	}

	protected function round_line_tax( $value, $in_cents = true ) {
		$round_at_subtotal = 'yes' === get_option( 'woocommerce_tax_round_at_subtotal' ) ? true : false;
		if ( ! $round_at_subtotal ) {
			$precision = $in_cents ? 0 : null;
			$value     = wc_round_tax_total( $value, $precision );
		}
		return $value;
	}

	public function woocommerce_order_subtotal_to_display( $subtotal, $compound, $order ) {
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			$tax_display      = get_option( 'woocommerce_tax_display_cart' );
			$subtotal         = wc_remove_number_precision(
				$order->get_rounded_items_total( self::get_values_for_total( 'subtotal', $order ) )
			);
			if ( ! $compound ) {
				if ( 'incl' === $tax_display ) {
					$subtotal_taxes = 0;
					foreach ( $order->get_items() as $item ) {
						$subtotal_taxes += self::round_line_tax( $item->get_subtotal_tax(), false );
					}
					$subtotal += wc_round_tax_total( $subtotal_taxes );
				}
				$subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $subtotal, $convert_currency, $currency_code );
				if ( 'excl' === $tax_display && $order->get_prices_include_tax() && wc_tax_enabled() ) {
					$subtotal .= ' <small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>';
				}
			} else {
				if ( 'incl' === $tax_display ) {
					return '';
				}
				// Add Shipping Costs.
				$subtotal += $order->get_shipping_total();
				// Remove non-compound taxes.
				foreach ( $order->get_taxes() as $tax ) {
					if ( $tax->is_compound() ) {
						continue;
					}
					$subtotal = $subtotal + $tax->get_tax_total() + $tax->get_shipping_tax_total();
				}
				// Remove discounts.
				$subtotal = $subtotal - $order->get_total_discount();
				$subtotal = YayCurrencyHelper::get_formatted_total_by_convert_currency( $subtotal, $convert_currency, $currency_code );
			}
		}
		return $subtotal;
	}

	public function woocommerce_order_shipping_to_display( $shipping, $order, $tax_display ) {
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			$tax_display      = $tax_display ? $tax_display : get_option( 'woocommerce_tax_display_cart' );

			if ( 0 < abs( (float) $order->get_shipping_total() ) ) {
				if ( 'excl' === $tax_display ) {
					// Show shipping excluding tax.
					$shipping = YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_shipping_total(), $convert_currency, $currency_code );
					if ( (float) $order->get_shipping_tax() > 0 && $order->get_prices_include_tax() ) {
						$shipping .= apply_filters( 'woocommerce_order_shipping_to_display_tax_label', '&nbsp;<small class="tax_label">' . WC()->countries->ex_tax_or_vat() . '</small>', $order, $tax_display );
					}
				} else {
					// Show shipping including tax.
					$shipping = YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_shipping_total() + $order->get_shipping_tax(), $convert_currency, $currency_code );
					if ( (float) $order->get_shipping_tax() > 0 && ! $order->get_prices_include_tax() ) {
						$shipping .= apply_filters( 'woocommerce_order_shipping_to_display_tax_label', '&nbsp;<small class="tax_label">' . WC()->countries->inc_tax_or_vat() . '</small>', $order, $tax_display );
					}
				}
				/* translators: %s: method */
				$shipping .= apply_filters( 'woocommerce_order_shipping_to_display_shipped_via', '&nbsp;<small class="shipped_via">' . sprintf( __( 'via %s', 'woocommerce' ), $order->get_shipping_method() ) . '</small>', $order );
			} elseif ( $order->get_shipping_method() ) {
				return $shipping;
			} else {
				$shipping = __( 'Free!', 'woocommerce' );
			}
		}
		return $shipping;
	}

	public function woocommerce_order_discount_to_display( $discount, $order ) {
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$tax_display      = get_option( 'woocommerce_tax_display_cart' );
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			$price_format     = $order->get_total_discount( 'excl' === $tax_display );
			$discount         = YayCurrencyHelper::get_formatted_total_by_convert_currency( $price_format, $convert_currency, $currency_code );
		}
		return $discount;
	}

	public function woocommerce_get_formatted_order_total( $formatted_total, $order, $tax_display, $display_refunded ) {
		if ( apply_filters( 'yay_currency_skip_order_total_formatting', false ) ) {
			return $formatted_total;
		}
		$total_refunded = $order->get_total_refunded();
		if ( $total_refunded && $display_refunded ) {
			return $formatted_total;
		}
		$currency_code = YayCurrencyHelper::get_currency_code_by_order( $order );
		if ( ! empty( $currency_code ) ) {
			$convert_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $this->converted_currency );
			if ( ! $convert_currency ) {
				return $formatted_total;
			}

			$total = YayCurrencyHelper::get_total_by_order( $order );

			$formatted_total = YayCurrencyHelper::get_formatted_total_by_convert_currency( $total, $convert_currency, $currency_code );

			if ( wc_tax_enabled() && 'incl' === $tax_display ) {
				$formatted_tax = sprintf( '%s %s', YayCurrencyHelper::get_formatted_total_by_convert_currency( $order->get_total_tax(), $convert_currency, $currency_code ), WC()->countries->tax_or_vat() );
				/* translators: %s: taxes */
				$formatted_tax_string = ' <small class="includes_tax">' . sprintf( __( '(includes %s)', 'woocommerce' ), $formatted_tax ) . '</small>';
				$formatted_total      = $formatted_total . $formatted_tax_string;
			}
		}

		return $formatted_total;
	}

	public function woocommerce_currency( $currency ) {
		$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );

		if ( ! $apply_currency || ! isset( $this->fallback_currency ) || ! isset( $apply_currency['currency'] ) ) {
			return $currency;
		}

		$default_currency_symbol = SupportHelper::detect_keep_old_currency_symbol( false, $this->is_dis_checkout_diff_currency, $apply_currency );
		if ( $default_currency_symbol ) {
			return $currency;
		}

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {
			$currency = apply_filters( 'yay_currency_woocommerce_currency', $this->fallback_currency['currency'], $this->is_dis_checkout_diff_currency );
			return $currency;
		}

		$currency = apply_filters( 'yay_currency_woocommerce_currency', $apply_currency['currency'], $this->is_dis_checkout_diff_currency );

		return $currency;
	}

	public function woocommerce_currency_symbol( $currency_symbol, $currency ) {
		$apply_currency          = YayCurrencyHelper::get_current_currency( $this->apply_currency );
		$default_currency_symbol = SupportHelper::detect_keep_old_currency_symbol( false, $this->is_dis_checkout_diff_currency, $apply_currency );

		if ( ! $apply_currency || $default_currency_symbol || ! isset( $this->fallback_currency['symbol'] ) || ! isset( $apply_currency['symbol'] ) || ( function_exists( 'is_account_page' ) && is_account_page() ) ) {
			return $currency_symbol;
		}

		if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {
			$currency_symbol = apply_filters( 'yay_currency_woocommerce_currency_symbol', $currency_symbol, $currency, $apply_currency );
			return $currency_symbol;
		}

		$currency_symbol = wp_kses_post( html_entity_decode( $apply_currency['symbol'] ) );
		$currency_symbol = apply_filters( 'yay_currency_woocommerce_currency_symbol', $currency_symbol, $currency, $apply_currency );

		return $currency_symbol;

	}

	protected function get_current_currency_options() {
		$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );

		if ( ! $apply_currency || YayCurrencyHelper::disable_fallback_option_in_checkout_page( $apply_currency ) ) {
			$apply_currency = YayCurrencyHelper::get_fallback_currency();
		}

		return Helper::set_default_currency_options( $apply_currency );

	}

	public function wc_get_price_thousand_separator( $thousand_separator ) {
		$currency_options = self::get_current_currency_options( $this->apply_currency );
		if ( ! isset( $currency_options['thousandSeparator'] ) ) {
			return $thousand_separator;
		}
		return apply_filters( 'yay_currency_custom_thousand_separator', $currency_options['thousandSeparator'], $this->apply_currency );
	}

	public function wc_get_price_decimal_separator( $price_decimal_sep ) {
		$currency_options = self::get_current_currency_options( $this->apply_currency );
		if ( ! isset( $currency_options['decimalSeparator'] ) ) {
			return $price_decimal_sep;
		}
		return apply_filters( 'yay_currency_custom_decimal_separator', $currency_options['decimalSeparator'], $this->apply_currency );
	}

	public function wc_get_price_decimals( $num_decimals ) {
		$currency_options = self::get_current_currency_options( $this->apply_currency );
		if ( ! isset( $currency_options['numberDecimal'] ) ) {
			return $num_decimals;
		}
		return apply_filters( 'yay_currency_custom_number_decimal', $currency_options['numberDecimal'], $this->apply_currency );
	}

	public function woocommerce_price_format( $format, $currency_position ) {
		$apply_currency = YayCurrencyHelper::get_current_currency( $this->apply_currency );
		return Helper::change_price_format( $apply_currency, $format );
	}

	private function should_skip_fallback() {
		return YayCurrencyHelper::disable_fallback_option_in_checkout_page( $this->apply_currency );
	}
	// Approximate price
	private function get_approximately_price_data() {
		$price_data = SupportHelper::get_approximately_price_data( $this->country_info, $this->apply_currency, $this->converted_currency );
		if ( ! $price_data || self::should_skip_fallback() ) {
			return false;
		}
		return $price_data;
	}

	private function get_number_decimal() {
		return isset( $this->fallback_currency['currency'] ) && $this->fallback_currency['currency'] === $this->default_currency
			? intval( $this->fallback_currency['numberDecimal'] )
			: get_option( 'woocommerce_price_num_decimals', 2 );
	}

	public function custom_approximately_price_html( $price_html, $product ) {

		if ( is_admin() && ! wp_doing_ajax() ) {
			return $price_html;
		}

		if ( ! isset( $this->apply_currency['currency'] ) || '' === $product->get_price() ) {
			return $price_html;
		}

		if ( isset( $_REQUEST['yay_currency_price_html_use_shortcode'] ) && ! empty( $_REQUEST['yay_currency_price_html_use_shortcode'] ) ) {
			return $price_html;
		}

		$formatted_price = SupportHelper::get_formatted_product_price( $product, $this->country_info, $this->apply_currency, $this->converted_currency );
		if ( ! $formatted_price ) {
			return $price_html;
		}

		$price_html = SupportHelper::get_formatted_approximately_price( $price_html, $formatted_price );

		return $price_html;
	}

	public function custom_approximately_price_cart_product_price_html( $product_price_html, $product ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $product_price_html;
		}

		// Determine the price based on tax settings
		$price_args              = array(
			'qty'   => 1,
			'price' => $product->get_price( 'edit' ),
		);
		$cart_product_item_price = wc()->cart->display_prices_including_tax()
			? wc_get_price_including_tax( $product, $price_args )
			: wc_get_price_excluding_tax( $product, $price_args );

		// Update price data and format output
		$approximately_price_data['default_price'] = $cart_product_item_price;
		$formatted_cart_item_price                 = SupportHelper::get_formatted_price_by_specific_product( $product, $approximately_price_data );

		return SupportHelper::get_formatted_approximately_price( $product_price_html, $formatted_cart_item_price );
	}

	public function custom_approximately_price_cart_product_subtotal_html( $product_subtotal_html, $product, $quantity, $cart ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $product_subtotal_html;
		}

		$price_args = array(
			'qty'   => $quantity,
			'price' => $product->get_price( 'edit' ),
		);

		// Determine the subtotal price based on tax settings
		if ( $product->is_taxable() ) {
			$product_subtotal_price = $cart->display_prices_including_tax()
				? wc_get_price_including_tax( $product, $price_args )
				: wc_get_price_excluding_tax( $product, $price_args );
		} else {
			$product_subtotal_price = $price_args['price'] * $quantity;
		}

		// Update price data and format output
		$approximately_price_data['default_price'] = $product_subtotal_price;
		$approximately_price_data['quantity']      = $quantity;
		$formatted_product_subtotal_price          = SupportHelper::get_formatted_price_by_specific_product( $product, $approximately_price_data );

		return SupportHelper::get_formatted_approximately_price( $product_subtotal_html, $formatted_product_subtotal_price );
	}

	public function custom_approximately_price_cart_subtotal_html( $cart_subtotal_html, $compound, $cart ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $cart_subtotal_html;
		}

		$approximately_apply_currency = $approximately_price_data['approximately_apply_currency'];
		if ( $approximately_apply_currency && YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $approximately_apply_currency ) ) {
			$cart_subtotal = apply_filters( 'yay_currency_get_cart_subtotal', 0, $approximately_apply_currency );
		} else {
			if ( $compound ) {
				$subtotal_price = $cart->get_cart_contents_total() + $cart->get_shipping_total() + $cart->get_taxes_total( false, false );
			} elseif ( $cart->display_prices_including_tax() ) {
				$subtotal_price = $cart->get_subtotal() + $cart->get_subtotal_tax();
			} else {
				$subtotal_price = $cart->get_subtotal();
			}
			$cart_subtotal = YayCurrencyHelper::reverse_calculate_price_by_currency( $subtotal_price, $this->apply_currency );
			$cart_subtotal = round( $cart_subtotal, self::get_number_decimal(), PHP_ROUND_HALF_UP );
			$cart_subtotal = $cart_subtotal * $approximately_price_data['params']['rate'];
		}
		$formatted_cart_subtotal = SupportHelper::get_formatted_price( $cart_subtotal, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $cart_subtotal_html, $formatted_cart_subtotal );

	}

	public function custom_approximately_price_coupon_html( $coupon_html, $coupon, $discount_amount_html ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $coupon_html;
		}

		$coupon_total           = YayCurrencyHelper::reverse_calculate_price_by_currency( (float) $coupon->get_amount(), $this->apply_currency );
		$coupon_total           = round( $coupon_total, self::get_number_decimal(), PHP_ROUND_HALF_UP );
		$coupon_total           = $coupon_total * $approximately_price_data['params']['rate'];
		$formatted_coupon_total = SupportHelper::get_formatted_price( $coupon_total, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $coupon_html, $formatted_coupon_total );

	}

	public function custom_approximately_price_shipping_label( $label, $method ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data || 'free_shipping' === $method->method_id ) {
			return $label;
		}

		$shipping_total           = YayCurrencyHelper::reverse_calculate_price_by_currency( (float) $method->cost, $this->apply_currency );
		$shipping_total           = round( $shipping_total, self::get_number_decimal(), PHP_ROUND_HALF_UP );
		$shipping_total           = $shipping_total * $approximately_price_data['params']['rate'];
		$formatted_shipping_total = SupportHelper::get_formatted_price( $shipping_total, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $label, $formatted_shipping_total, true );

	}

	public function custom_approximately_price_cart_totals_fee_html( $cart_totals_fee_html, $fee ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $cart_totals_fee_html;
		}

		$fee_total           = YayCurrencyHelper::reverse_calculate_price_by_currency( floatval( $fee->amount ), $this->apply_currency );
		$fee_total           = round( $fee_total, self::get_number_decimal(), PHP_ROUND_HALF_UP );
		$fee_total           = $fee_total * $approximately_price_data['params']['rate'];
		$formatted_fee_total = SupportHelper::get_formatted_price( $fee_total, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $cart_totals_fee_html, $formatted_fee_total );

	}

	public function custom_approximately_price_cart_totals_taxes_total_html( $taxes_total_html ) {

		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $taxes_total_html;
		}

		$approximately_apply_currency = $approximately_price_data['approximately_apply_currency'];
		if ( $approximately_apply_currency && YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $approximately_apply_currency ) ) {
			$taxes_total = apply_filters( 'yay_currency_get_total_tax', 0, $approximately_apply_currency );
		} else {
			$taxes_total = YayCurrencyHelper::reverse_calculate_price_by_currency( WC()->cart->get_taxes_total(), $this->apply_currency );
			$taxes_total = round( $taxes_total, self::get_number_decimal(), PHP_ROUND_HALF_UP );
			$taxes_total = $taxes_total * $approximately_price_data['params']['rate'];
		}

		$formatted_taxes_total = SupportHelper::get_formatted_price( $taxes_total, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $taxes_total_html, $formatted_taxes_total, false );

	}

	public function custom_approximately_price_cart_total_html( $cart_total_html ) {
		$approximately_price_data = self::get_approximately_price_data();

		if ( ! $approximately_price_data ) {
			return $cart_total_html;
		}

		$approximately_apply_currency = $approximately_price_data['approximately_apply_currency'];
		if ( $approximately_apply_currency && YayCurrencyHelper::enable_rounding_or_fixed_price_currency( $approximately_apply_currency ) ) {
			$cart_total = apply_filters( 'yay_currency_get_cart_total', 0, $approximately_apply_currency, false );
		} else {
			$cart_total = YayCurrencyHelper::reverse_calculate_price_by_currency( (float) WC()->cart->total, $this->apply_currency );
			$cart_total = round( $cart_total, self::get_number_decimal(), PHP_ROUND_HALF_UP );
			$cart_total = $cart_total * $approximately_price_data['params']['rate'];
		}

		$formatted_shipping_total = SupportHelper::get_formatted_price( $cart_total, $approximately_price_data['params'] );
		return SupportHelper::get_formatted_approximately_price( $cart_total_html, $formatted_shipping_total, true );

	}
}
