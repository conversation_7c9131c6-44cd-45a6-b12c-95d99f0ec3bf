<?php

namespace Yay_Currency\License;

defined( 'ABSPATH' ) || exit;

class CorePlugin {

	public static function get( $name ) {
		$data = array(
			'path'        => YAY_CURRENCY_PLUGIN_DIR,
			'url'         => YAY_CURRENCY_PLUGIN_URL,
			'basename'    => YAY_CURRENCY_BASE_NAME,
			'version'     => YAY_CURRENCY_VERSION,
			'slug'        => 'yay_currency',
			'link'        => 'https://yaycommerce.com/yaycurrency-woocommerce-multi-currency-switcher/',
			'download_id' => '7761',
		);

		if ( isset( $data[ $name ] ) ) {
			return $data[ $name ];
		}
		return null;
	}
}
