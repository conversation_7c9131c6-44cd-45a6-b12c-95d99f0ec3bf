!function (e, r) { YayCurrency_Callback = r.Yay<PERSON>urrency_Callback || {}, yay_currency_data_args = { common_data_args: { yayCurrencySymbolWrapper: "span.woocommerce-Price-currencySymbol", yayCurrencySwitcher: ".yay-currency-single-page-switcher", yayCountryCurrency: ".yay-currency-country-currency-notice-wrapper", yayCurrencyLoading: "yay-currency-cache-loading", yayCurrencyHide: "yay-currency-force-payment-hide", yayCurrencyWidget: ".yay-currency-widget-switcher", yayCurrencyMenu: ".yay-currency-menu-shortcode-switcher", yayCurrencyShortcode: ".yay-currency-shortcode-switcher", yayCurrencyBlock: ".yay-currency-block-switcher", yayCurrencyShortcodePriceHtml: ".yay-currency-shortcode-price-html-wrapper", yayCurrencyShortcodeProductPriceHtml: ".yay-currency-shortcode-product-price-html-wrapper" }, converter_args: { converterWrapper: ".yay-currency-converter-container", converterAmount: ".yay-currency-converter-amount", converterFrom: ".yay-currency-converter-from-currency", converterTo: ".yay-currency-converter-to-currency", converterResultWrapper: ".yay-currency-converter-result-wrapper", converterResultAmount: ".yay-currency-converter-amount-value", converterResultFrom: ".yay-currency-converter-from-currency-code", converterResultValue: ".yay-currency-converter-result-value", converterResultTo: ".yay-currency-converter-to-currency-code" }, caching_data_args: { yayProductId: ".yay-currency-cache-product-id", yayVariationId: ".variations_form", yayDataProductId: "yay_currency-product-id", yayCurrencyMiniCartContents: yay_callback_data.minicart_contents_class, yayCurrencyProduct: ".yay-currency-single-product-switcher" }, switcher_data_args: { activeClass: "active", upwardsClass: "upwards", openClass: "open", selectedClass: "selected", currencySwitcher: ".yay-currency-switcher", currencyFlag: ".yay-currency-flag", currencySelectedFlag: ".yay-currency-flag.selected", customLoader: ".yay-currency-custom-loader", customOption: ".yay-currency-custom-options", customArrow: ".yay-currency-custom-arrow", customOptionArrow: ".yay-currency-custom-option-row", customOptionArrowSelected: ".yay-currency-custom-option-row.selected", selectTrigger: ".yay-currency-custom-select__trigger", selectWrapper: ".yay-currency-custom-select-wrapper", customSelect: ".yay-currency-custom-select", selectedOption: ".yay-currency-custom-select__trigger .yay-currency-selected-option" }, blocks_data_args: { checkout: ".wp-block-woocommerce-checkout[data-block-name='woocommerce/checkout']", totalCheckout: ".wp-block-woocommerce-checkout .wp-block-woocommerce-checkout-order-summary-block .wc-block-components-totals-footer-item", cart: ".wp-block-woocommerce-cart[data-block-name='woocommerce/cart']", totalCart: ".wp-block-woocommerce-cart .wp-block-woocommerce-cart-order-summary-block .wc-block-components-totals-footer-item", payments: { container: ".wc-block-checkout__payment-method#payment-method", options: 'input.wc-block-components-radio-control__input[name="radio-control-wc-payment-method-options"]', localStorageName: "YayCurrencySelectedPaymentMethod", class: { parent: ".wc-block-components-radio-control-accordion-option", labelOption: "label.wc-block-components-radio-control__option" } }, filterPrice: { class: { wrapper: ".wp-block-woocommerce-price-filter", controls: ".wc-block-price-filter__controls", filterSlideInput: '.wp-block-woocommerce-filter-wrapper[data-filter-type="price-filter"] .wc-block-price-slider input', minPriceWrapper: ".wc-block-price-filter__range-input--min", maxPriceWrapper: ".wc-block-price-filter__range-input--max", minPriceInput: "input.wc-block-price-filter__amount--min", maxPriceInput: "input.wc-block-price-filter__amount--max", resetButton: ".wc-block-components-filter-reset-button", progressRange: ".wc-block-price-filter__range-input-progress" } } }, cookies_data_args: { cartBlocks: "yay_cart_blocks_page", checkoutBlocks: "yay_checkout_blocks_page", blocksForceNoticeHtml: "yay_blocks_force_notice", forcePaymentHtml: "yay_fore_payment_notice_html" } }, YayCurrency_Callback.yay_force_payment_data_args = Object.assign({}, yay_currency_data_args.common_data_args, { yayCurrencyForcePaymentNotice: ".yay-currency-checkout-force-payment-notice" }), YayCurrency_Callback.yay_caching_data_args = Object.assign({}, yay_currency_data_args.common_data_args, yay_currency_data_args.caching_data_args); var a = { actions: {}, filters: {} }; YayCurrency_Callback.Helper = { addHook: function (e, r, c) { a[e][r] || (a[e][r] = []), a[e][r].push(c) }, applyFilters: function (e, r) { return a.filters[e] && a.filters[e].forEach((function (e) { r = e(r) })), r }, doAction: function (e, r) { a.actions[e] && a.actions[e].forEach((function (e) { e.apply(null, r) })) }, setCookie: function (e, r, a) { var c = ""; if (a) { var t = new Date; t.setTime(t.getTime() + 24 * a * 60 * 60 * 1e3), c = "; expires=" + t.toUTCString() } document.cookie = e + "=" + (r || "") + c + "; path=/" }, getCookie: function (e) { let r = e + "=", a = decodeURIComponent(document.cookie).split(";"); for (let e = 0; e < a.length; e++) { let c = a[e]; for (; " " == c.charAt(0);)c = c.substring(1); if (0 == c.indexOf(r)) return c.substring(r.length, c.length) } return "" }, deleteCookie: function (e) { YayCurrency_Callback.Helper.setCookie(e, "", -1) }, getCurrentCurrency: function (e = !1) { e = e || YayCurrency_Callback.Helper.getCookie(r.yayCurrency.cookie_name); let a = !1; return r.yayCurrency.converted_currency && r.yayCurrency.converted_currency.forEach((r => { r.ID === +e && (a = r) })), yayCurrencyHooks.applyFilters("yayCurrencyGetCurrentCurrency", a) }, getCurrencyIDbyCurrencyName: function (e) { let a = !1; return r.yayCurrency.converted_currency && r.yayCurrency.converted_currency.forEach((r => { r.currency === e && (a = r) })), !!a && a.ID }, getRateFeeByCurrency: function (e = !1) { e = e || YayCurrency_Callback.Helper.getCurrentCurrency(); let r = parseFloat(e.rate); return r = "percentage" === e.fee.type ? parseFloat(e.rate) + parseFloat(e.rate) * (parseFloat(e.fee.value) / 100) : parseFloat(e.rate) + parseFloat(e.fee.value), yayCurrencyHooks.applyFilters("yayCurrencyGetRateFeeByCurrency", r) }, getBlockData: function () { let r = []; const a = e(yay_currency_data_args.common_data_args.yayCurrencyBlock); return a.length && a.each((function () { var a = { isBlockID: e(this).data("block-id"), isShowFlag: e(this).data("show-flag"), isShowCurrencyName: e(this).data("show-currency-name"), isShowCurrencySymbol: e(this).data("show-currency-symbol"), isShowCurrencyCode: e(this).data("show-currency-code"), widgetSize: e(this).data("switcher-size") }; r.push(a) })), yayCurrencyHooks.applyFilters("yayCurrencyGetBlockData", r) }, customResponseCommon: function (a) { let c = !1; a.data.widget_content && (c = !0, e(yay_currency_data_args.common_data_args.yayCurrencyWidget).html(a.data.widget_content)), a.data.menu_content && (c = !0, e(yay_currency_data_args.common_data_args.yayCurrencyMenu).html(a.data.menu_content)), a.data.shortcode_content && (c = !0, e(yay_currency_data_args.common_data_args.yayCurrencyShortcode).html(a.data.shortcode_content)), a.data.block_content && (c = !0, e(yay_currency_data_args.common_data_args.yayCurrencyBlock).each((function () { const r = e(this).data("block-id"); r && e(this).html(a.data.block_content[r]) }))), a.data.country_notice && e(yay_currency_data_args.common_data_args.yayCountryCurrency).html(a.data.country_notice), a.data.shortcode_price_html && e(yay_currency_data_args.common_data_args.yayCurrencyShortcodePriceHtml).each((function (r, c) { a.data.shortcode_price_html[r] && e(c).html(a.data.shortcode_price_html[r]) })), a.data.shortcode_product_price_html && e(yay_currency_data_args.common_data_args.yayCurrencyShortcodeProductPriceHtml).each((function (r, c) { a.data.shortcode_product_price_html[r] && e(c).html(a.data.shortcode_product_price_html[r]) })), c && e(r).on("load resize scroll", YayCurrency_Callback.Helper.switcherUpwards()), yayCurrencyHooks.doAction("yayCurrencyResponseCommon", [{ response: a }]) }, blockLoading: function (r) { e(r).addClass("processing").block({ message: null, overlayCSS: { background: "#fff", opacity: .6 } }), yayCurrencyHooks.doAction("yayCurrencyCustomBlockLoading", [{ data: r }]) }, unBlockLoading: function (r) { e(r).removeClass("processing").unblock(), yayCurrencyHooks.doAction("yayCurrencyCustomUnBlockLoading", [{ data: r }]) }, switcherUpwards: function () { const a = e(yay_currency_data_args.common_data_args.yayCurrencySwitcher); a.each((function () { const a = e(this).offset().top + e(this).height() - e(r).scrollTop(); e(r).height() - e(this).height() - e(this).offset().top + e(r).scrollTop() < 250 && a > 250 ? (e(this).find(yay_currency_data_args.switcher_data_args.customOption).addClass(yay_currency_data_args.switcher_data_args.upwardsClass), e(this).find(yay_currency_data_args.switcher_data_args.customArrow).addClass(yay_currency_data_args.switcher_data_args.upwardsClass), e(this).find(yay_currency_data_args.switcher_data_args.selectTrigger).addClass(yay_currency_data_args.switcher_data_args.upwardsClass)) : (e(this).find(yay_currency_data_args.switcher_data_args.customOption).removeClass(yay_currency_data_args.switcher_data_args.upwardsClass), e(this).find(yay_currency_data_args.switcher_data_args.customArrow).removeClass(yay_currency_data_args.switcher_data_args.upwardsClass), e(this).find(yay_currency_data_args.switcher_data_args.selectTrigger).removeClass(yay_currency_data_args.switcher_data_args.upwardsClass)) })), yayCurrencyHooks.doAction("yayCurrencyCustomSwitcherDropdown", [{ data: a }]) }, switcherAction: function () { const a = yay_currency_data_args.switcher_data_args; e(document).on("click", a.selectWrapper, (function () { e(a.customSelect, this).toggleClass(a.openClass), e("#slide-out-widget-area").find(a.customOption).toggleClass("overflow-fix"), e("[id^=footer]").toggleClass("z-index-fix"), e(a.customSelect, this).parents(".handheld-navigation").toggleClass("overflow-fix") })), e(document).on("click", a.customOptionArrow, (function () { let r = e(this).data("value") ? e(this).data("value") : e(this).data("currency-id"); if (!r) { const a = e(this).attr("class").match(/yay-currency-id-(\d+)/); a && (r = a[1], YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name ?? "yay_currency_widget", r, 1), location.reload()) } const c = e(this).children(a.currencyFlag).data("country_code"); if (YayCurrency_Callback.Helper.refreshCartFragments(), e(a.currencySwitcher).val(r).change(), YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name ?? "yay_currency_do_change_switcher", r, 1), !e(this).hasClass(a.selectedClass)) { const r = e(this).closest(a.customSelect); e(this).parent().find(a.customOptionArrowSelected).removeClass(a.selectedClass), e(this).addClass(a.selectedClass), r.find(a.currencySelectedFlag).css({ background: `url(${yayCurrency.yayCurrencyPluginURL}assets/dist/flags/${c}.svg)` }), r.find(a.selectedOption).text(e(this).text()), r.find(a.customLoader).addClass(a.activeClass), r.find(a.customArrow).hide() } })), r.addEventListener("click", (function (e) { document.querySelectorAll(yay_currency_data_args.switcher_data_args.customSelect).forEach((r => { r.contains(e.target) || r.classList.remove(yay_currency_data_args.switcher_data_args.openClass) })) })), yayCurrencyHooks.doAction("yayCurrencyCustomSwitcherAction", [{ data: a }]) }, refreshCartFragments: function () { "undefined" != typeof wc_cart_fragments_params && null !== wc_cart_fragments_params && sessionStorage.removeItem(wc_cart_fragments_params.fragment_name), yayCurrencyHooks.doAction("yayCurrencyCustomRefreshCartFragments", []) }, getListDataForcePayment: function () { const r = YayCurrency_Callback.yay_force_payment_data_args; let a = { action: "yayCurrency_handle_force_payment_response", blocks: YayCurrency_Callback.Helper.getBlockData(), widget: e(r.yayCurrencyWidget).length, menu: e(r.yayCurrencyMenu).length, shortcode: e(r.yayCurrencyShortcode).length, country_notice: e(r.yayCountryCurrency).length, country_code: yayCurrency.country_code, foce_payment_notice: e(r.yayCurrencyForcePaymentNotice).length, is_cart_page: yayCurrency.cart_page, nonce: yayCurrency.nonce }; if (e(r.yayCurrencyShortcodePriceHtml).length) { let c = []; e(r.yayCurrencyShortcodePriceHtml).each((function (r, a) { c[r] = e(a).data("default-price") })), a.shortcode_default_price = c } if (e(r.yayCurrencyShortcodeProductPriceHtml).length) { let c = []; e(r.yayCurrencyShortcodeProductPriceHtml).each((function (r, a) { c[r] = e(a).data("shortcode-product-id") })), a.shortcode_product_ids = c } return yayCurrencyHooks.applyFilters("yayCurrencyGetDataForcePayment", a) }, customResponseForcePayment: function (r) { if (r.success) { if (YayCurrency_Callback.Helper.customResponseCommon(r), r.data.force_payment_is_dis_checkout_diff_currency ? e(".yay-currency-checkout-notice-force-payment-wrapper").length && e(".yay-currency-checkout-notice-force-payment-wrapper").html(r.data.force_payment_checkout_notice_html) : e(".yay-currency-checkout-notice-force-payment-wrapper").length && e(".yay-currency-checkout-notice-force-payment-wrapper").html(""), "undefined" != r.data.force_payment_notice_html) { const a = YayCurrency_Callback.yay_force_payment_data_args; if (e(a.yayCurrencyForcePaymentNotice).length) { const c = yay_currency_data_args.cookies_data_args.forcePaymentHtml; if ("" == r.data.force_payment_notice_html || r.data.force_payment_checkout_notice_html) { YayCurrency_Callback.Helper.getCookie(c) ? YayCurrency_Callback.Helper.deleteCookie(c) : e(a.yayCurrencyForcePaymentNotice).remove() } else { let t = r.data.force_payment_notice_html; e(a.yayCurrencyForcePaymentNotice).removeClass(a.yayCurrencyHide), e(a.yayCurrencyForcePaymentNotice).html(t), r.data.reload_page && YayCurrency_Callback.Helper.setCookie(c, t, +yayCurrency.cookie_lifetime_days) } } } YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_switcher_name) && YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name)), yayCurrencyHooks.doAction("yayCurrencyResponseForcePayment", [{ response: r }]), r.data.reload_page && location.reload() } }, customResponseForceCurrencyByPaymentMethod: function (r) { r.success && (YayCurrency_Callback.Helper.customResponseCommon(r), r.data.force_payment_is_dis_checkout_diff_currency ? e(".yay-currency-checkout-notice-force-payment-wrapper").length && e(".yay-currency-checkout-notice-force-payment-wrapper").html(r.data.force_payment_checkout_notice_html) : e(".yay-currency-checkout-notice-force-payment-wrapper").length && e(".yay-currency-checkout-notice-force-payment-wrapper").html(""), YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_switcher_name) && YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name)), yayCurrencyHooks.doAction("yayCurrencyResponseForceCurrencyByPaymentMethod", [{ response: r }]), r.data.reload_page && location.reload()) }, forcePaymentCountryCurrency: function (r, a = !1) { e.ajax({ url: yayCurrency.ajaxurl, type: "POST", data: r, beforeSend: function (e) { YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher), YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents) }, xhrFields: { withCredentials: !0 }, success: function (r) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher), YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), YayCurrency_Callback.Helper.customResponseForcePayment(r), a && (r.data.force_payment_blocks_checkout_notice_html && "" != r.data.force_payment_blocks_checkout_notice_html && (e(yay_currency_data_args.blocks_data_args.checkout).before(r.data.force_payment_blocks_checkout_notice_html), e(".yay-currency-checkout-notice-current-currency").text(r.data.force_payment_blocks_current_currency)), e(document.body).trigger("wc_fragment_refresh"), r.data.force_payment_blocks_cart_subtotal_fallback && YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name))) }, error: function (e, r, a) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher), YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), console.log("Error responseText: ", e.responseText) } }) }, forceCurrencyByPaymentMethodSelected: function (r, a = !1) { e.ajax({ url: yayCurrency.ajaxurl, type: "POST", data: r, beforeSend: function (e) { YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout) }, xhrFields: { withCredentials: !0 }, success: function (e) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout), YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), YayCurrency_Callback.Helper.customResponseForceCurrencyByPaymentMethod(e) }, error: function (e, r, a) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.common_data_args.yayCurrencySwitcher), YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), console.log("Error responseText: ", e.responseText) } }) }, forcePaymentOnCheckoutPage: function (r) { "undefined" != typeof wc_checkout_params && (yayCurrency.force_payment && e(document.body).on("updated_checkout", (function (a) { e(document.body).trigger("wc_fragment_refresh"), YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(r) })), yayCurrency.force_currency ? (e(document.body).on("payment_method_selected", (function (r, a) { e(document.body).trigger("update_checkout") })), e(document.body).on("updated_checkout", (function (a) { e(document.body).trigger("wc_fragment_refresh"), YayCurrency_Callback.Helper.reRenderHTMLAfterForceCurrencyByPaymentMethod(r) }))) : localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName) && localStorage.removeItem(yay_currency_data_args.blocks_data_args.payments.localStorageName)) }, forcePaymentOnCartPage: function (r) { yayCurrency.force_payment && "1" === yayCurrency.cart_page && e(document.body).on("updated_cart_totals", (function (e) { YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name) !== r && YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(r) })) }, reRenderHTMLAfterForceCountryCode: function (e, r = !1) { let a = YayCurrency_Callback.Helper.getListDataForcePayment(); a.yay_currency_old = e, r ? a.woocommerce_blocks = "yes" : a.yay_currency_new = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name), YayCurrency_Callback.Helper.forcePaymentCountryCurrency(a, r) }, reRenderHTMLAfterForceCurrencyByPaymentMethod: function (r, a = !1) { var c = e('input[name="payment_method"]:checked').val(); let t = YayCurrency_Callback.Helper.getListDataForcePayment(); t.action = "yayCurrency_handle_force_currency_by_payment_selected_response", t.yay_currency_old = r, t.yay_currency_new = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name), t.payment_method_id = c, YayCurrency_Callback.Helper.forceCurrencyByPaymentMethodSelected(t, a) }, detectAllowCaching: function (e) { return yayCurrencyHooks.applyFilters("yayCurrencyDetectAllowCaching", e) }, getListProductIdsAvailable: function () { let r = [], a = e(yay_currency_data_args.caching_data_args.yayProductId); a.length && a.each((function (a, c) { const t = e(c).data(yay_currency_data_args.caching_data_args.yayDataProductId); t && r.push(t) })); let c = e(yay_currency_data_args.caching_data_args.yayVariationId); return c.length && c.each((function (a, c) { let t = e(c).data("product_variations"); t.length && t.forEach((e => { r.push(e.variation_id) })) })), yayCurrencyHooks.applyFilters("yayCurrencyGetListProductIds", r) }, getListDataCaching: function (a = !1, c = []) { const t = YayCurrency_Callback.yay_caching_data_args; let n = { action: a ? "yay_caching_get_price_html" : "yay_caching_generate_currency_switcher_html", blocks: YayCurrency_Callback.Helper.getBlockData(), widget: e(t.yayCurrencyWidget).length, product: e(t.yayCurrencyProduct).length, menu: e(t.yayCurrencyMenu).length, shortcode: e(t.yayCurrencyShortcode).length, country_notice: e(t.yayCountryCurrency).length, ip_address: yay_currency_caching_data.ip_address, cache_compatible: yayCurrency.cache_compatible ?? "0", _nonce: yay_currency_caching_data.nonce }; const y = r.location.search, o = new URLSearchParams(y).get("currency"); if (o && (n.currency_code_param = o), e(t.yayCurrencyShortcodePriceHtml).length) { let r = []; e(t.yayCurrencyShortcodePriceHtml).each((function (a, c) { r[a] = e(c).data("default-price") })), n.shortcode_default_price = r } if (e(t.yayCurrencyShortcodeProductPriceHtml).length) { let r = []; e(t.yayCurrencyShortcodeProductPriceHtml).each((function (a, c) { r[a] = e(c).data("shortcode-product-id") })), n.shortcode_product_ids = r } return a && (n.productIds = c), yayCurrencyHooks.applyFilters("yayCurrencyGetListDataCaching", n) }, resetCachingLoading: function () { const r = [yay_currency_data_args.common_data_args.yayCurrencySwitcher, yay_currency_data_args.common_data_args.yayCountryCurrency, yay_currency_data_args.common_data_args.yayCurrencyShortcodePriceHtml, yay_currency_data_args.common_data_args.yayCurrencyShortcodeProductPriceHtml, yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents]; e.each(r, (function (r, a) { e(a).length && e(a).hasClass(yay_currency_data_args.common_data_args.yayCurrencyLoading) && e(a).removeClass(yay_currency_data_args.common_data_args.yayCurrencyLoading) })), yayCurrencyHooks.doAction("yayCurrencyResetCachingLoading", [{ wrapper_args: r }]) }, detectCheckoutBlocks: function () { var r = !1; return e(yay_currency_data_args.blocks_data_args.checkout).length && (r = !0), yayCurrencyHooks.applyFilters("yayCurrencyDetectCheckoutBlocks", r) }, detectCartBlocks: function () { var r = !1; return e(yay_currency_data_args.blocks_data_args.cart).length && (r = !0), yayCurrencyHooks.applyFilters("yayCurrencyDetectCartBlocks", r) }, wooCommerceBlocksForcePayment: function (r) { YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.cartBlocks), YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.checkoutBlocks), YayCurrency_Callback.Helper.detectCheckoutBlocks() && (YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.checkoutBlocks, "yes", +yayCurrency.cookie_lifetime_days), e(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).length && "1" !== yayCurrency.checkout_diff_currency && YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name)), yayCurrency.force_payment && YayCurrency_Callback.Helper.forcePaymentOnCheckoutBlocksPage(r), yayCurrency.force_currency && YayCurrency_Callback.Helper.forceCurrencyByPaymentMethodOnCheckoutBlocksPage(r), yayCurrency.checkout_notice_html && ("" != yayCurrency.checkout_notice_html && e(yay_currency_data_args.blocks_data_args.checkout).before(yayCurrency.checkout_notice_html), YayCurrency_Callback.Helper.reCalculateCartSubtotalBlocksPage(YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name)))), YayCurrency_Callback.Helper.detectCartBlocks() && (YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.cartBlocks, "yes", +yayCurrency.cookie_lifetime_days), yayCurrency.force_payment && YayCurrency_Callback.Helper.forcePaymentOnCartBlocksPage(r)), yayCurrencyHooks.doAction("yayCurrencyHandleForcePaymentBlocks", [{ data_args: yay_currency_data_args, oldCurrencyID: r }]) }, detectForcePaymentInitDomChanges: function (e) { r.onload = function () { const r = yayCurrency.total_block_class ?? (yayCurrency.cart_page ? yay_currency_data_args.blocks_data_args.totalCart : yay_currency_data_args.blocks_data_args.totalCheckout); var a = document.querySelector(r); if (a) { new MutationObserver((function (r, a) { for (var c of r) "characterData" === c.type && YayCurrency_Callback.Helper.reRenderHTMLAfterForceCountryCode(e, !0) })).observe(a, { characterData: !0, subtree: !0 }) } } }, forcePaymentOnCheckoutBlocksPage: function (e) { YayCurrency_Callback.Helper.detectForcePaymentInitDomChanges(e), YayCurrency_Callback.Helper.detectApplyCurrencyForceCountryCode(e), yayCurrencyHooks.doAction("yayCurrencyHandleForcePaymentBlocksCheckoutPage", [{ data_args: yay_currency_data_args, oldCurrencyID: e }]) }, forcePaymentOnCartBlocksPage: function (e) { YayCurrency_Callback.Helper.detectForcePaymentInitDomChanges(e), yayCurrencyHooks.doAction("yayCurrencyHandleForcePaymentBlocksCartPage", [{ data_args: yay_currency_data_args, oldCurrencyID: e }]) }, reCalculateCartSubtotalBlocksPage: function (r) { e(document.body).on("wc_fragments_refreshed", (function () { e.ajax({ url: yayCurrency.ajaxurl, type: "POST", data: { action: "yayCurrency_get_cart_subtotal_blocks", nonce: yayCurrency.nonce, fallback_currency_code: yayCurrency.fallback_currency_code ? yayCurrency.fallback_currency_code : "" }, beforeSend: function (e) { YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents) }, xhrFields: { withCredentials: !0 }, success: function (r) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), r.success && r.data.cart_subtotal && e(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).find(".woocommerce-Price-amount.amount").html(r.data.cart_subtotal), yayCurrencyHooks.doAction("yayCurrencyReCalculateCartSubtotalBlocksPage", [{ response: r }]) }, error: function (e, r, a) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents), console.log("Error responseText: ", e.responseText) } }) })) }, detectApplyCurrencyForceCountryCode: function (a) { const c = YayCurrency_Callback.Helper.getCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml); if (c && (e(yay_currency_data_args.blocks_data_args.checkout).before(c), YayCurrency_Callback.Helper.deleteCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml)), yayCurrency.currency_code) { const c = yayCurrency.currency_code; let t = null; r.yayCurrency.converted_currency.forEach((e => { e.currency != c || (t = e) })), t && t.ID !== +a && e.ajax({ url: yayCurrency.ajaxurl, type: "POST", data: { action: "yayCurrency_recalculate_apply_currency_from_blocks", nonce: yayCurrency.nonce, currencyID: a, currentCurrency: t }, beforeSend: function (e) { YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout) }, xhrFields: { withCredentials: !0 }, success: function (e) { e.success ? (e.data.force_payment_notice && YayCurrency_Callback.Helper.setCookie(yay_currency_data_args.cookies_data_args.blocksForceNoticeHtml, e.data.force_payment_notice, +yayCurrency.cookie_lifetime_days), location.reload()) : YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout) }, error: function (e, r, a) { YayCurrency_Callback.Helper.unBlockLoading(yay_currency_data_args.blocks_data_args.checkout), console.log("Error responseText: ", e.responseText) } }) } }, getCurrencyIDByMethodSelected: function (e) { const r = yayCurrency.force_currency.force_currency_payment_options; var a = jQuery.grep(r, (function (r) { return r.id === e })); if (a.length) { const r = a[0].currency[0]; if (r && "default" !== r) { const a = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(r); if (a && a.ID) { let r = a.paymentMethods; if ("0" == a.status) { const e = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(yayCurrency.fallback_currency_code); e && e.ID && (r = e.paymentMethods) } if (-1 !== r.indexOf("all") || -1 !== r.indexOf(e)) return a.ID } } } return !1 }, selectedPaymentMethodExists: function (r) { if (!r) return !1; return !!e(yay_currency_data_args.blocks_data_args.payments.options + '[value="' + r + '"]').length }, setInitWithPaymentOptionsExists: function (r, a) { let c = localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName); const t = r.first().val(); YayCurrency_Callback.Helper.selectedPaymentMethodExists(c) && t !== c || (localStorage.setItem(yay_currency_data_args.blocks_data_args.payments.localStorageName, t), c = t); const n = e(yay_currency_data_args.blocks_data_args.payments.options + '[value="' + c + '"]').closest(yay_currency_data_args.blocks_data_args.payments.class.parent).find(yay_currency_data_args.blocks_data_args.payments.class.labelOption); n.length && (YayCurrency_Callback.Helper.handleForceCurrencyByPaymentMethod(c, a), n.click()) }, setInitPaymentMethodSelected: function (r) { let a = e(yay_currency_data_args.blocks_data_args.payments.options); if (a.length) YayCurrency_Callback.Helper.setInitWithPaymentOptionsExists(a, r); else if (e(yay_currency_data_args.blocks_data_args.payments.container).length) { let c = 1, t = setInterval((function () { (e(yay_currency_data_args.blocks_data_args.payments.options).length || 3 === c) && (a = e(yay_currency_data_args.blocks_data_args.payments.options), a.length ? (YayCurrency_Callback.Helper.setInitWithPaymentOptionsExists(a, r), clearInterval(t)) : --c), ++c }), 500) } }, paymentMethodSelectedInitCheckoutBlocks: function (e) { let r = !1; new MutationObserver((a => { a.forEach((a => { "childList" === a.type && a.addedNodes.forEach((a => { 1 === a.nodeType && "payment-method" === a.id && (r = !0, YayCurrency_Callback.Helper.setInitPaymentMethodSelected(e)) })) })) })).observe(document.body, { childList: !0, subtree: !0 }), setTimeout((() => { r || YayCurrency_Callback.Helper.setInitPaymentMethodSelected(e) }), 500) }, handleChangePaymentMethod: function (r) { e(document).on("change", yay_currency_data_args.blocks_data_args.payments.options, (function () { const a = localStorage.getItem(yay_currency_data_args.blocks_data_args.payments.localStorageName), c = e(this).val(); a !== c && (localStorage.setItem(yay_currency_data_args.blocks_data_args.payments.localStorageName, c), YayCurrency_Callback.Helper.handleForceCurrencyByPaymentMethod(c, r)) })) }, handleForceCurrencyByPaymentMethod: function (e, r) { const a = YayCurrency_Callback.Helper.getCurrencyIDByMethodSelected(e); a && a !== +r && (YayCurrency_Callback.Helper.blockLoading(yay_currency_data_args.blocks_data_args.checkout), YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_switcher_name, a, +yayCurrency.cookie_lifetime_days), YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name, a, +yayCurrency.cookie_lifetime_days), location.reload()) }, forceCurrencyByPaymentMethodOnCheckoutBlocksPage: function (e) { YayCurrency_Callback.Helper.paymentMethodSelectedInitCheckoutBlocks(e), YayCurrency_Callback.Helper.handleChangePaymentMethod(e) }, approximatePriceCheckoutBlocks: function (a) { if (YayCurrency_Callback.Helper.detectCheckoutBlocks()) { const c = YayCurrency_Callback.Helper.getCurrentCurrency(a); if ("0" === yayCurrency.checkout_diff_currency && yayCurrency.default_currency_code !== c.currency || "1" === yayCurrency.checkout_diff_currency && "0" === c.status) { YayCurrency_Callback.Helper.addApproximatePrices(c); const a = new MutationObserver((function (e) { YayCurrency_Callback.Helper.addApproximatePrices(c) })); a.observe(document.querySelector(".wc-block-checkout"), { childList: !0, subtree: !0 }), e(r).on("unload", (function () { a.disconnect() })) } } }, addApproximatePrices: function (r) { e(".wc-block-checkout__order-summary-item__total-price, .wc-block-formatted-money-amount").each((function () { if (!e(this).find(".yay-currency-checkout-converted-approximately").length) { const a = e(this).text().trim(); let c = YayCurrency_Callback.Helper.parsePrice(a); if (!isNaN(c)) { const a = YayCurrency_Callback.Helper.approximatePriceHTML(c, r); e(this).append(a) } } })) }, parsePrice: function (e) { let r = e.replace(/[^0-9\s_,.]/g, "").trim(); if (!r) return 0; let a = r.split(/[\s_,.]+/); if (a.length < 1) return parseFloat(r) || 0; let c = a.pop() || "0", t = a.join(""); return parseFloat(t + (c ? "." + c : "")) || 0 }, approximatePriceHTML: function (e, r) { if (yayCurrency.default_currency_code !== yayCurrency.fallback_currency_code) { const r = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(yayCurrency.fallback_currency_code); e = YayCurrency_Callback.Helper.handelRevertPrice(e, r) } return " <span class='yay-currency-checkout-converted-approximately'>(~" + YayCurrency_Callback.Helper.formatPriceByCurrency(e, !0, r) + ")</span>" }, getCurrentCurrencyByCode: function (e = !1, a = !1) { e = e || r.yayCurrency.default_currency_code, a = a || r.yayCurrency.converted_currency; let c = !1; return a && a.forEach((r => { r.currency === e && (c = r) })), c }, currencyConverter: function () { const r = yay_currency_data_args.converter_args.converterWrapper; e(r).length && e(r).each((function (r, a) { YayCurrency_Callback.Helper.doConverterCurrency(e(a)) })) }, doFormatNumber: function (e, r, a, c, t = !1) { if ("N/A" === e || "" === e) return e; e = (e + "").replace(/[^0-9+\-Ee.]/g, ""); let n = isFinite(+e) ? +e : 0, y = isFinite(+r) ? Math.abs(r) : 0, o = void 0 === c ? "," : c, l = void 0 === a ? "." : a, u = ""; return u = (y ? function (e, r) { let a = Math.pow(10, r); return "" + Math.round(e * a) / a }(n, y) : "" + Math.round(n)).split("."), u[0].length > 3 && (u[0] = u[0].replace(/\B(?=(?:\d{3})+(?!\d))/g, o)), (u[1] || "").length < y && (u[1] = u[1] || "", u[1] += new Array(y - u[1].length + 1).join("0")), t ? u.join(l) : u.join(l).replace(/([0-9]*\.0*[1-9]+)0+$/gm, "$1").replace(/.00+$/, "") }, roundedAmountByCurrency: function (e, r) { if (!r) return e; const { numberDecimal: a, decimalSeparator: c, thousandSeparator: t } = r; e = YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(e, r); return YayCurrency_Callback.Helper.doFormatNumber(e, Number(a), c, t, !0) }, handelRoundedPriceByCurrency: function (e, r) { const { roundingType: a, roundingValue: c, subtractAmount: t } = r; switch (a) { case "up": e = Math.ceil(e / c) * c - t; break; case "down": e = Math.floor(e / c) * c - t; break; case "nearest": e = Math.round(e / c) * c - t }return e }, handelConvertPrice: function (e = 0, r = !1) { if (!r) { const e = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name); r = YayCurrency_Callback.Helper.getCurrentCurrency(e) } const a = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(r)); return a && 1 !== a ? YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(e * a, r) : e }, handelRevertPrice: function (e = 0, r = !1) { if (!r) { const e = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name); r = YayCurrency_Callback.Helper.getCurrentCurrency(e) } const a = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(r)); return a && 1 !== a ? e / a : e }, decodeHtmlEntity: function (e) { var r = document.createElement("textarea"); return r.innerHTML = e, r.value }, formatPricePosition: function (e = 0, r = "", a = "left") { let c = e; switch (a) { case "left": c = r + c; break; case "right": c += r; break; case "left_space": c = r + " " + c; break; case "right_space": c = c + " " + r }return c }, formatPriceByCurrency: function (e = 0, r = !1, a = !1) { if (!a) { const e = YayCurrency_Callback.Helper.getCookie(yayCurrency.cookie_name); a = YayCurrency_Callback.Helper.getCurrentCurrency(e) } if (r) { const r = parseFloat(YayCurrency_Callback.Helper.getRateFeeByCurrency(a)); e = YayCurrency_Callback.Helper.handelRoundedPriceByCurrency(e * r, a) } var c = e.toFixed(a.numberDecimal).split("."); c[0] = c[0].replace(/\B(?=(\d{3})+(?!\d))/g, a.thousandSeparator); var t = c.join(a.decimalSeparator), n = YayCurrency_Callback.Helper.decodeHtmlEntity(a.symbol); return t = YayCurrency_Callback.Helper.formatPricePosition(t, n, a.currencyPosition), a.currencyCodePosition && (t = YayCurrency_Callback.Helper.formatPricePosition(t, a.currency, a.currencyCodePosition)), t }, doApplyResultConverter: function (r, a) { const c = r.find(yay_currency_data_args.converter_args.converterFrom), t = r.find(yay_currency_data_args.converter_args.converterTo), n = a.from_currency_code ? a.from_currency_code : e(c).val(), y = a.to_currency_code ? a.to_currency_code : e(t).val(); let o = a.amount_value ? +a.amount_value : +e(r.find(yay_currency_data_args.converter_args.converterAmount)).val(); if (y === n) e(r.find(yay_currency_data_args.converter_args.converterResultValue)).text(o); else { const a = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(n), c = YayCurrency_Callback.Helper.getCurrentCurrencyByCode(y), t = YayCurrency_Callback.Helper.getRateFeeByCurrency(c); if (a && n !== yayCurrency.default_currency_code) { const e = YayCurrency_Callback.Helper.getRateFeeByCurrency(a); o *= parseFloat(1 / e) } e(r.find(yay_currency_data_args.converter_args.converterResultValue)).text(YayCurrency_Callback.Helper.roundedAmountByCurrency(o * t, c)) } }, doConverterCurrency: function (r) { const a = r.find(yay_currency_data_args.converter_args.converterAmount), c = r.find(yay_currency_data_args.converter_args.converterFrom), t = r.find(yay_currency_data_args.converter_args.converterTo), n = r.find(yay_currency_data_args.converter_args.converterResultWrapper); e(c).change((function () { e(r.find(yay_currency_data_args.converter_args.converterResultFrom)).text(e(this).val()), YayCurrency_Callback.Helper.doApplyResultConverter(r, { from_currency_code: e(this).val() }) })), e(t).change((function () { e(r.find(yay_currency_data_args.converter_args.converterResultTo)).text(e(this).val()), YayCurrency_Callback.Helper.doApplyResultConverter(r, { to_currency_code: e(this).val() }) })), e(a).on("input", (function () { const a = e(this).val(); e(this).val(a.replace(/\D/g, "")), a ? (e(n).show(), e(r.find(yay_currency_data_args.converter_args.converterResultAmount)).text(a), YayCurrency_Callback.Helper.doApplyResultConverter(r, { amount_value: a })) : e(n).hide() })), e(a).trigger("input"), e(c).trigger("change"), e(t).trigger("change") }, handleFilterByPrice: function (e) { r.addEventListener("load", (function () { YayCurrency_Callback.Helper.handleFilterByPriceClassicEditor(e), YayCurrency_Callback.Helper.handleFilterByPriceBlock(e) })) }, handleFilterByPriceClassicEditor: function (a) { if (e(".widget_price_filter .price_slider").length) { const c = YayCurrency_Callback.Helper.getCurrentCurrency(a); if (c.currency === r.yayCurrency.default_currency_code) return; let t = e(".price_slider_amount #min_price").val(), n = e(".price_slider_amount #max_price").val(); if (!t || !n) return; e(document.body).on("price_slider_create price_slider_slide", (function (r, a, t) { e(".price_slider_amount span.from").html(YayCurrency_Callback.Helper.formatPriceByCurrency(a, !0, c)), e(".price_slider_amount span.to").html(YayCurrency_Callback.Helper.formatPriceByCurrency(t, !0, c)) })), e(".price_slider_amount span.from").html(YayCurrency_Callback.Helper.formatPriceByCurrency(t, !0, c)), e(".price_slider_amount span.to").html(YayCurrency_Callback.Helper.formatPriceByCurrency(n, !0, c)), yayCurrencyHooks.doAction("yayCurrencyHandleFilterPriceClassicEditor", [{ current_currency_id: a }]) } }, handleFilterByPriceBlock: function (a) { if (!r.wc) return; const c = e(yay_currency_data_args.blocks_data_args.filterPrice.class.wrapper); if (c.length && c.find(yay_currency_data_args.blocks_data_args.filterPrice.class.controls).length) { let r = 1, a = !1, c = setInterval((function () { let t = e(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceWrapper), n = e(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceWrapper); if (t.length && n.length) { const r = e(yay_currency_data_args.blocks_data_args.filterPrice.class.filterSlideInput).parents(yay_currency_data_args.blocks_data_args.filterPrice.class.controls), c = r.clone(); r.replaceWith(c); const y = !!t.attr("aria-valuetext") && +t.attr("aria-valuetext"); y && (e(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(y, !0)), e(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).css("pointer-events", "none")); const o = !!n.attr("aria-valuetext") && +n.attr("aria-valuetext"); o && (e(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(o, !0)), e(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).css("pointer-events", "none")), a = !0 } (5 === r || a) && clearInterval(c), ++r }), 500) } e(document).on("input", yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceWrapper, (function () { const r = !!e(this).attr("aria-valuetext") && +e(this).attr("aria-valuetext"); r && e(yay_currency_data_args.blocks_data_args.filterPrice.class.minPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(r, !0)) })), e(document).on("input", yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceWrapper, (function () { const r = !!e(this).attr("aria-valuetext") && +e(this).attr("aria-valuetext"); r && e(yay_currency_data_args.blocks_data_args.filterPrice.class.maxPriceInput).val(YayCurrency_Callback.Helper.formatPriceByCurrency(r, !0)) })), yayCurrencyHooks.doAction("yayCurrencyHandleFilterPriceBlock", [{ current_currency_id: a }]) } } }(jQuery, window);