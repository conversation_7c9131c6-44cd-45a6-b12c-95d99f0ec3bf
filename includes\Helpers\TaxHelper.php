<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class TaxHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function fetch_product_tax_info( $product ) {
		$rates   = \WC_Tax::get_rates( $product->get_tax_class() );
		$rate_id = key( $rates );

		return array(
			'tax_rate_id'      => $rate_id,
			'tax_rate_percent' => \WC_Tax::get_rate_percent_value( $rate_id ),
			'tax_rate'         => \WC_Tax::_get_tax_rate( $rate_id ),
			'tax_code'         => \WC_Tax::get_rate_code( $rate_id ),
			'tax_compound'     => \WC_Tax::is_compound( $rate_id ),
		);
	}
	public static function get_taxes_in_cart( $apply_currency, $cart_contents, $calculate_default = false ) {
		$taxes = array();

		foreach ( $cart_contents as $cart_item ) {
			$product_id     = ! empty( $cart_item['variation_id'] ) ? $cart_item['variation_id'] : $cart_item['product_id'];
			$product_object = wc_get_product( $product_id );
			$tax_info       = self::fetch_product_tax_info( $product_object );
			$rate_id        = $tax_info['tax_rate_id'];
			$quantity       = $cart_item['quantity'];
			$tax_status     = $product_object->get_tax_status();

			// Skip if product is non-taxable or tax rate is zero
			if ( 'taxable' !== $tax_status || $tax_info['tax_rate_percent'] <= 0 ) {
				continue;
			}

			// Calculate product subtotal
			$product_price = $calculate_default
				? SupportHelper::get_product_price_default_by_cart_item( $cart_item )
				: SupportHelper::get_product_price_by_cart_item( $cart_item, $apply_currency );
			$product_price = apply_filters( 'yay_currency_get_product_price_apply_currency', $product_price, $cart_item['data'], 1 );
			$subtotal      = $product_price * $quantity;

			// Prepare product data
			$product_data = [
				'product_id' => $product_id,
				'quantity'   => $quantity,
				'subtotal'   => $subtotal,
				'is_virtual' => $product_object->is_virtual(),
				'tax_status' => $tax_status,
			];

			// Initialize or update tax data
			if ( ! isset( $taxes[ $rate_id ] ) ) {
				$taxes[ $rate_id ] = array(
					'tax_rate_percent' => $tax_info['tax_rate_percent'],
					'tax_rate'         => $tax_info['tax_rate'],
					'subtotal'         => 0,
					'products'         => array(),
					'quantity_product' => 0,
				);
			}

			$taxes[ $rate_id ]['subtotal']               += $subtotal;
			$taxes[ $rate_id ]['quantity_product']       += $quantity;
			$taxes[ $rate_id ]['products'][ $product_id ] = $product_data;
		}

		return array(
			'taxes'     => $taxes,
			'tax_apply' => array_keys( WC()->cart->get_taxes() ),
		);
	}

	public static function get_info_taxes_include_in_cart( $apply_currency, $shipping_total, $calculate_default = false ) {
		$cart_contents = WC()->cart->get_cart_contents();
		$taxes_info    = self::get_taxes_in_cart( $apply_currency, $cart_contents, $calculate_default );
		if ( isset( $taxes_info['taxes'] ) ) {
			$taxes                = $taxes_info['taxes'];
			$total_tax            = 0;
			$total_coupon_applies = 0;
			$total_tax_fees       = SupportHelper::get_total_fees( $apply_currency, false, $calculate_default );
			ksort( $taxes );
			foreach ( $taxes as $tax_key => $tax_value ) {
				$total_coupon_applies = WC()->cart->applied_coupons ? self::get_total_coupon_applies( $apply_currency, $tax_value['subtotal'], $tax_value['quantity_product'], $tax_value['products'], $calculate_default ) : 0;
				$tax_subtotal         = $tax_value['subtotal'] - $total_coupon_applies + $total_tax_fees;
				$tax_percent          = $tax_value['tax_rate_percent'] / 100;
				if ( in_array( $tax_key, $taxes_info['tax_apply'], true ) ) {
					$tax_rate_shipping = isset( $tax_value['tax_rate']['tax_rate_shipping'] ) ? (int) $tax_value['tax_rate']['tax_rate_shipping'] : false;
					$tax_rate_shipping = in_array( false, array_column( $tax_value['products'], 'is_virtual' ), true ) ? $tax_rate_shipping : false;
					if ( $tax_value['subtotal'] < $total_coupon_applies ) {
						if ( $tax_rate_shipping ) {
							$total_by_tax   = $shipping_total * $tax_percent;
							$shipping_total = 0;
						} else {
							$total_by_tax = 0;
						}
					} elseif ( $tax_rate_shipping ) {
							$total_by_tax   = ( $tax_subtotal + $shipping_total ) * $tax_percent;
							$shipping_total = 0;
					} else {
						$total_by_tax = $tax_subtotal * $tax_percent;
					}
				} else {
					$total_by_tax = $tax_subtotal * $tax_percent;
				}

				// detect is virtual
				if ( 1 === count( WC()->cart->cart_contents ) && isset( $tax_value['products'] ) ) {
					$taxes[ $tax_key ]['tax_status'] = array_shift( $tax_value['products'] )['tax_status'];
					$taxes[ $tax_key ]['is_virtual'] = in_array( true, array_column( $tax_value['products'], 'is_virtual' ), true );
				}

				$total_tax                     = $total_tax + $total_by_tax;
				$taxes[ $tax_key ]['subtotal'] = $total_by_tax;

			}

			return array(
				'taxes'     => $taxes,
				'tax_apply' => $taxes_info['tax_apply'],
				'total_tax' => $total_tax,
			);
		}

		return false;

	}

	public static function get_cart_subtotal_by_fixed_include_tax( $apply_currency ) {
		$shipping_total = SupportHelper::get_shipping_total_selected( $apply_currency, false, true );
		$taxes_in_cart  = self::get_info_taxes_include_in_cart( $apply_currency, $shipping_total );
		$subtotal       = $taxes_in_cart ? $taxes_in_cart['total_tax'] : 0;
		if ( self::is_apply_multiple_taxes() ) {
			$subtotal = self::recalculate_excl_total_taxes_in_cart( $subtotal, $apply_currency );
		}
		return $subtotal;
	}

	// Recalculate with Multiple Taxes Excl Tax

	public static function is_use_excl_tax() {
		$tax_display_shop = get_option( 'woocommerce_tax_display_shop' );
		$tax_display_cart = get_option( 'woocommerce_tax_display_cart' );
		if ( ! wc_prices_include_tax() && 'excl' === $tax_display_shop && 'excl' === $tax_display_cart ) {
			return true;
		}
		return false;
	}

	public static function is_apply_multiple_taxes( $flag = false ) {

		if ( ! wc_tax_enabled() ) {
			$flag = false;
		}

		$cart_taxes = array_keys( WC()->cart->get_taxes() );
		if ( count( $cart_taxes ) > 1 ) {
			return $cart_taxes;
		}

		return $flag;
	}

	public static function recalculate_total_tax_excl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency ) {
		$tax_percent = \WC_Tax::get_rate_percent_value( $tax_rate_id );
		if ( $tax_percent ) {
			$tax_percent = $tax_percent / 100;
			$taxes_info  = self::get_taxes_in_cart( $apply_currency, WC()->cart->get_cart_contents() );

			if ( isset( $taxes_info['taxes'] ) && isset( $taxes_info['taxes'][ $tax_rate_id ] ) ) {
				$shipping_total_tax = 0;
				if ( WC()->cart->get_shipping_taxes() ) {
					$shipping_taxes = array_keys( WC()->cart->get_shipping_taxes() );
					if ( in_array( $tax_rate_id, $shipping_taxes, true ) ) {
						$shipping_total_tax = apply_filters( 'yay_currency_get_shipping_total', 0, $apply_currency, false );
					}
				}
				$total_fees_tax = 0;
				if ( WC()->cart->get_fee_taxes() ) {
					$shipping_taxes = array_keys( WC()->cart->get_fee_taxes() );
					if ( in_array( $tax_rate_id, $shipping_taxes, true ) ) {
						$total_fees_tax = ( SupportHelper::get_total_fees( $apply_currency, false ) );
					}
				}

				$total_coupon_applies = SupportHelper::get_total_coupons( $taxes_info['taxes'][ $tax_rate_id ]['subtotal'], $apply_currency );
				$cart_subtotal_tax    = $taxes_info['taxes'][ $tax_rate_id ]['subtotal'] - $total_coupon_applies;

				$tax_total = ( $cart_subtotal_tax + $shipping_total_tax + $total_fees_tax ) * $tax_percent;
			}
		}
		return $tax_total;
	}

	public static function recalculate_excl_total_taxes_in_cart( $tax_total, $apply_currency ) {
		if ( self::is_use_excl_tax() ) {
			$cart_taxes = self::is_apply_multiple_taxes();
			if ( ! $cart_taxes ) {
				return $tax_total;
			}
			$tax_total = 0;
			foreach ( $cart_taxes as $tax_rate_id ) {
				$tax_total += self::recalculate_total_tax_excl_by_rate_id( $tax_total, $tax_rate_id, $apply_currency );
			}
		}
		return $tax_total;
	}

	public static function get_price_by_tax_rate_id( $apply_currency, $tax_rate_id, $is_fallback = false ) {
		$shipping_total = SupportHelper::get_shipping_total_selected( $apply_currency, false, true, $is_fallback );
		$taxes_in_cart  = self::get_info_taxes_include_in_cart( $apply_currency, $shipping_total );
		$price          = $taxes_in_cart ? ( isset( $taxes_in_cart['taxes'][ $tax_rate_id ] ) ? $taxes_in_cart['taxes'][ $tax_rate_id ]['subtotal'] : 0 ) : 0;
		if ( self::is_apply_multiple_taxes() ) {
			$price = self::recalculate_total_tax_excl_by_rate_id( $price, $tax_rate_id, $apply_currency );
		}
		return $price;
	}

	public static function get_total_coupon_applies( $apply_currency, $product_subtotal, $quantity_product, $tax_products = array(), $calculate_default = false ) {

		$total_coupon_applies = 0;
		if ( WC()->cart->applied_coupons ) {
			$applied_coupons = WC()->cart->applied_coupons;
			foreach ( $applied_coupons  as $coupon_code ) {
				$coupon          = new \WC_Coupon( $coupon_code );
				$discount_type   = $coupon->get_discount_type();
				$discount_amount = (float) $coupon->get_data()['amount'];
				$coupon_data     = $coupon->get_data();
				$products_ids    = isset( $coupon_data['product_ids'] ) && $coupon_data['product_ids'] ? $coupon_data['product_ids'] : false;
				if ( SupportHelper::woo_discount_rules_active() && $products_ids ) {
					if ( 'percent' === $discount_type ) {
						$cart_subtotal_apply_discount     = YayCurrencyHelper::calculate_price_by_currency( $discount_amount, true, $apply_currency );
						$cart_subtotal_apply_rule_as_cart = apply_filters( 'yay_currency_discount_rules_get_cart_subtotal_apply_coupon_as_cart_rule', $cart_subtotal_apply_discount, $discount_amount, $products_ids, $apply_currency );
						$total_coupon_applies            += $cart_subtotal_apply_rule_as_cart;
					}
				} elseif ( 'percent' !== $discount_type ) {
					if ( 'fixed_product' === $discount_type ) {
						$discount_amount *= $quantity_product;
					}

					if ( ! $calculate_default ) {
						$cart_subtotal_apply_discount = YayCurrencyHelper::calculate_price_by_currency( $discount_amount, true, $apply_currency );

						if ( self::has_fixed_price_product_in_tax_class( $apply_currency, $tax_products ) ) {
							$cart_subtotal_fixed_apply_discount = self::calculate_discounted_fixed_tax_subtotal( $apply_currency, $tax_products, (float) $discount_amount );
							$cart_subtotal_fixed_apply_discount = $cart_subtotal_fixed_apply_discount > $cart_subtotal_apply_discount ? $cart_subtotal_apply_discount : $cart_subtotal_fixed_apply_discount;
							$total_coupon_applies              += $cart_subtotal_fixed_apply_discount;
						} else {
							$total_coupon_applies += $cart_subtotal_apply_discount;
						}
					} else {
						$total_coupon_applies += $discount_amount;
					}
				} else {
					$total_coupon_applies += ( $product_subtotal * $discount_amount ) / 100;
				}
			}
		}
		return $total_coupon_applies;
	}

	public static function has_fixed_price_product_in_tax_class( $apply_currency, $tax_products ) {
		if ( ! $tax_products ) {
			return FixedPriceHelper::has_fixed_price_product_in_cart( $apply_currency );
		}
		$flag = false;
		foreach ( $tax_products  as $tax_product ) {
			$product     = wc_get_product( $tax_product['product_id'] );
			$fixed_price = FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $apply_currency );
			if ( $fixed_price ) {
				$flag = true;
				break;
			}
		}
		return $flag;
	}

	public static function calculate_discounted_fixed_tax_subtotal( $apply_currency, $tax_products, $coupon_amount ) {
		$cart_subtotal_fixed = 0;
		if ( $tax_products ) {
			foreach ( $tax_products  as $tax_product ) {
				$quantity             = isset( $tax_product['quantity'] ) ? $tax_product['quantity'] : 1;
				$product              = wc_get_product( $tax_product['product_id'] );
				$fixed_product_price  = FixedPriceHelper::product_is_set_fixed_price_by_currency( $product, $apply_currency );
				$cart_subtotal_fixed += $fixed_product_price ? $fixed_product_price * $quantity : YayCurrencyHelper::calculate_price_by_currency( $coupon_amount, true, $apply_currency ) * $quantity;
			}
		} else {
			$cart_subtotal_fixed = FixedPriceHelper::calculate_discounted_fixed_cart_subtotal( $apply_currency, (float) $coupon_amount );
		}
		return $cart_subtotal_fixed;
	}
}
