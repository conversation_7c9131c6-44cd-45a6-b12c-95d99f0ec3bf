<?php
namespace Yay_Currency\Engine;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\RateHelper;

class ScheduleJobs {

	use SingletonTrait;

	protected function __construct() {
		// Move hooks to init action to prevent loading translations too early
		add_action( 'init', array( $this, 'init_hooks' ) );
	}

	public function init_hooks() {
		add_filter( 'cron_schedules', array( $this, 'auto_update_exchange_rate_schedule' ) );
		add_action( 'yay_currency_schedule', array( $this, 'call_api_update_exchange_rate_by_schedule' ) );
		if ( ! wp_next_scheduled( 'yay_currency_schedule' ) ) {
			wp_schedule_event( time(), 'yay_currency_update_exchange_rate_schedule', 'yay_currency_schedule' );
		}
	}

	public function auto_update_exchange_rate_schedule( $schedules ) {
		$is_auto_update_exchange_rate = get_option( 'yay_currency_update_exchange_rate_auto', 0 );

		if ( ! $is_auto_update_exchange_rate ) {
			return $schedules;
		}

		$update_interval_object = get_option( 'yay_currency_time_update_exchange_rate_auto' );

		$update_interval_object_unserialized = maybe_unserialize( $update_interval_object );

		if ( ! is_array( $update_interval_object_unserialized ) ) {
			delete_option( 'yay_currency_time_update_exchange_rate_auto' );
			return $schedules;
		}

		switch ( $update_interval_object_unserialized['type'] ) {
			case 'mins':
				$schedules['yay_currency_update_exchange_rate_schedule'] = array(
					'interval' => $update_interval_object_unserialized['value'] * 60,
					'display'  => __( 'By minutes', 'yay-currency' ),
				);
				break;
			case 'hours':
				$schedules['yay_currency_update_exchange_rate_schedule'] = array(
					'interval' => ( $update_interval_object_unserialized['value'] * 60 ) * 60,
					'display'  => __( 'By hours', 'yay-currency' ),
				);
				break;
			case 'days':
				$schedules['yay_currency_update_exchange_rate_schedule'] = array(
					'interval' => ( ( $update_interval_object_unserialized['value'] * 60 ) * 60 ) * 24,
					'display'  => __( 'By days', 'yay-currency' ),
				);
				break;
			default:
				return $schedules;
		}

		return $schedules;
	}

	public function call_api_update_exchange_rate_by_schedule() {
		$default_currency = Helper::default_currency_code();
		$yay_currencies   = Helper::get_currencies_post_type();
		RateHelper::update_exchange_rate_currency( $yay_currencies, $default_currency, true );
	}
}
