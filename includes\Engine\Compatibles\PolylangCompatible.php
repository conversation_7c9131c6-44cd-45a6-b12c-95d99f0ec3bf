<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;

use Yay_Currency\Helpers\SupportHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

class PolylangCompatible {

	use SingletonTrait;

	private $language;
	private $currencies_by_languages;

	public function __construct() {
		if ( ! function_exists( 'pll_get_post_translations' ) ) {
			return;
		}

		add_filter( 'yay_currency_multiple_language_active', '__return_true' );
		add_filter( 'yay_currency_get_apply_currency_by_language_3rd_plugin', array( $this, 'get_apply_currency_by_language_3rd_plugin' ), 10, 2 );
		add_filter( 'yay_currency_is_not_detect_language', array( $this, 'is_not_detect_language' ), 10, 1 );

		add_filter( 'locale', array( $this, 'yay_currency_set_current_language' ), 99999 );
		add_filter( 'yay_currency_send_data_settings_args', array( $this, 'get_polylang_data' ), 10, 1 );

		add_filter( 'woocommerce_cart_subtotal', array( $this, 'recalculate_cart_subtotal_mini_cart' ), 10, 3 );

		// Support Caching
		add_filter( 'yay_currency_get_current_currency_by_caching_with_multiple_language', array( $this, 'get_current_currency_by_caching_with_multiple_language' ), 10, 2 );

	}

	public function get_apply_currency_by_language_3rd_plugin( $apply_currency, $converted_currency ) {
		if ( 0 === intval( get_option( 'yay_currency_polylang_compatible', 0 ) ) ) {
			return $apply_currency;
		}

		$currencies_by_languages = get_option( 'yay_currency_currencies_by_languages_polylang', array() );
		$selected_language       = get_locale();
		$apply_currency          = SupportHelper::get_apply_currency_by_polylang_or_wpml( $apply_currency, $currencies_by_languages, $selected_language, $converted_currency );

		return $apply_currency;
	}

	public function is_not_detect_language( $flag ) {
		if ( function_exists( 'pll_current_language' ) && ! pll_current_language() ) {
			$flag = true;
		}
		return $flag;
	}

	public function yay_currency_set_current_language( $locale ) {
		$cookie_language = YayCurrencyHelper::get_cookie_name( 'language' );
		// Set current Language with Ajax
		if ( wp_doing_ajax() && isset( $_COOKIE[ $cookie_language ] ) && 1 === intval( get_option( 'yay_currency_polylang_compatible', 0 ) ) ) {
			$locale = sanitize_text_field( $_COOKIE[ $cookie_language ] );
		}

		return $locale;
	}

	public function get_polylang_data( $data ) {
		$list_current_polylang_languages   = pll_languages_list( array( 'fields' => array() ) );
		$converted_list_polylang_languages = array();
		$currencies_by_languages           = get_option( 'yay_currency_currencies_by_languages_polylang', array() );
		foreach ( $list_current_polylang_languages as $language ) {
			$flag = false;
			if ( $currencies_by_languages ) {
				$converted_language = SupportHelper::get_filtered_currency_by_language( $currencies_by_languages, $language->locale );
				if ( $converted_language ) {
					$converted_language = array_shift( $converted_language );
					$flag               = true;
				}
			}

			if ( ! $flag ) {
				$converted_language = array(
					'code'     => $language->locale,
					'language' => $language->name,
					'currency' => 'default',
				);
			}

			array_push( $converted_list_polylang_languages, $converted_language );
		}

		$data['currency_manage_tab_data']['listCurrentPolylangLanguages'] = $converted_list_polylang_languages;
		return $data;
	}

	public function recalculate_cart_subtotal_mini_cart( $cart_subtotal, $compound, $cart ) {
		if ( defined( 'ELEMENTOR_PRO_VERSION' ) && doing_filter( 'astra_header' ) ) {
			$converted_currency = YayCurrencyHelper::converted_currency();
			$apply_currency     = self::get_apply_currency_by_language_3rd_plugin( array(), $converted_currency );
			if ( $apply_currency ) {
				$subtotal      = apply_filters( 'yay_currency_get_cart_subtotal', $cart_subtotal, $apply_currency );
				$cart_subtotal = YayCurrencyHelper::format_price( $subtotal );
			}
		}
		return $cart_subtotal;
	}

	public function get_current_currency_by_caching_with_multiple_language( $apply_currency, $converted_currency ) {
		if ( 0 !== intval( get_option( 'yay_currency_polylang_compatible', 0 ) ) ) {
			$currencies_by_languages       = get_option( 'yay_currency_currencies_by_languages_polylang', array() );
			$selected_language             = get_locale();
			$filtered_currency_by_language = SupportHelper::get_filtered_currency_by_language( $currencies_by_languages, $selected_language );
			if ( ! $filtered_currency_by_language || ! is_array( $filtered_currency_by_language ) ) {
				return $apply_currency;
			}
			$filtered_currency_by_language = array_shift( $filtered_currency_by_language );
			$currency_code                 = isset( $filtered_currency_by_language['currency'] ) ? $filtered_currency_by_language['currency'] : false;
			if ( $currency_code ) {
				$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $converted_currency );
			}
		}
		return $apply_currency;
	}
}
