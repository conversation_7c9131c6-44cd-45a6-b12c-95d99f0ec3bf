<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\CountryHelper;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\Helper;

use LP_Settings_Courses;

defined( 'ABSPATH' ) || exit;

// link plugin : https://wordpress.org/plugins/learnpress/

class LearnPress {
	use SingletonTrait;

	private $apply_currency = array();

	public function __construct() {

		if ( ! class_exists( '\LP_Admin_Assets' ) ) {
			return;
		}

		$this->apply_currency = YayCurrencyHelper::detect_current_currency();

		add_filter( 'learn-press/course/price', array( $this, 'custom_course_price' ), 10, 2 );
		add_filter( 'learn-press/course/regular-price', array( $this, 'custom_course_regular_price' ), 10, 2 );
		add_filter( 'learn_press_currency_symbol', array( $this, 'learn_press_currency_symbol' ), 10, 2 );

		// Archive
		add_filter( 'learnPress/course/price', array( $this, 'archive_course_price' ), 10, 2 );
		add_filter( 'learnPress/course/regular-price', array( $this, 'archive_course_regular_price' ), 10, 2 );

		add_filter( 'learn-press/course/regular-price-html', array( $this, 'archive_course_regular_price_html' ), 10, 2 );
		add_filter( 'learn_press_course_price_html', array( $this, 'archive_course_price_html' ), 10, 3 );

		// Cart Item Subtotal
		add_filter( 'learn-press/cart/item-subtotal', array( $this, 'cart_course_item_subtotal' ), 10, 4 );

		// LearnPress - WooCommerce Payment Methods Integration
		if ( defined( 'LP_ADDON_WOO_PAYMENT_VER' ) ) {
			add_filter( 'learn-press/woo-course/price', array( $this, 'get_woo_course_price_custom' ), 10, 2 );
			add_filter( 'learn-press/woo-course/regular-price', array( $this, 'get_woo_course_price_custom' ), 10, 2 );
			add_filter( 'learn-press/woo-course/sale-price', array( $this, 'get_woo_course_price_custom' ), 10, 2 );
		}

		// Added Course Fixed Prices option for Each currency

		if ( FixedPriceHelper::is_set_fixed_price() ) {
			add_action( 'learnpress/course-settings/after-price', array( $this, 'add_fixed_price_fields_to_pricing_tab' ) );
			add_filter( 'save_post_lp_course', array( $this, 'save_fixed_price_fields_custom' ), 999, 2 );
		}

	}

	// Detect is Admin dashboard
	protected function is_admin_dashboard() {
		return is_admin() && ! wp_doing_ajax();
	}

	// Get Course Fixed Price if have
	protected function get_course_fixed_price( $course_price, $course_id, $type = 'price' ) {

		if ( ! FixedPriceHelper::is_set_fixed_price() || ! isset( $this->apply_currency ) || ! isset( $this->apply_currency['currency'] ) ) {
			return $course_price;
		}

		$currency_code = $this->apply_currency['currency'];

		$custom_fixed_prices = get_post_meta( $course_id, 'yay_currency_custom_fixed_prices', true );
		$custom_fixed_prices = isset( $custom_fixed_prices[ $currency_code ] ) && ! empty( $custom_fixed_prices[ $currency_code ] ) ? $custom_fixed_prices[ $currency_code ] : false;
		if ( ! $custom_fixed_prices ) {
			return $course_price;
		}

		$regular_price = FixedPriceHelper::get_regular_price( $custom_fixed_prices, $course_price );

		if ( 'regular_price' === $type ) {
			return $regular_price;
		}

		$course = learn_press_get_course( $course_id );

		if ( ! $course || ! $course->has_sale_price() ) {
			return $regular_price;
		}

		return FixedPriceHelper::get_sale_price( $custom_fixed_prices, $course_price );
	}

	// Get Course price from original currency
	protected function get_course_price( $course_id, $type = 'price' ) {
		$prices = array(
			'regular' => get_post_meta( $course_id, '_lp_regular_price', true ),
			'sale'    => get_post_meta( $course_id, '_lp_sale_price', true ),
		);

		if ( 'regular' === $type || 'sale' === $type ) {
			return ! empty( $prices[ $type ] ) && $prices[ $type ] > 0 ? $prices[ $type ] : 0;
		}

		// Return sale price if available, otherwise fallback to regular price
		return ( ! empty( $prices['sale'] ) && $prices['sale'] > 0 ) ? $prices['sale'] : ( ! empty( $prices['regular'] ) && $prices['regular'] > 0 ? $prices['regular'] : 0 );
	}

	public function custom_course_price( $price, $course_id ) {

		if ( self::is_admin_dashboard() || empty( $price ) || ! is_numeric( $price ) ) {
			return $price;
		}

		$price = self::get_course_price( $course_id );
		$price = apply_filters( 'yay_currency_convert_price', $price, $this->apply_currency );
		$price = self::get_course_fixed_price( $price, $course_id );

		return $price;
	}

	public function custom_course_regular_price( $regular_price, $course_id ) {

		if ( self::is_admin_dashboard() || empty( $regular_price ) || ! is_numeric( $regular_price ) ) {
			return $regular_price;
		}

		$regular_price = self::get_course_price( $course_id, 'regular' );
		$regular_price = apply_filters( 'yay_currency_convert_price', $regular_price, $this->apply_currency );
		$regular_price = self::get_course_fixed_price( $regular_price, $course_id, 'regular_price' );

		return $regular_price;

	}

	public function learn_press_currency_symbol( $currency_symbol, $currency ) {

		if ( self::is_admin_dashboard() ) {
			return $currency_symbol;
		}

		if ( isset( $this->apply_currency['symbol'] ) && ! is_admin() ) {
			$currency_symbol = $this->apply_currency['symbol'];
		}

		return $currency_symbol;
	}

	// Archive Course page

	protected function archive_course_rest_route() {

		if ( isset( $GLOBALS['wp']->query_vars ) && isset( $GLOBALS['wp']->query_vars['course-item'] ) ) {
			return true;
		}

		$rest_route = CountryHelper::get_rest_route_via_rest_api();

		if ( $rest_route && str_contains( $rest_route, '/lp/v1/' ) ) {
			return true;
		}

		return false;
	}

	protected function is_archive_course() {
		$archive_course_ajax = LP_Settings_Courses::is_ajax_load_courses() && isset( $_REQUEST['lp-load-ajax'] ) && 'load_content_via_ajax' === $_REQUEST['lp-load-ajax'];
		return is_post_type_archive( 'lp_course' ) || $archive_course_ajax;
	}

	public function archive_course_price( $price, $course_id ) {
		if ( empty( $price ) || ! is_numeric( $price ) ) {
			return $price;
		}

		if ( self::is_admin_dashboard() ) {
			return $price;
		}

		if ( self::is_archive_course() ) {
			$price = apply_filters( 'yay_currency_convert_price', $price, $this->apply_currency );
			$price = self::get_course_fixed_price( $price, $course_id );
		}

		return $price;

	}

	public function archive_course_regular_price( $regular_price, $course_id ) {

		if ( empty( $regular_price ) || ! is_numeric( $regular_price ) ) {
			return $regular_price;
		}

		if ( self::is_admin_dashboard() ) {
			return $regular_price;
		}

		if ( self::is_archive_course() ) {
			$regular_price = apply_filters( 'yay_currency_convert_price', $regular_price, $this->apply_currency );
			$regular_price = self::get_course_fixed_price( $regular_price, $course_id, 'regular_price' );
		}

		return $regular_price;
	}

	public function archive_course_regular_price_html( $price, $course ) {
		if ( self::is_admin_dashboard() ) {
			return $price;
		}

		if ( self::archive_course_rest_route() || self::is_archive_course() || self::is_single_course_page() ) {
			$course_id     = is_object( $course ) ? $course->get_id() : $course;
			$regular_price = self::get_course_price( $course_id, 'regular' );
			$regular_price = YayCurrencyHelper::calculate_price_by_currency( $regular_price, false, $this->apply_currency );
			$regular_price = self::get_course_fixed_price( $regular_price, $course_id, 'regular_price' );
			$price         = YayCurrencyHelper::format_price( $regular_price );
		}

		return $price;
	}

	private function is_single_course_page() {
		return isset( $GLOBALS['wp']->query_vars ) && isset( $GLOBALS['wp']->query_vars['lp_course'] );
	}

	public function archive_course_price_html( $price, $has_sale_price, $course_id ) {

		if ( self::is_admin_dashboard() ) {
			return $price;
		}

		if ( self::archive_course_rest_route() || self::is_archive_course() || self::is_single_course_page() ) {
			$course = learn_press_get_course( $course_id );
			if ( $course ) {
				$price_html = '';
				if ( $has_sale_price ) {
					$price_html .= sprintf( '<span class="origin-price">%s</span>', self::archive_course_regular_price_html( $price, $course ) );
				}
				$final_price  = $course->get_price();
				$format_price = YayCurrencyHelper::format_price( $final_price );
				if ( self::is_single_course_page() ) {
					$format_price = '<span class="price">' . $format_price . '</span>';
				}
				$price = $price_html . $format_price;
			}
		}

		return $price;
	}

	public function cart_course_item_subtotal( $course_subtotal, $course, $quantity, $cart ) {
		if ( ! function_exists( 'learn_press_format_price' ) ) {
			return $course_subtotal;
		}

		$course_price    = apply_filters( 'yay_currency_convert_price', $course->get_price(), $this->apply_currency );
		$course_price    = self::get_course_fixed_price( $course_price, $course->get_id() );
		$course_subtotal = $course_price * $quantity;
		$course_subtotal = learn_press_format_price( $course_subtotal, true );

		return $course_subtotal;
	}

	// LearnPress - WooCommerce Payment Methods Integration

	public function get_woo_course_price_custom( $price, $course ) {
		if ( apply_filters( 'yay_currency_learn_press_default_course_price', false ) || YayCurrencyHelper::disable_fallback_option_in_checkout_page( $this->apply_currency ) ) {
			return $price;
		}
		$price = apply_filters( 'yay_currency_convert_price', $price, $this->apply_currency );
		return $price;
	}

	// Added Course Fixed Prices option for Each currency

	public function add_fixed_price_fields_to_pricing_tab() {
		global $product_object;
		require YAY_CURRENCY_PLUGIN_DIR . 'includes/templates/custom-fixed-prices/simple-product.php';
	}

	public function save_fixed_price_fields_custom( $post_id ) {
		if ( ! current_user_can( 'edit_post', $post_id ) || wp_is_post_autosave( $post_id ) || wp_is_post_revision( $post_id ) ) {
			return;
		}

		$nonce = isset( $_POST['yay-custom-fixed-prices-nonce'] ) ? sanitize_text_field( $_POST['yay-custom-fixed-prices-nonce'] ) : false;
		if ( ! $nonce && ! wp_verify_nonce( $nonce, 'yay-custom-fixed-prices-nonce' ) ) {
			return;
		}

		$currencies = Helper::get_currencies_post_type();

		$custom_fixed_prices = array();

		foreach ( $currencies as $currency ) {

			if ( Helper::default_currency_code() === $currency->post_title ) {
				continue;
			}

			$code = $currency->post_title;

			$regular_price = isset( $_POST[ 'regular_price_' . $code ] ) ? sanitize_text_field( $_POST[ 'regular_price_' . $code ] ) : null;

			$sale_price = isset( $_POST[ 'sale_price_' . $code ] ) ? sanitize_text_field( $_POST[ 'sale_price_' . $code ] ) : null;

			$custom_fixed_prices[ $currency->post_title ] = array(
				'regular_price' => $regular_price,
				'sale_price'    => $sale_price,
				'price'         => $sale_price ? $sale_price : $regular_price,
			);

		}

		update_post_meta( $post_id, 'yay_currency_custom_fixed_prices', $custom_fixed_prices );

	}
}
