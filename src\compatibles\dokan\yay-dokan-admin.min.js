!function (a) { "use strict"; var e = function () { var e = this; e.DokanPage = "admin.php?page=dokan#/", e.DokanLiteArgs = { dashboard: e.DokanPage, withdraw: e.<PERSON>kan<PERSON>age + "withdraw?status=pending", reverseWithdrawal: e.<PERSON> + "reverse-withdrawal", vendors: e.<PERSON>kan<PERSON> + "vendors" }, yay_dokan_admin_data.dokan_pro && (e.DokanLiteArgs.reports = e.DokanPage + "reports", e.DokanLiteArgs.reportsByDay = e.DokanPage + "reports?tab=report&type=by-day", e.DokanLiteArgs.reportsByVendor = e.<PERSON>kan<PERSON> + "reports?tab=report&type=by-vendor", e.DokanLiteArgs.reportsByYear = e.DokanPage + "reports?tab=report&type=by-year", e.DokanLiteArgs.refundPending = e.DokanPage + "refund?status=pending", e.DokanLiteArgs.refundApproved = e.<PERSON> + "refund?status=completed", e.DokanLiteArgs.refundCancelled = e.DokanPage + "refund?status=cancelled"), e.atAGalanceAreaDashBoard = ".postbox.dokan-postbox.dokan-status", e.atAGalanceSaleAreaDashBoard = e.atAGalanceAreaDashBoard + " li.sale a", e.atAGalanceCommissionAreaDashBoard = e.atAGalanceAreaDashBoard + " li.commission a", e.refundWrapperArea = ".dokan-refund-wrapper", e.refundArrays = ["refundPending", "refundApproved", "refundCancelled"], e.reportsLogsWrapperArea = ".reports-page .logs-area", e.tableReportsLogs = e.reportsLogsWrapperArea + " table.wp-list-table tbody tr.yay-currency-report-log", e.tooltipReportLogs = '<span style="cursor:pointer" title="Fee received by Seller" class="notication-tooltip yay-currency-reports-log-tooltip" data-original-title="Fee received by Seller">!</span>', e.init = function () { let t = window.location.href, r = t.replace(yay_dokan_admin_data.admin_url, ""), n = e.getKeyByValue(e.DokanLiteArgs, r); e.YayDokanAction(n), yay_dokan_admin_data.dokan_pro && a(document).on("ajaxComplete", function (a, t, r) { -1 != r.url.toLocaleLowerCase().indexOf("wp-json/dokan/v1/admin/logs") && e.YayDoKanReportsLogs() }), a(document).on("click", ".wp-submenu.wp-submenu-wrap li", function (t) { let r = a(this), n = r.find("a").attr("href"), o = e.getKeyByValue(e.DokanLiteArgs, n); e.YayDokanAction(o) }), a(document).on("click", ".report-area .dokan-report-sub li", function (t) { let r = a(this), n = e.DokanPage + r.find("a").attr("href").replace("#/", ""), o = e.getKeyByValue(e.DokanLiteArgs, n); e.YayDoKanReports(o, !0) }), a(document).on("click", ".reports-page .nav-tab-wrapper.woo-nav-tab-wrapper .nav-tab", function (t) { let r = a(this), n = "admin.php?page=dokan" + r.attr("href"), o = e.getKeyByValue(e.DokanLiteArgs, n); e.YayDoKanReports(o, !0) }), a(document).on("click", e.refundWrapperArea + " ul.subsubsub li", function (t) { let r = a(this), n = e.DokanPage + r.find("a").attr("href").replace("#/", ""), o = e.getKeyByValue(e.DokanLiteArgs, n); e.YayDokanRefund(o) }) }, e.YayDokanAction = function (a) { e.YayDoKanDashBoard(a), yay_dokan_admin_data.dokan_pro && (e.YayDoKanReports(a), e.YayDokanRefund(a)) }, e.getKeyByValue = function (a, e) { return Object.keys(a).find(t => a[t] === e) }, e.YayDoKanGetDataThisMonth = function () { a.ajax({ url: yay_dokan_admin_data.ajax_url, type: "POST", data: { action: "yay_dokan_admin_custom_dashboard", _nonce: yay_dokan_admin_data.nonce }, beforeSend: function (t) { a(e.atAGalanceAreaDashBoard).css("opacity", .2) }, success: function a(t) { t.success && t.data && t.data.report_data && e.customDokanAtAGalanceArea(t.data.report_data) } }) }, e.YayDoKanDashBoard = function (a) { a && "dashboard" === a && e.YayDoKanGetDataThisMonth() }, e.customDokanAtAGalanceArea = function (t, r = !1) { a(e.atAGalanceAreaDashBoard).css("opacity", .2), a(e.atAGalanceAreaDashBoard).css("opacity", .2); var n = setInterval(function () { if (a(e.atAGalanceSaleAreaDashBoard).length > 0 && clearInterval(n), a(e.atAGalanceAreaDashBoard).css("opacity", 1), a(e.atAGalanceSaleAreaDashBoard).length > 0) { let o = r ? t.sales.this_period : t.sales.this_month; a(e.atAGalanceSaleAreaDashBoard).find("strong").html(o), a(e.atAGalanceSaleAreaDashBoard).find(".up").length > 0 && a(e.atAGalanceSaleAreaDashBoard).find(".up").html(t.sales.parcent), a(e.atAGalanceSaleAreaDashBoard).find(".down").length > 0 && a(e.atAGalanceSaleAreaDashBoard).find(".down").html(t.sales.parcent) } if (a(e.atAGalanceCommissionAreaDashBoard).length > 0) { let s = r ? t.earning.this_period : t.earning.this_month; a(e.atAGalanceCommissionAreaDashBoard).find("strong").html(s), a(e.atAGalanceCommissionAreaDashBoard).find(".up").length > 0 && a(e.atAGalanceCommissionAreaDashBoard).find(".up").html(t.earning.parcent), a(e.atAGalanceCommissionAreaDashBoard).find(".down").length > 0 && a(e.atAGalanceCommissionAreaDashBoard).find(".down").html(t.earning.parcent) } }, 500) }, e.getMonthNumber = function (a) { return ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"].indexOf(a) + 1 }, e.convertDateRange = function (a) { let t = a.split(" - "), [r, n, o] = t[0].split(" "), [s, i, d] = t[1].split(" "); n = n.replace(",", ""), i = i.replace(",", ""); let l = `${o}-${String(e.getMonthNumber(r)).padStart(2, "0")}-${String(n).padStart(2, "0")}`, c = `${d}-${String(e.getMonthNumber(s)).padStart(2, "0")}-${String(i).padStart(2, "0")}`; return { from: l, to: c } }, e.changeDokanAtAGalanceAreaHTML = function (t) { let r = {}, n = ""; switch (t) { case "reportsByYear": let o = a(".form-inline.report-filter").find("select.dokan-input").val(); e.YayDoKanReportsByYear(o); break; case "reportsByVendor": if ((n = jQuery(".form-group .date-picker-input .reportrange-text")).length) { let s = n.text(); r = e.convertDateRange(s) } break; default: if ((n = jQuery(".form-group .date-picker-input .reportrange-text")).length) { let i = n.text(); r = e.convertDateRange(i) } }let d = a(".multiselect__option--selected").length > 0 ? a(".multiselect__option--selected").text() : ""; r && a.ajax({ url: yay_dokan_admin_data.ajax_url, type: "POST", data: { action: "yay_dokan_admin_custom_reports", from: r.from, to: r.to, seller_id: d, _nonce: yay_dokan_admin_data.nonce }, beforeSend: function (t) { a(e.atAGalanceAreaDashBoard).css("opacity", .2) }, success: function a(t) { t.success && t.data && t.data.report_data && e.customDokanAtAGalanceArea(t.data.report_data, !0) } }) }, e.YayDoKanReports = function (t, r = !1) { t && -1 != a.inArray(t, ["reports", "reportsByDay", "reportsByVendor", "reportsByYear"]) && (r ? e.changeDokanAtAGalanceAreaHTML(t) : e.YayDoKanGetDataThisMonth(), a(document).on("click", '.form-inline.report-filter button[type="submit"]', function (a) { e.changeDokanAtAGalanceAreaHTML(t) })) }, e.YayDoKanReportsByYear = function (t) { a.ajax({ url: yay_dokan_admin_data.ajax_url, type: "POST", data: { action: "yay_dokan_admin_reports_by_year", _year: t, _nonce: yay_dokan_admin_data.nonce }, beforeSend: function (t) { a(e.atAGalanceAreaDashBoard).css("opacity", .2) }, success: function a(t) { t.success && t.data && t.data.report_data && e.customDokanAtAGalanceArea(t.data.report_data, !0) } }) }, e.YayDoKanReportsLogs = function () { var t = setInterval(function () { if (!a(".logs-area .table-loading .table-loader").length) { let r = a(".reports-page .logs-area table.wp-list-table tbody tr td.order_id"); if (r.length > 0) { let n = []; r.each(function (e) { let t = a(this).find("a").text().replace("#", ""); a(this).closest("tr").addClass("yay-currency-report-log").attr("data-order_id", t), n.push(t) }); let o = a(location).attr("href"), s = {};["vendor_id", "order_status", "start_date", "end_date", "order_id", "page"].forEach(function (a) { s[a] = e.getValueinParam(a, o) ? e.getValueinParam(a, o) : "" }), a.ajax({ url: yay_dokan_admin_data.ajax_url, type: "POST", data: { action: "yay_dokan_admin_custom_reports_logs", reportArgs: s, _nonce: yay_dokan_admin_data.nonce }, beforeSend: function (t) { a(e.reportsLogsWrapperArea).css("opacity", .2) }, success: function t(r) { a(e.reportsLogsWrapperArea).css("opacity", 1), r.success && r.data.reports_logs && a(e.tableReportsLogs).each(function (e) { let t = +a(this).find("td.order_id").text().replace("#", ""); r.data.reports_logs[t] && (a(this).find("td.order_total").html(r.data.reports_logs[t].order_total), a(this).find("td.vendor_earning").html(r.data.reports_logs[t].vendor_earning), a(this).find("td.commission").html(r.data.reports_logs[t].commission), a(this).find("td.dokan_gateway_fee").html(r.data.reports_logs[t].dokan_gateway_fee), a(this).find("td.shipping_total div").html(r.data.reports_logs[t].shipping_total), a(this).find("td.shipping_total_tax div").html(r.data.reports_logs[t].shipping_total_tax), a(this).find("td.tax_total div").html(r.data.reports_logs[t].tax_total)) }) } }) } clearInterval(t) } }, 500) }, e.YayDokanRefund = function (t) { if (e.refundArrays.includes(t)) var r = setInterval(function () { a(".table-loading .table-loader").length || clearInterval(r); let n = a(".dokan-refund-wrapper table.wp-list-table tbody tr td.order_id"); if (n.length > 0) { let o = []; n.each(function (e) { let t = a(this).find("a strong").text().replace("#", ""); a(this).closest("tr").find("td.amount").attr("data-order_id", t), o.push(t) }); let s = 0; "refundApproved" === t ? s = 1 : "refundCancelled" === t && (s = 2), a.ajax({ url: yay_dokan_admin_data.ajax_url, type: "POST", data: { action: "yay_dokan_admin_custom_refund_request", orderIds: o, status: s, _nonce: yay_dokan_admin_data.nonce }, beforeSend: function (t) { a(e.refundWrapperArea).css("opacity", .2) }, success: function t(r) { a(e.refundWrapperArea).css("opacity", 1), r.success && r.data.refunds && a(".dokan-refund-wrapper table.wp-list-table tbody tr td.amount").each(function (e) { a(this).html(r.data.refunds[a(this).data("order_id")]) }) } }) } }, 500) }, e.getValueinParam = function (a, e) { let t = new URLSearchParams(e); return t.get(a) } }; jQuery(document).ready(function (a) { new e().init() }) }(jQuery);