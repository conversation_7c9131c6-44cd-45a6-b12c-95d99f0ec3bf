<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\FixedPriceHelper;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;

// Link plugin: https://woocommerce.com/products/product-bundles/

class WooCommerceProductBundles {
	use SingletonTrait;

	private $apply_currency = array();

	public function __construct() {

		if ( ! class_exists( 'WC_Bundles' ) ) {
			return;
		}

		$this->apply_currency = YayCurrencyHelper::detect_current_currency();

		add_filter( 'woocommerce_cart_item_subtotal', array( $this, 'woocommerce_cart_item_subtotal' ), 20, 3 );
		add_filter( 'woocommerce_checkout_item_subtotal', array( $this, 'woocommerce_cart_item_subtotal' ), 20, 3 );

		add_filter( 'woocommerce_bundle_container_cart_item', array( $this, 'woocommerce_bundle_container_cart_item' ), 20, 2 );
		add_filter( 'woocommerce_bundled_cart_item', array( $this, 'woocommerce_bundled_cart_item' ), 20, 2 );
		add_filter( 'yay_currency_checkout_converted_product_subtotal_fixed', array( $this, 'checkout_converted_product_subtotal_fixed' ), 20, 4 );
		add_filter( 'yay_product_bundles_get_product_subtotal', array( $this, 'yay_get_product_subtotal' ), 10, 3 );
		add_filter( 'yay_product_bundles_get_cart_subtotal', array( $this, 'yay_get_cart_subtotal' ), 10, 2 );

		add_filter( 'yay_currency_get_product_price_by_cart_item', array( $this, 'get_product_price_by_cart_item' ), 10, 3 );
		add_filter( 'yay_currency_get_product_price_default_by_cart_item', array( $this, 'get_product_price_default_by_cart_item' ), 10, 2 );

		add_filter( 'yay_currency_is_change_format_order_line_subtotal', array( $this, 'is_change_format_order_line_subtotal' ), 10, 4 );

	}

	public function get_container_cart_item_subtotal( $subtotal, $cart_item, $cart_item_key, $wc_pb_display ) {
		if ( ! class_exists( 'WC_Product_Bundle' ) || ! class_exists( 'WC_PB_Product_Prices' ) ) {
			return $subtotal;
		}
		$aggregate_subtotals = \WC_Product_Bundle::group_mode_has( $cart_item['data']->get_group_mode(), 'aggregated_subtotals' );

		if ( $aggregate_subtotals ) {

			$calc_type                             = ! $wc_pb_display->display_cart_prices_including_tax() ? 'excl_tax' : 'incl_tax';
			$bundle_price                          = \WC_PB_Product_Prices::get_product_price(
				$cart_item['data'],
				array(
					'price' => $cart_item['data']->get_price(),
					'calc'  => $calc_type,
					'qty'   => $cart_item['quantity'],
				)
			);
			$bundled_cart_items                    = wc_pb_get_bundled_cart_items( $cart_item, WC()->cart->cart_contents );
			$bundled_items_price                   = 0.0;
			$bundle_subtotal_cart_item_by_currency = 0;
			foreach ( $bundled_cart_items as $bundled_cart_item ) {

				$bundled_item_id        = $bundled_cart_item['bundled_item_id'];
				$bundled_item_raw_price = $bundled_cart_item['data']->get_price();

				if ( class_exists( 'WC_Subscriptions_Product' ) && WC_PB()->compatibility->is_subscription( $bundled_cart_item['data'] ) && ! WC_PB()->compatibility->is_subscription( $cart_item['data'] ) ) {

					$bundled_item = $cart_item['data']->get_bundled_item( $bundled_item_id );

					if ( $bundled_item ) {
						$bundled_item_raw_recurring_fee = $bundled_cart_item['data']->get_price();
						$bundled_item_raw_sign_up_fee   = (float) \WC_Subscriptions_Product::get_sign_up_fee( $bundled_cart_item['data'] );
						$bundled_item_raw_price         = $bundled_item->get_up_front_subscription_price( $bundled_item_raw_recurring_fee, $bundled_item_raw_sign_up_fee, $bundled_cart_item['data'] );
					}
				}

				$bundled_item_price                     = \WC_PB_Product_Prices::get_product_price(
					$bundled_cart_item['data'],
					array(
						'price' => $bundled_item_raw_price,
						'calc'  => $calc_type,
						'qty'   => $bundled_cart_item['quantity'],
					)
				);
				$bundle_subtotal_cart_item_by_currency += isset( $bundled_cart_item['product_bunlde_discount_by_currency'] ) ? $bundled_cart_item['product_bunlde_discount_by_currency'] * $bundled_cart_item['quantity'] : 0;
				$bundled_items_price                   += wc_format_decimal( (float) $bundled_item_price, wc_pb_price_num_decimals() );
			}
			$product_subtotal = (float) $bundle_price + $bundled_items_price;
			$subtotal         = $wc_pb_display->format_subtotal( $cart_item['data'], $product_subtotal );
			if ( YayCurrencyHelper::disable_fallback_option_in_checkout_page( $this->apply_currency ) && 1 !== floatval( $this->apply_currency['rate'] ) ) {
				$bundle_subtotal_cart_item_by_currency += isset( $cart_item['product_bunle_container_by_currency'] ) ? $cart_item['product_bunle_container_by_currency'] : 0;
				$converted_subtotal                     = YayCurrencyHelper::calculate_custom_price_by_currency_html( $this->apply_currency, $bundle_subtotal_cart_item_by_currency );
				$converted_subtotal_html                = YayCurrencyHelper::converted_approximately_html( $converted_subtotal );
				$subtotal                              .= $converted_subtotal_html;
			}
		} elseif ( empty( $cart_item['line_subtotal'] ) ) {
			$hide_container_zero_subtotal = \WC_Product_Bundle::group_mode_has( $cart_item['data']->get_group_mode(), 'component_multiselect' );
			$subtotal                     = $hide_container_zero_subtotal ? '' : $subtotal;
		}

		return $subtotal;
	}

	public function woocommerce_cart_item_subtotal( $subtotal, $cart_item, $cart_item_key ) {
		if ( class_exists( 'WC_Bundles' ) && class_exists( 'WC_PB_Display' ) ) {
			$wc_pb_display = \WC_PB_Display::instance();
			if ( wc_pb_is_bundled_cart_item( $cart_item ) ) {
				$subtotal = $wc_pb_display->get_child_cart_item_subtotal( $subtotal, $cart_item, $cart_item_key );
			} elseif ( wc_pb_is_bundle_container_cart_item( $cart_item ) ) {
				$subtotal = $this->get_container_cart_item_subtotal( $subtotal, $cart_item, $cart_item_key, $wc_pb_display );
			}
		}
		return $subtotal;
	}

	public function woocommerce_bundle_container_cart_item( $cart_item, $bundle ) {

		if ( isset( $cart_item['stamp'] ) ) {
			$product_bunle_container_id    = Helper::get_value_variable( $cart_item['variation_id'], $cart_item['product_id'] );
			$product_bunle_container_obj   = $cart_item['data'];
			$price_original                = SupportHelper::get_product_price( $product_bunle_container_id );
			$price_convert_currency        = YayCurrencyHelper::calculate_price_by_currency( $price_original, false, $this->apply_currency );
			$product_bunle_container_price = FixedPriceHelper::get_price_fixed_by_apply_currency( $cart_item['data'], $price_convert_currency, $this->apply_currency );
			$product_bunle_container_obj->product_bunle_container_by_currency = $product_bunle_container_price;
			$product_bunle_container_obj->product_bunle_container_by_default  = $price_original;
			$product_bunle_container_obj->is_product_bunle_container          = true;
			$cart_item['product_bunle_container_by_currency']                 = $product_bunle_container_price;
			$cart_item['product_bunle_container_by_default']                  = $price_original;
			$cart_item['is_product_bunle_container']                          = true;
		}

		return $cart_item;
	}

	public function woocommerce_bundled_cart_item( $cart_item, $bundle ) {
		$bundle_stamp_by_cart_item = Helper::get_value_variable( $cart_item['stamp'] );
		if ( $bundle_stamp_by_cart_item ) {
			$product_obj                      = $cart_item['data'];
			$variation_id                     = Helper::get_value_variable( $cart_item['variation_id'] );
			$product_id                       = Helper::get_value_variable( $cart_item['product_id'] );
			$filtered_product_bundle_selected = array_filter(
				$bundle_stamp_by_cart_item,
				function ( $bundle_item ) use ( $variation_id, $product_id ) {
					if ( ! isset( $bundle_item['variation_id'] ) && ! $variation_id ) {
						if ( $bundle_item['product_id'] === $product_id ) {
							return true;
						}
					} elseif ( $bundle_item['product_id'] === $product_id && isset( $bundle_item['variation_id'] ) && intval( $bundle_item['variation_id'] ) === $variation_id ) {
							return true;
					}

					return false;
				}
			);
			if ( $filtered_product_bundle_selected ) {
				if ( ! empty( $cart_item['line_subtotal'] ) ) {
					$product_bundle_settings = array_shift( $filtered_product_bundle_selected );
					$product_bundle_id       = isset( $product_bundle_settings['variation_id'] ) ? intval( $product_bundle_settings['variation_id'] ) : $product_bundle_settings['product_id'];
					$price_original          = SupportHelper::get_product_price( $product_bundle_id );
					$price_convert_currency  = YayCurrencyHelper::calculate_price_by_currency( $price_original, false, $this->apply_currency );
					$product_bundle_price    = FixedPriceHelper::get_price_fixed_by_apply_currency( $cart_item['data'], $price_convert_currency, $this->apply_currency );

					$discount_value                   = Helper::get_value_variable( $product_bundle_settings['discount'] );
					$product_bunlde_discount          = $discount_value ? $product_bundle_price - ( $discount_value / 100 ) * $product_bundle_price : $product_bundle_price;
					$product_bundle_original_discount = $discount_value ? $price_original - ( $discount_value / 100 ) * $price_original : $price_original;
				} else {
					$product_bunlde_discount          = 0;
					$product_bundle_original_discount = 0;
				}

				$product_obj->product_bunlde_discount_by_currency = $product_bunlde_discount;
				$product_obj->product_bunlde_discount_by_default  = $product_bundle_original_discount;
				$cart_item['product_bunlde_discount_by_currency'] = $product_bunlde_discount;
				$cart_item['product_bunlde_discount_by_default']  = $product_bundle_original_discount;
			}
		}

		return $cart_item;
	}

	public function yay_get_product_subtotal( $subtotal, $cart_item, $quantity ) {
		if ( isset( $cart_item['stamp'] ) ) {
			if ( isset( $cart_item['is_product_bunle_container'] ) && isset( $cart_item['product_bunle_container_by_currency'] ) ) {
				$subtotal = $cart_item['product_bunle_container_by_currency'] * $quantity;
			} elseif ( isset( $cart_item['product_bunlde_discount_by_currency'] ) ) {
				$subtotal = $cart_item['product_bunlde_discount_by_currency'] * $quantity;
			}
		}
		return $subtotal;
	}

	public function checkout_converted_product_subtotal_fixed( $converted_product_subtotal, $product, $apply_currency, $quantity ) {
		if ( class_exists( 'WC_PB_Display' ) ) {
			remove_filter( 'woocommerce_cart_item_subtotal', array( \WC_PB_Display::instance(), 'cart_item_subtotal' ), 10, 3 );
		}
		if ( isset( $product->product_bunlde_discount_by_currency ) ) {
			$product_price_result       = $product->product_bunlde_discount_by_currency * $quantity;
			$converted_product_subtotal = YayCurrencyHelper::calculate_custom_price_by_currency_html( $apply_currency, $product_price_result );
			return $converted_product_subtotal;
		}

		return $converted_product_subtotal;
	}

	public function yay_get_cart_subtotal( $subtotal_price_fixed, $apply_currency ) {
		$subtotal = 0;
		foreach ( WC()->cart->get_cart_contents()  as $cart_item ) {
			$quantity = $cart_item['quantity'];
			if ( isset( $cart_item['stamp'] ) ) {
				if ( isset( $cart_item['is_product_bunle_container'] ) && isset( $cart_item['product_bunle_container_by_currency'] ) ) {
					$subtotal = $subtotal + $cart_item['product_bunle_container_by_currency'] * $quantity;
				} elseif ( $cart_item['product_bunlde_discount_by_currency'] ) {
					$subtotal = $subtotal + $cart_item['product_bunlde_discount_by_currency'] * $quantity;
				}
			} else {
				$product_fixed = FixedPriceHelper::product_is_set_fixed_price_by_currency( $cart_item['data'], $this->apply_currency );
				if ( $product_fixed ) {
					$subtotal = $subtotal + $product_fixed * $quantity;
				} else {
					$subtotal = $subtotal + YayCurrencyHelper::calculate_price_by_currency( $cart_item['line_subtotal'], false, $apply_currency );
				}
			}
		}
		if ( $subtotal ) {
			return $subtotal;
		}
		return $subtotal_price_fixed;
	}

	public function recalculate_cart_subtotal( $subtotal, $apply_currency ) {
		$subtotal = apply_filters( 'yay_product_bundles_get_cart_subtotal', 0, $apply_currency );
		return $subtotal;
	}

	public function get_product_bunder_subtotal( $cart_item ) {
		$product_subtotal   = 0;
		$bundled_cart_items = wc_pb_get_bundled_cart_items( $cart_item, WC()->cart->cart_contents );
		foreach ( $bundled_cart_items as $bundled_cart_item ) {
			if ( isset( $bundled_cart_item['is_product_bunle_container'] ) && isset( $bundled_cart_item['product_bunle_container_by_currency'] ) ) {
				$product_subtotal += $bundled_cart_item['product_bunle_container_by_currency'];
			} elseif ( isset( $bundled_cart_item['product_bunlde_discount_by_currency'] ) ) {
				$product_subtotal += $bundled_cart_item['product_bunlde_discount_by_currency'];
			}
		}

		return $product_subtotal;
	}

	public function get_product_price_by_cart_item( $price, $cart_item, $apply_currency ) {
		if ( isset( $cart_item['stamp'] ) ) {
			if ( isset( $cart_item['is_product_bunle_container'] ) && isset( $cart_item['product_bunle_container_by_currency'] ) ) {
				$price = $cart_item['product_bunle_container_by_currency'];
			} elseif ( isset( $cart_item['product_bunlde_discount_by_currency'] ) ) {
				$price = $cart_item['product_bunlde_discount_by_currency'];
			}
		}
		return $price;
	}

	public function get_product_price_default_by_cart_item( $price, $cart_item ) {

		if ( isset( $cart_item['stamp'] ) ) {
			if ( isset( $cart_item['is_product_bunle_container'] ) && isset( $cart_item['product_bunle_container_by_default'] ) ) {
				$price = $cart_item['product_bunle_container_by_default'];
			} elseif ( isset( $cart_item['product_bunlde_discount_by_default'] ) ) {
				$price = $cart_item['product_bunlde_discount_by_default'];
			}
		}

		return $price;
	}

	public function get_fixed_tax_product_subtotal_3rd_plugin( $fixed_tax_product_subtotal, $cart_item, $fixed_product_price, $quantity, $apply_currency ) {
		$fixed_tax_product_subtotal = apply_filters( 'yay_product_bundles_get_product_subtotal', $fixed_tax_product_subtotal, $cart_item, $quantity );
		return $fixed_tax_product_subtotal;
	}

	public function is_change_format_order_line_subtotal( $flag, $subtotal, $item, $order ) {

		if ( function_exists( 'wc_pb_is_bundle_container_order_item' ) && wc_pb_is_bundle_container_order_item( $item ) ) {
			$flag = false;
		}

		return $flag;

	}
}
