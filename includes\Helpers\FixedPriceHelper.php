<?php
namespace Yay_Currency\Helpers;

use Yay_Currency\Utils\SingletonTrait;
class FixedPriceHelper {

	use SingletonTrait;

	protected function __construct() {}

	public static function is_set_fixed_price() {
		$flag = get_option( 'yay_currency_set_fixed_price', 0 );
		return $flag;
	}

	public static function get_custom_fixed_prices( $product_id, $currency_code ) {
		$custom_fixed_prices = get_post_meta( $product_id, 'yay_currency_custom_fixed_prices', true );
		$custom_fixed_prices = isset( $custom_fixed_prices[ $currency_code ] ) && ! empty( $custom_fixed_prices[ $currency_code ] ) ? $custom_fixed_prices[ $currency_code ] : false;
		return $custom_fixed_prices;
	}

	public static function get_regular_price( $custom_fixed_prices, $default_price ) {
		$regular_price = isset( $custom_fixed_prices['regular_price'] ) && ! empty( $custom_fixed_prices['regular_price'] ) ? $custom_fixed_prices['regular_price'] : $default_price;
		return $regular_price;
	}

	public static function get_sale_price( $custom_fixed_prices, $default_price ) {
		$sale_price = isset( $custom_fixed_prices['sale_price'] ) && ! empty( $custom_fixed_prices['sale_price'] ) ? $custom_fixed_prices['sale_price'] : $default_price;
		return $sale_price;
	}

	public static function get_price_fixed_by_apply_currency( $product, $price, $apply_currency ) {
		if ( ! self::is_set_fixed_price() || ! $apply_currency ) {
			return $price;
		}

		$custom_fixed_prices = self::get_custom_fixed_prices( $product->get_id(), $apply_currency['currency'] );

		if ( $custom_fixed_prices ) {
			$regular_price = self::get_regular_price( $custom_fixed_prices, $price );
			if ( doing_filter( 'woocommerce_product_get_regular_price' ) || doing_filter( 'woocommerce_product_variation_get_regular_price' ) || doing_filter( 'woocommerce_variation_prices_regular_price' ) ) {
				return $regular_price;
			}
			// Return sale price if product is on sale, otherwise return regular price
			if ( $product->is_on_sale( 'edit' ) ) {
				return self::get_sale_price( $custom_fixed_prices, $price );
			} else {
				return $regular_price;
			}
		}

		return $price;
	}

	public static function get_fixed_regular_sale_price_by_apply_currency( $product, $regular_sale_price, $type = 'regular', $apply_currency = array() ) {
		if ( ! self::is_set_fixed_price() || ! $apply_currency ) {
			return $regular_sale_price;
		}

		$custom_fixed_prices = self::get_custom_fixed_prices( $product->get_id(), $apply_currency['currency'] );

		if ( $custom_fixed_prices ) {
			$regular_price = self::get_regular_price( $custom_fixed_prices, $regular_sale_price );
			if ( 'regular' === $type ) {
				return $regular_price;
			} elseif ( $product->is_on_sale( 'edit' ) ) {
				return self::get_sale_price( $custom_fixed_prices, $regular_sale_price );
			} else {
				return $regular_price;
			}
		}

		return $regular_sale_price;
	}

	public static function product_is_set_fixed_price_by_currency( $product, $apply_currency ) {
		$fixed_price = self::is_set_fixed_price() ? self::get_price_fixed_by_apply_currency( $product, false, $apply_currency ) : false;
		return $fixed_price;
	}

	public static function has_fixed_price_product_in_cart( $apply_currency = array() ) {
		if ( ! $apply_currency ) {
			return false;
		}
		$cart_contents = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $cart_item ) {
			$product_id = ! empty( $cart_item['variation_id'] ) ? $cart_item['variation_id'] : $cart_item['product_id'];
			$_product   = wc_get_product( $product_id );
			if ( self::product_is_set_fixed_price_by_currency( $_product, $apply_currency ) ) {
				return true;
			}
		}
		return false;
	}

	public static function calculate_discounted_fixed_cart_subtotal( $apply_currency, $coupon_amount ) {
		$fixed_cart_subtotal = 0;
		$cart_contents       = WC()->cart->get_cart_contents();
		foreach ( $cart_contents  as $cart_item ) {
			$product_id           = ! empty( $cart_item['variation_id'] ) ? $cart_item['variation_id'] : $cart_item['product_id'];
			$_product             = wc_get_product( $product_id );
			$fixed_price          = self::product_is_set_fixed_price_by_currency( $_product, $apply_currency );
			$item_subtotal        = $fixed_price && is_numeric( $fixed_price ) ? $fixed_price * $cart_item['quantity'] : YayCurrencyHelper::calculate_price_by_currency( $coupon_amount, true, $apply_currency ) * $cart_item['quantity'];
			$fixed_cart_subtotal += $item_subtotal;
		}
		return $fixed_cart_subtotal;
	}
}
