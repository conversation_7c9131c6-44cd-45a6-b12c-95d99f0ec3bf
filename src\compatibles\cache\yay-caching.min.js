!function (a) { "use strict"; var c = function () { var c = this; c.currencyParamInput = 'input[name="yay_currency_current_url"]', c.oldCurrency = YayCurrency_Callback.Helper.getCookie(window.yayCurrency.cookie_name), c.init = function () { c.customCachingCompatibles(), a(document).on("append.infiniteScroll", (() => { c.customCachingCompatibles() })) }, c.detectAllowCaching = function () { if ("1" === yayCurrency.cart_page || "1" === yayCurrency.hide_dropdown_switcher || "1" === yayCurrency.checkout_page) return !1; if ("undefined" != typeof wc_checkout_params && parseInt(wc_checkout_params.is_checkout)) return !1; return YayCurrency_Callback.Helper.detectAllowCaching(!0) }, c.getCachingAjaxUrl = function () { let a = yay_currency_caching_data.ajax_url; return yay_currency_caching_data.gtranslate_active && (a = document.location.origin + "/wp-admin/admin-ajax.php"), a }, c.customCachingCompatibles = function () { if (!c.detectAllowCaching()) return c.resetCachingLoading(), void c.resetProductCacheLoading(); const e = YayCurrency_Callback.Helper.getListProductIdsAvailable(); e.length ? a.ajax({ url: c.getCachingAjaxUrl(), type: "POST", data: c.getListDataCaching(!0, e), beforeSend: function (a) { c.customBeforeSendCaching(a) }, xhrFields: { withCredentials: !0 }, success: function (a) { c.customResponseCaching(a, !0) }, error: function (a, e, r) { console.log("Error responseText: ", a.responseText), c.resetCachingLoading(), c.resetProductCacheLoading() } }) : c.customCachingSwitcher() }, c.customCachingSwitcher = function () { a.ajax({ url: yay_currency_caching_data.ajax_url, type: "POST", data: c.getListDataCaching(), beforeSend: function (a) { c.customBeforeSendCaching(a) }, xhrFields: { withCredentials: !0 }, success: function (a) { c.customResponseCaching(a) }, error: function (a, e, r) { console.log("Error responseText: ", a.responseText), c.resetCachingLoading(), c.resetProductCacheLoading() } }) }, c.getListDataCaching = function (a = !1, e = []) { let r = YayCurrency_Callback.Helper.getListDataCaching(a, e); return a && yay_currency_caching_data.product_id && yay_currency_caching_data.product_is_variable && (r.product_variable_id = yay_currency_caching_data.product_id), r = c.getDataCaching3rdPlugins(r), r }, c.customBeforeSendCaching = function (e) { "1" === yayCurrency.cache_compatible && (yay_currency_caching_data.is_loading_mark && a(yay_currency_data_args.caching_data_args.yayCurrencyMiniCartContents).addClass(yay_currency_data_args.common_data_args.yayCurrencyLoading), c.customBeforeSendCaching3rdPlugins(e)) }, c.customResponseCaching = function (e, r = !1) { if (c.resetCachingLoading(), e.success) { if ("no" === yay_currency_caching_data.is_switch_currency && a(document.body).trigger("wc_fragment_refresh"), e.data.product_variable_should_not_render_html) { const c = yay_currency_caching_data.product_single_variation_wrap_class ?? ".single_variation_wrap"; a(c).addClass("yay-caching-hide-variation-render-html") } e.data.current_currency_id && +c.oldCurrency != e.data.current_currency_id && YayCurrency_Callback.Helper.setCookie(yayCurrency.cookie_name, e.data.current_currency_id, +yayCurrency.cookie_lifetime_days), YayCurrency_Callback.Helper.customResponseCommon(e), e.data.product_content && a(yay_currency_data_args.caching_data_args.yayCurrencyProduct).html(e.data.product_content), a(c.currencyParamInput).val(yay_currency_caching_data.yay_currency_current_url), c.customResponseProductCaching(e, r), c.customResponseCaching3rdPlugins(e) } else c.resetProductCacheLoading() }, c.customResponseProductCaching = function (e, r = !1) { if (r) { if (e.data.price_html) { const c = e.data.price_html; for (let e in c) { const r = `.yay-currency-cache-product-id[data-yay_currency-product-id=${e}]`, n = c[e], t = a(r).closest(".price"); yay_currency_caching_data.product_html_caching_enable && t.length ? t.html(n) : a(r).replaceWith(n) } a(yay_currency_data_args.caching_data_args.yayVariationId).each(((e, r) => { let n = a(r).data("product_variations"); n && (n.map((a => { let e = a.variation_id; return a.price_html = c[e], a })), a(r).data("product_variations", n)) })) } c.resetProductCacheLoading() } }, c.resetProductCacheLoading = function () { a(yay_currency_data_args.caching_data_args.yayProductId).length && a(yay_currency_data_args.caching_data_args.yayProductId).addClass("price").removeClass("yay-currency-cache-product-id yay-currency-cache-loading").removeAttr("data-yay_currency-product-id") }, c.resetCachingLoading = function () { YayCurrency_Callback.Helper.resetCachingLoading(), c.resetCachingLoading3rdPlugins() }, c.getDataCaching3rdPlugins = function (a) { return yayCurrencyHooks.applyFilters("yayCurrencyGetDataCaching", a) }, c.customBeforeSendCaching3rdPlugins = function (a) { yayCurrencyHooks.doAction("yayCurrencyBeforeSendCaching", [{ response: a }]) }, c.customResponseCaching3rdPlugins = function (a) { yayCurrencyHooks.doAction("yayCurrencyResponseCaching", [{ response: a }]) }, c.resetCachingLoading3rdPlugins = function () { yayCurrencyHooks.doAction("yayCurrencyResetLoadingCaching", []) } }; jQuery(document).ready((function (a) { (new c).init() })) }(jQuery);