<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;
use Yay_Currency\Helpers\YayCurrencyHelper;

defined( 'ABSPATH' ) || exit;

// Link plugin: https://wordpress.org/plugins/pymntpl-paypal-woocommerce/

class PaymentPluginsForPayPalWooCommerce {

	use SingletonTrait;

	private $apply_currency   = array();
	private $default_currency = '';
	private $is_dis_checkout_diff_currency;

	public function __construct() {

		if ( ! class_exists( '\PaymentPlugins\WooCommerce\PPCP\Main' ) ) {
			return;
		}

		$this->apply_currency                = YayCurrencyHelper::detect_current_currency();
		$this->default_currency              = Helper::default_currency_code();
		$this->is_dis_checkout_diff_currency = YayCurrencyHelper::is_dis_checkout_diff_currency( $this->apply_currency );

		if ( $this->is_dis_checkout_diff_currency ) {
			// Keep original Price & Shipping
			add_filter( 'yay_currency_get_original_shipping_cost', array( $this, 'yay_currency_get_original_shipping_cost' ), 20, 2 ); // caculate when use fallback currency differrent Default currency
			add_filter( 'yay_currency_get_price_default_in_checkout_page', array( $this, 'yay_currency_get_price_default_in_checkout_page' ), 10, 2 );
		}
		add_filter( 'yay_currency_woocommerce_currency', array( $this, 'custom_currency_paypal_method' ), 10, 2 );
	}

	public function yay_currency_get_price_default_in_checkout_page( $price, $product ) {

		if ( wp_doing_ajax() && isset( $_REQUEST['wc-ajax'] ) && 'wc_ppcp_frontend_request' === $_REQUEST['wc-ajax'] ) {
			$fallback_currency = YayCurrencyHelper::get_fallback_currency( YayCurrencyHelper::converted_currency() );
			if ( $fallback_currency && $fallback_currency['currency'] !== $this->default_currency ) {
				$price = $price * YayCurrencyHelper::get_rate_fee( $fallback_currency );
			}
		}

		return $price;
	}

	public function yay_currency_get_original_shipping_cost( $cost, $apply_currency ) {

		if ( wp_doing_ajax() && isset( $_REQUEST['wc-ajax'] ) && 'wc_ppcp_frontend_request' === $_REQUEST['wc-ajax'] ) {
			$fallback_currency = YayCurrencyHelper::get_fallback_currency( YayCurrencyHelper::converted_currency() );
			if ( is_numeric( $cost ) && $fallback_currency && $fallback_currency['currency'] !== $this->default_currency ) {
				$cost = $cost * YayCurrencyHelper::get_rate_fee( $fallback_currency );
			}
		}

		return $cost;
	}

	public function custom_currency_paypal_method( $currency, $is_dis_checkout_diff_currency ) {

		if ( $is_dis_checkout_diff_currency ) {
			$fallback_currency     = YayCurrencyHelper::get_fallback_currency( YayCurrencyHelper::converted_currency() );
			$default_currency_code = Helper::default_currency_code();
			$currency              = isset( $fallback_currency ) && $fallback_currency['currency'] !== $default_currency_code ? $fallback_currency['currency'] : $default_currency_code;
		}

		return $currency;

	}
}
