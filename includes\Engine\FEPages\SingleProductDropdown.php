<?php
namespace Yay_Currency\Engine\FEPages;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\Helper;

defined( 'ABSPATH' ) || exit;

class SingleProductDropdown {

	use SingletonTrait;

	public $apply_currencies = array();

	public $all_currencies = array();

	protected function __construct() {

		$is_show_on_single_product_page = get_option( 'yay_currency_show_single_product_page', 1 );

		if ( $is_show_on_single_product_page ) {
			$switcherPositionOnSingleProductPage = get_option( 'yay_currency_switcher_position_on_single_product_page', 'before_description' );
			if ( 'after_description' === $switcherPositionOnSingleProductPage ) {
				add_action( 'woocommerce_before_add_to_cart_form', array( $this, 'dropdown_price_in_different_currency' ) );
			} else {
				add_action( 'woocommerce_single_product_summary', array( $this, 'dropdown_price_in_different_currency' ) );
			}
		}
	}

	public function dropdown_price_in_different_currency() {
		$show_notice = get_option( 'yay_currency_setting_show_notice' );
		if ( $show_notice && ( 'true' === $show_notice || 0 !== intval( $show_notice ) ) ) {
			echo do_shortcode( '[yaycurrency-country-currency-notice]' );
		}

		Helper::currency_switcher_html( 'product' );

	}
}
