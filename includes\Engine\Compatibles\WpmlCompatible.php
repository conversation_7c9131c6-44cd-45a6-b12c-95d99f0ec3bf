<?php
namespace Yay_Currency\Engine\Compatibles;

use Yay_Currency\Utils\SingletonTrait;
use Yay_Currency\Helpers\YayCurrencyHelper;
use Yay_Currency\Helpers\SupportHelper;

defined( 'ABSPATH' ) || exit;

class WpmlCompatible {

	use SingletonTrait;

	private $language;
	private $currencies_by_languages;

	public function __construct() {
		global $sitepress;
		if ( ! $sitepress || null === $sitepress || 'SitePress' !== get_class( $sitepress ) ) {
			return;
		}

		add_filter( 'yay_currency_multiple_language_active', '__return_true' );

		add_filter( 'yay_currency_get_apply_currency_by_language_3rd_plugin', array( $this, 'get_apply_currency_by_language_3rd_plugin' ), 10, 2 );

		add_filter( 'yay_currency_send_data_settings_args', array( $this, 'get_wpml_data' ), 10, 1 );
		// Support Caching
		add_filter( 'yay_currency_get_current_currency_by_caching_with_multiple_language', array( $this, 'get_current_currency_by_caching_with_multiple_language' ), 10, 2 );
	}

	public function get_apply_currency_by_language_3rd_plugin( $apply_currency, $converted_currency ) {
		global $sitepress;

		if ( 0 === intval( get_option( 'yay_currency_wpml_compatible', 0 ) ) ) {
			return $apply_currency;
		}

		$currencies_by_languages = get_option( 'yay_currency_currencies_by_languages_wpml', array() );
		$selected_language       = $sitepress->get_current_language();
		$apply_currency          = SupportHelper::get_apply_currency_by_polylang_or_wpml( $apply_currency, $currencies_by_languages, $selected_language, $converted_currency );

		return $apply_currency;
	}

	public function get_wpml_data( $data ) {
		$list_current_wpml_languages   = function_exists( 'icl_get_languages' ) ? icl_get_languages() : array();
		$converted_list_wpml_languages = array();
		$currencies_by_languages       = get_option( 'yay_currency_currencies_by_languages_wpml', array() );
		foreach ( $list_current_wpml_languages as $key => $value ) {
			$flag = false;
			if ( $currencies_by_languages ) {
				$converted_language = SupportHelper::get_filtered_currency_by_language( $currencies_by_languages, $key );
				if ( $converted_language ) {
					$converted_language = array_shift( $converted_language );
					$flag               = true;
				}
			}
			if ( ! $flag ) {
				$converted_language = array(
					'code'     => $key,
					'language' => $value['native_name'],
					'currency' => 'default',
				);
			}
			array_push( $converted_list_wpml_languages, $converted_language );
		}

		$data['currency_manage_tab_data']['listCurrentWpmlLanguages'] = $converted_list_wpml_languages;
		return $data;
	}

	public function get_current_currency_by_caching_with_multiple_language( $apply_currency, $converted_currency ) {
		if ( 0 !== intval( get_option( 'yay_currency_wpml_compatible', 0 ) ) ) {
			global $sitepress;
			$currencies_by_languages       = get_option( 'yay_currency_currencies_by_languages_wpml', array() );
			$selected_language             = $sitepress->get_current_language();
			$filtered_currency_by_language = SupportHelper::get_filtered_currency_by_language( $currencies_by_languages, $selected_language );
			if ( ! $filtered_currency_by_language || ! is_array( $filtered_currency_by_language ) ) {
				return $apply_currency;
			}
			$filtered_currency_by_language = array_shift( $filtered_currency_by_language );
			$currency_code                 = isset( $filtered_currency_by_language['currency'] ) ? $filtered_currency_by_language['currency'] : false;
			if ( $currency_code ) {
				$apply_currency = YayCurrencyHelper::get_currency_by_currency_code( $currency_code, $converted_currency );
			}
		}
		return $apply_currency;
	}
}
